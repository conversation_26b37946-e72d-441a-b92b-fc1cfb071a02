using System;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using NetworkManagement.Data;

namespace NetworkManagement.Services
{
    public interface IDatabaseRepairService
    {
        Task<bool> FixDeviceNumericFieldsAsync();
        Task<string> GetRepairStatusAsync();
    }

    public class DatabaseRepairService : IDatabaseRepairService
    {
        private readonly IServiceProvider _serviceProvider;

        public DatabaseRepairService(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        public async Task<bool> FixDeviceNumericFieldsAsync()
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                if (context.Devices == null)
                {
                    System.Diagnostics.Debug.WriteLine("Devices DbSet is null");
                    return false;
                }

                // إصلاح البيانات باستخدام Entity Framework
                var devices = await context.Devices.ToListAsync();
                var updatedCount = 0;

                foreach (var device in devices)
                {
                    var needsUpdate = false;

                    // إصلاح الحقول الرقمية
                    if (device.Channel.HasValue && device.Channel.Value <= 0)
                    {
                        device.Channel = null;
                        needsUpdate = true;
                    }

                    if (device.ConnectedDevices.HasValue && device.ConnectedDevices.Value < 0)
                    {
                        device.ConnectedDevices = null;
                        needsUpdate = true;
                    }

                    if (device.NetworkCableLength.HasValue && device.NetworkCableLength.Value <= 0)
                    {
                        device.NetworkCableLength = null;
                        needsUpdate = true;
                    }

                    if (device.PowerCableLength.HasValue && device.PowerCableLength.Value <= 0)
                    {
                        device.PowerCableLength = null;
                        needsUpdate = true;
                    }

                    if (needsUpdate)
                    {
                        updatedCount++;
                    }
                }

                if (updatedCount > 0)
                {
                    await context.SaveChangesAsync();
                    System.Diagnostics.Debug.WriteLine($"Fixed {updatedCount} devices");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("No corrupted data found");
                }
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error fixing database: {ex}");
                return false;
            }
        }



        public async Task<string> GetRepairStatusAsync()
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                if (context.Devices == null)
                    return "لا يمكن الوصول إلى جدول الأجهزة";

                // فحص وجود بيانات غير صحيحة بطريقة مبسطة
                var devices = await context.Devices.ToListAsync();
                var invalidCount = 0;

                foreach (var device in devices)
                {
                    try
                    {
                        // فحص الحقول الرقمية
                        if (device.Channel.HasValue && !int.TryParse(device.Channel.ToString(), out _))
                            invalidCount++;
                        if (device.ConnectedDevices.HasValue && !int.TryParse(device.ConnectedDevices.ToString(), out _))
                            invalidCount++;
                        if (device.NetworkCableLength.HasValue && !int.TryParse(device.NetworkCableLength.ToString(), out _))
                            invalidCount++;
                        if (device.PowerCableLength.HasValue && !int.TryParse(device.PowerCableLength.ToString(), out _))
                            invalidCount++;
                    }
                    catch
                    {
                        invalidCount++;
                    }
                }

                if (invalidCount > 0)
                {
                    return $"يوجد {invalidCount} جهاز يحتوي على بيانات رقمية غير صحيحة";
                }
                else
                {
                    return "جميع البيانات الرقمية صحيحة";
                }
            }
            catch (Exception ex)
            {
                return $"خطأ في فحص البيانات: {ex.Message}";
            }
        }
    }
}
