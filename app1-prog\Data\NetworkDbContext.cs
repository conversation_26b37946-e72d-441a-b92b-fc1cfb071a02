using System;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using NetworkManagement.Models;
using Microsoft.Extensions.DependencyInjection;
using NetworkManagement.ViewModels;

namespace NetworkManagement.Data
{
    public class NetworkDbContext : DbContext
    {
        public NetworkDbContext(DbContextOptions<NetworkDbContext> options) : base(options)
        {
        }

        public DbSet<Network>? Networks { get; set; }
        public DbSet<User>? Users { get; set; }
        public DbSet<Device>? Devices { get; set; }
        public DbSet<Site>? Sites { get; set; }
        public DbSet<Models.Task>? Tasks { get; set; }
        public DbSet<Purchase>? Purchases { get; set; }
        public DbSet<Inventory>? Inventory { get; set; }
        public DbSet<UserEvaluation>? UserEvaluations { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            base.OnConfiguring(optionsBuilder);
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Network configurations
            modelBuilder.Entity<Network>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.Name).IsUnique();
                entity.Property(e => e.Id).HasMaxLength(50);
                entity.Property(e => e.Name).HasMaxLength(100).IsRequired();
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.Property(e => e.Color).HasMaxLength(20);
            });

            // User configurations
            modelBuilder.Entity<User>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.Username).IsUnique();
                entity.HasIndex(e => e.Role);
                entity.HasIndex(e => e.NetworkId);

                entity.Property(e => e.Id).HasMaxLength(50);
                entity.Property(e => e.Username).HasMaxLength(50).IsRequired();
                entity.Property(e => e.Password).HasMaxLength(255).IsRequired();
                entity.Property(e => e.Name).HasMaxLength(100).IsRequired();
                entity.Property(e => e.Email).HasMaxLength(100);
                entity.Property(e => e.Role).HasMaxLength(50).IsRequired();
                entity.Property(e => e.NetworkId).HasMaxLength(50);

                entity.HasOne(e => e.Network)
                      .WithMany(n => n.Users)
                      .HasForeignKey(e => e.NetworkId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            // Device configurations
            modelBuilder.Entity<Device>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => new { e.Type, e.Status });
                entity.HasIndex(e => new { e.SiteId, e.NetworkId });
                entity.HasIndex(e => e.Ip); // إزالة الفهرس الفريد لتجنب مشاكل القيم الفارغة
                entity.HasIndex(e => e.Responsible);

                entity.Property(e => e.Id).HasMaxLength(50);
                entity.Property(e => e.NetworkId).HasMaxLength(50); // جعل NetworkId اختياري

                // تحديد قيود للحقول الرقمية لمنع القيم السالبة
                entity.HasCheckConstraint("CK_Device_Channel_Positive", "Channel IS NULL OR Channel > 0");
                entity.HasCheckConstraint("CK_Device_ConnectedDevices_NonNegative", "ConnectedDevices IS NULL OR ConnectedDevices >= 0");
                entity.HasCheckConstraint("CK_Device_NetworkCableLength_Positive", "NetworkCableLength IS NULL OR NetworkCableLength > 0");
                entity.HasCheckConstraint("CK_Device_PowerCableLength_Positive", "PowerCableLength IS NULL OR PowerCableLength > 0");

                // تجاهل خاصية IsSelected من قاعدة البيانات (UI property only)
                entity.Ignore(d => d.IsSelected);

                entity.HasOne(d => d.Site)
                      .WithMany(s => s.Devices)
                      .HasForeignKey(d => d.SiteId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(d => d.Network)
                      .WithMany(n => n.Devices)
                      .HasForeignKey(d => d.NetworkId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            // Site configurations
            modelBuilder.Entity<Site>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.Name);
                entity.HasIndex(e => e.NetworkId);

                entity.Property(e => e.Id).HasMaxLength(50);
                entity.Property(e => e.NetworkId).HasMaxLength(50); // جعل NetworkId اختياري

                entity.HasOne(s => s.Network)
                      .WithMany(n => n.Sites)
                      .HasForeignKey(s => s.NetworkId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            // Task configurations
            modelBuilder.Entity<Models.Task>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.UserId);
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.NetworkId);
                entity.HasIndex(e => e.RequestDate);
                entity.HasIndex(e => e.CompletedAt);

                entity.Property(e => e.Id).HasMaxLength(50);
                entity.Property(e => e.UserId).HasMaxLength(50).IsRequired();
                entity.Property(e => e.Description).HasMaxLength(1000).IsRequired();
                entity.Property(e => e.RequestDate).IsRequired().HasDefaultValueSql("NOW()");
                entity.Property(e => e.Status).HasMaxLength(50).HasDefaultValue("pending");
                entity.Property(e => e.Priority).HasMaxLength(50).HasDefaultValue("medium");
                entity.Property(e => e.Notes).HasColumnType("TEXT");
                entity.Property(e => e.NetworkId).HasMaxLength(50); // جعل NetworkId اختياري

                entity.HasOne(t => t.User)
                      .WithMany(u => u.Tasks)
                      .HasForeignKey(t => t.UserId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(t => t.Network)
                      .WithMany(n => n.Tasks)
                      .HasForeignKey(t => t.NetworkId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            // Purchase configurations
            modelBuilder.Entity<Purchase>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.ItemType);
                entity.HasIndex(e => e.NetworkId);
                entity.HasIndex(e => e.Date);
                entity.HasIndex(e => e.Category);

                entity.Property(e => e.Id).HasMaxLength(50);
                entity.Property(e => e.NetworkId).HasMaxLength(50); // جعل NetworkId اختياري
                entity.Property(e => e.Price).HasPrecision(18, 2);

                entity.HasOne(p => p.Network)
                      .WithMany(n => n.Purchases)
                      .HasForeignKey(p => p.NetworkId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            // Inventory configurations
            modelBuilder.Entity<Inventory>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.Name);
                entity.HasIndex(e => e.Category);
                entity.HasIndex(e => e.NetworkId);
                entity.HasIndex(e => new { e.Category, e.NetworkId });
                entity.HasIndex(e => e.Quantity);
                entity.HasIndex(e => e.LastUpdated);

                entity.Property(e => e.Id).HasMaxLength(50);
                entity.Property(e => e.NetworkId).HasMaxLength(50); // جعل NetworkId اختياري

                // Check constraints for data validation - دعم القيم السالبة للكمية
                entity.HasCheckConstraint("CK_Inventory_MinMax_Valid",
                    "MinimumStock IS NULL OR MaximumStock IS NULL OR MinimumStock <= MaximumStock");
                entity.HasCheckConstraint("CK_Inventory_UnitPrice_NonNegative",
                    "UnitPrice IS NULL OR UnitPrice >= 0");
                entity.HasCheckConstraint("CK_Inventory_MinStock_NonNegative",
                    "MinimumStock IS NULL OR MinimumStock >= 0");
                entity.HasCheckConstraint("CK_Inventory_MaxStock_NonNegative",
                    "MaximumStock IS NULL OR MaximumStock >= 0");

                entity.HasOne(i => i.Network)
                      .WithMany(n => n.Inventories)
                      .HasForeignKey(i => i.NetworkId)
                      .OnDelete(DeleteBehavior.Restrict);
            });






            // UserEvaluation configurations
            modelBuilder.Entity<UserEvaluation>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.UserId);
                entity.HasIndex(e => e.EvaluatedBy);
                entity.HasIndex(e => e.EvaluationDate);
                entity.HasIndex(e => e.NetworkId);
                entity.HasIndex(e => e.Score);
                entity.HasIndex(e => new { e.UserId, e.EvaluationDate }).IsUnique();

                entity.Property(e => e.Id).HasMaxLength(50);
                entity.Property(e => e.UserId).HasMaxLength(50);
                entity.Property(e => e.EvaluatedBy).HasMaxLength(50);
                entity.Property(e => e.NetworkId).HasMaxLength(50);

                entity.HasOne(e => e.User)
                      .WithMany()
                      .HasForeignKey(e => e.UserId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.EvaluatedByUser)
                      .WithMany()
                      .HasForeignKey(e => e.EvaluatedBy)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.Network)
                      .WithMany()
                      .HasForeignKey(e => e.NetworkId)
                      .OnDelete(DeleteBehavior.Restrict);
            });
        }

        public override int SaveChanges()
        {
            ValidateDeviceData();
            return base.SaveChanges();
        }

        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            ValidateDeviceData();
            return await base.SaveChangesAsync(cancellationToken);
        }

        private void ValidateDeviceData()
        {
            var deviceEntries = ChangeTracker.Entries<Device>()
                .Where(e => e.State == EntityState.Added || e.State == EntityState.Modified);

            foreach (var entry in deviceEntries)
            {
                var device = entry.Entity;

                // تنظيف وتصحيح الحقول الرقمية
                if (device.Channel.HasValue && device.Channel.Value <= 0)
                    device.Channel = null;

                if (device.ConnectedDevices.HasValue && device.ConnectedDevices.Value < 0)
                    device.ConnectedDevices = null;

                if (device.NetworkCableLength.HasValue && device.NetworkCableLength.Value <= 0)
                    device.NetworkCableLength = null;

                if (device.PowerCableLength.HasValue && device.PowerCableLength.Value <= 0)
                    device.PowerCableLength = null;

                // تنظيف النصوص
                if (!string.IsNullOrEmpty(device.Responsible))
                    device.Responsible = device.Responsible.Trim();

                if (!string.IsNullOrEmpty(device.Location))
                    device.Location = device.Location.Trim();

                if (!string.IsNullOrEmpty(device.Type))
                    device.Type = device.Type.Trim();

                if (!string.IsNullOrEmpty(device.Ip))
                    device.Ip = device.Ip.Trim();
            }
        }
    }
}
