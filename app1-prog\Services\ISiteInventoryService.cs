using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using NetworkManagement.Models;

namespace NetworkManagement.Services
{
    public class SiteInventoryDeductionResult
    {
        public bool Success { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public List<string> DeductedItems { get; set; } = new();
        public List<string> InsufficientItems { get; set; } = new();
    }

    public class SiteInventoryCheckResult
    {
        public bool HasSufficientStock { get; set; }
        public List<string> InsufficientItems { get; set; } = new();
        public Dictionary<string, int> RequiredQuantities { get; set; } = new();
        public Dictionary<string, int> AvailableQuantities { get; set; } = new();
    }

    public interface ISiteInventoryService
    {
        /// <summary>
        /// التحقق من توفر الكمية المطلوبة في المخزون لموقع معين
        /// </summary>
        Task<SiteInventoryCheckResult> CheckInventoryAvailabilityAsync(Site site, string? networkId = null);

        /// <summary>
        /// خصم الكميات المطلوبة من المخزون عند إضافة موقع جديد
        /// </summary>
        Task<SiteInventoryDeductionResult> DeductInventoryForNewSiteAsync(Site site, string? networkId = null);

        /// <summary>
        /// تحديث المخزون عند تعديل موقع موجود
        /// </summary>
        Task<SiteInventoryDeductionResult> UpdateInventoryForSiteModificationAsync(Site oldSite, Site newSite, string? networkId = null);

        /// <summary>
        /// إرجاع الكميات إلى المخزون عند حذف موقع
        /// </summary>
        Task<SiteInventoryDeductionResult> RestoreInventoryForDeletedSiteAsync(Site site, string? networkId = null);

        /// <summary>
        /// الحصول على قائمة بأنواع البطاريات المتاحة في المخزون
        /// </summary>
        Task<List<string>> GetAvailableBatteryTypesAsync(string? networkId = null);

        /// <summary>
        /// الحصول على قائمة بأنواع القواعد المتاحة في المخزون
        /// </summary>
        Task<List<string>> GetAvailableBaseTypesAsync(string? networkId = null);

        /// <summary>
        /// الحصول على قائمة بأنواع الصناديق المتاحة في المخزون
        /// </summary>
        Task<List<string>> GetAvailableBoxTypesAsync(string? networkId = null);

        // إزالة دوال أسلاك الشبكة والكهرباء - لم تعد مطلوبة

        /// <summary>
        /// الحصول على قائمة بجميع عناصر المخزون المتاحة
        /// </summary>
        Task<List<Inventory>> GetAvailableInventoryItemsAsync(string? networkId = null);
    }
}
