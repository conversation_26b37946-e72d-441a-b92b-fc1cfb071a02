using System;
using System.Threading.Tasks;
using System.Windows;
using NetworkManagement.Services;

namespace NetworkManagement.Helpers
{
    /// <summary>
    /// فئة مساعدة لإدارة الرسائل وإزالة التكرار
    /// </summary>
    public static class MessageHelper
    {
        // خدمة الإشعارات الثابتة للاستخدام في الدوال الثابتة
        private static INotificationService? _notificationService;
        private static IAuthService? _authService;

        /// <summary>
        /// تهيئة MessageHelper مع خدمات الإشعارات والمصادقة
        /// </summary>
        public static void Initialize(INotificationService notificationService, IAuthService authService)
        {
            _notificationService = notificationService;
            _authService = authService;
        }
        /// <summary>
        /// عرض رسالة نجاح موحدة
        /// </summary>
        /// <param name="action">نوع العملية (إضافة، تحديث، حذف)</param>
        /// <param name="itemType">نوع العنصر (جهاز، موقع، مستخدم)</param>
        /// <param name="itemName">اسم العنصر</param>
        /// <param name="additionalInfo">معلومات إضافية اختيارية</param>
        public static void ShowSuccessMessage(string action, string itemType, string itemName, string? additionalInfo = null)
        {
            var message = $"✅ تم {action} {itemType} بنجاح!";
            
            if (!string.IsNullOrEmpty(itemName))
            {
                message += $"\n\n{itemName}";
            }
            
            if (!string.IsNullOrEmpty(additionalInfo))
            {
                message += $"\n\n{additionalInfo}";
            }

            MessageBox.Show(message, $"تم {action}", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// عرض رسالة خطأ موحدة
        /// </summary>
        /// <param name="action">نوع العملية</param>
        /// <param name="error">رسالة الخطأ</param>
        /// <param name="details">تفاصيل إضافية</param>
        public static void ShowErrorMessage(string action, string error, string? details = null)
        {
            var message = $"حدث خطأ أثناء {action}:\n{error}";
            
            if (!string.IsNullOrEmpty(details))
            {
                message += $"\n\nتفاصيل إضافية:\n{details}";
            }

            MessageBox.Show(message, $"خطأ في {action}", MessageBoxButton.OK, MessageBoxImage.Error);
        }

        /// <summary>
        /// عرض رسالة تأكيد موحدة للحذف
        /// </summary>
        /// <param name="itemType">نوع العنصر</param>
        /// <param name="itemName">اسم العنصر</param>
        /// <param name="additionalWarning">تحذير إضافي</param>
        /// <returns>true إذا تم التأكيد</returns>
        public static bool ShowDeleteConfirmation(string itemType, string itemName, string? additionalWarning = null)
        {
            var message = $"هل أنت متأكد من حذف {itemType}:\n\n{itemName}";
            
            if (!string.IsNullOrEmpty(additionalWarning))
            {
                message += $"\n\n{additionalWarning}";
            }
            
            message += "\n\n⚠️ تحذير: لا يمكن التراجع عن هذا الإجراء!";

            var result = MessageBox.Show(message, $"تأكيد حذف {itemType}", 
                MessageBoxButton.YesNo, MessageBoxImage.Warning, MessageBoxResult.No);
            
            return result == MessageBoxResult.Yes;
        }

        /// <summary>
        /// عرض رسالة تأكيد للحذف المتعدد
        /// </summary>
        /// <param name="itemType">نوع العناصر</param>
        /// <param name="count">عدد العناصر</param>
        /// <param name="itemNames">أسماء العناصر (أول 5)</param>
        /// <returns>true إذا تم التأكيد</returns>
        public static bool ShowMultipleDeleteConfirmation(string itemType, int count, string[] itemNames)
        {
            var message = $"هل أنت متأكد من حذف {count} {itemType}؟\n\n";
            
            // عرض أول 5 عناصر
            var displayCount = Math.Min(5, itemNames.Length);
            for (int i = 0; i < displayCount; i++)
            {
                message += $"• {itemNames[i]}\n";
            }
            
            if (count > 5)
            {
                message += $"... و {count - 5} عناصر أخرى\n";
            }
            
            message += "\n⚠️ تحذير: لا يمكن التراجع عن هذا الإجراء!";

            var result = MessageBox.Show(message, $"تأكيد حذف {itemType}", 
                MessageBoxButton.YesNo, MessageBoxImage.Warning, MessageBoxResult.No);
            
            return result == MessageBoxResult.Yes;
        }

        /// <summary>
        /// عرض رسالة تحذير موحدة
        /// </summary>
        /// <param name="message">نص الرسالة</param>
        /// <param name="title">عنوان الرسالة</param>
        public static void ShowWarningMessage(string message, string title = "تنبيه")
        {
            MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Warning);
        }

        /// <summary>
        /// عرض رسالة معلومات موحدة
        /// </summary>
        /// <param name="message">نص الرسالة</param>
        /// <param name="title">عنوان الرسالة</param>
        public static void ShowInfoMessage(string message, string title = "معلومات")
        {
            MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// عرض رسالة تأكيد عامة
        /// </summary>
        /// <param name="message">نص الرسالة</param>
        /// <param name="title">عنوان الرسالة</param>
        /// <returns>true إذا تم التأكيد</returns>
        public static bool ShowConfirmation(string message, string title = "تأكيد")
        {
            var result = MessageBox.Show(message, title, MessageBoxButton.YesNo, 
                MessageBoxImage.Question, MessageBoxResult.No);
            return result == MessageBoxResult.Yes;
        }

        /// <summary>
        /// معالجة الأخطاء وعرضها بشكل موحد
        /// </summary>
        /// <param name="ex">الاستثناء</param>
        /// <param name="action">نوع العملية</param>
        /// <param name="showDetails">عرض التفاصيل التقنية</param>
        public static void HandleException(Exception ex, string action, bool showDetails = false)
        {
            var details = showDetails ? ex.ToString() : ex.InnerException?.Message;
            ShowErrorMessage(action, ex.Message, details);
            
            // تسجيل الخطأ للتشخيص
            System.Diagnostics.Debug.WriteLine($"Error in {action}: {ex}");
        }

        /// <summary>
        /// عرض رسالة نتائج العملية (نجاح + أخطاء)
        /// </summary>
        /// <param name="action">نوع العملية</param>
        /// <param name="successCount">عدد العناصر الناجحة</param>
        /// <param name="errorCount">عدد الأخطاء</param>
        /// <param name="errors">قائمة الأخطاء</param>
        public static void ShowOperationResults(string action, int successCount, int errorCount, string[]? errors = null)
        {
            var message = $"تم {action} {successCount} عنصر بنجاح";
            
            if (errorCount > 0)
            {
                message += $"\n\nفشل في {action} {errorCount} عنصر";
                
                if (errors != null && errors.Length > 0)
                {
                    var displayCount = Math.Min(3, errors.Length);
                    message += ":\n";
                    
                    for (int i = 0; i < displayCount; i++)
                    {
                        message += $"• {errors[i]}\n";
                    }
                    
                    if (errors.Length > 3)
                    {
                        message += $"... و {errors.Length - 3} أخطاء أخرى";
                    }
                }
            }

            var icon = errorCount == 0 ? MessageBoxImage.Information : MessageBoxImage.Warning;
            MessageBox.Show(message, $"نتائج {action}", MessageBoxButton.OK, icon);
        }

        /// <summary>
        /// تنسيق معلومات الجهاز للعرض في الرسائل
        /// </summary>
        /// <param name="responsible">المسؤول</param>
        /// <param name="type">النوع</param>
        /// <param name="location">الموقع</param>
        /// <param name="ip">عنوان IP</param>
        /// <returns>نص منسق</returns>
        public static string FormatDeviceInfo(string? responsible, string? type, string? location, string? ip)
        {
            var info = "";
            
            if (!string.IsNullOrEmpty(responsible))
                info += $"المسؤول: {responsible}\n";
            
            if (!string.IsNullOrEmpty(type))
                info += $"النوع: {type}\n";
            
            if (!string.IsNullOrEmpty(location))
                info += $"الموقع: {location}\n";
            
            if (!string.IsNullOrEmpty(ip))
                info += $"عنوان IP: {ip}";

            return info.TrimEnd('\n');
        }

        // ===== دوال الإشعارات الجديدة =====

        /// <summary>
        /// عرض إشعار نجاح العملية مع معلومات المستخدم
        /// </summary>
        public static async Task ShowOperationSuccessAsync(string action, string itemType, string itemName, string? networkId = null)
        {
            if (_notificationService == null || _authService == null)
            {
                ShowSuccessMessage(action, itemType, itemName);
                return;
            }

            var userName = _authService.CurrentUser?.Name ?? "المستخدم";
            var userId = _authService.CurrentUser?.Id;

            await _notificationService.ShowOperationAsync(
                $"تم {action} {itemType}",
                $"قام {userName} بـ{action} {itemType}: {itemName}",
                NotificationType.Success,
                userName, userId, action, itemName, networkId);
        }

        /// <summary>
        /// عرض إشعار خطأ العملية مع معلومات المستخدم
        /// </summary>
        public static async Task ShowOperationErrorAsync(string action, string error, string? networkId = null)
        {
            if (_notificationService == null || _authService == null)
            {
                ShowErrorMessage(action, error);
                return;
            }

            var userName = _authService.CurrentUser?.Name ?? "المستخدم";
            var userId = _authService.CurrentUser?.Id;

            await _notificationService.ShowOperationAsync(
                $"خطأ في {action}",
                $"فشل {userName} في {action}: {error}",
                NotificationType.Error,
                userName, userId, action, null, networkId);
        }

        /// <summary>
        /// عرض إشعار تحذير العملية مع معلومات المستخدم
        /// </summary>
        public static async Task ShowOperationWarningAsync(string action, string warning, string? networkId = null)
        {
            if (_notificationService == null || _authService == null)
            {
                ShowWarningMessage(warning);
                return;
            }

            var userName = _authService.CurrentUser?.Name ?? "المستخدم";
            var userId = _authService.CurrentUser?.Id;

            await _notificationService.ShowOperationAsync(
                $"تحذير في {action}",
                $"{userName}: {warning}",
                NotificationType.Warning,
                userName, userId, action, null, networkId);
        }

        /// <summary>
        /// عرض إشعار معلومات العملية مع معلومات المستخدم
        /// </summary>
        public static async Task ShowOperationInfoAsync(string action, string info, string? networkId = null)
        {
            if (_notificationService == null || _authService == null)
            {
                ShowInfoMessage(info);
                return;
            }

            var userName = _authService.CurrentUser?.Name ?? "المستخدم";
            var userId = _authService.CurrentUser?.Id;

            await _notificationService.ShowOperationAsync(
                $"معلومات {action}",
                $"{userName}: {info}",
                NotificationType.Info,
                userName, userId, action, null, networkId);
        }
    }
}
