using System.Text.RegularExpressions;
using System.Windows.Controls;
using System.Windows.Input;

namespace NetworkManagement.Views
{
    /// <summary>
    /// Interaction logic for DatabaseSetupView.xaml
    /// </summary>
    public partial class DatabaseSetupView : UserControl
    {
        public DatabaseSetupView()
        {
            InitializeComponent();
        }

        private void NumericTextBox_PreviewTextInput(object sender, TextCompositionEventArgs e)
        {
            // السماح بالأرقام فقط
            var regex = new Regex("[^0-9]+");
            e.Handled = regex.IsMatch(e.Text);
        }
    }
}
