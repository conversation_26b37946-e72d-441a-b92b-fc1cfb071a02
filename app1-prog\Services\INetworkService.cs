using System.Collections.Generic;
using System.Threading.Tasks;
using NetworkManagement.Models;
using Task = System.Threading.Tasks.Task;

namespace NetworkManagement.Services
{
    public interface INetworkService
    {
        Task<IEnumerable<Network>> GetAllAsync();
        Task<Network?> GetByIdAsync(string id);
        Task<Network> CreateAsync(Network network);
        Task<Network> UpdateAsync(Network network);
        Task DeleteAsync(string id);
        Task<bool> ExistsAsync(string id);
        Task<IEnumerable<Network>> GetActiveNetworksAsync();
    }
}
