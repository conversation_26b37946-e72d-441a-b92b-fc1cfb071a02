using System;
using System.Threading.Tasks;
using System.Windows;
using NetworkManagement.Services;

namespace NetworkManagement.Helpers
{
    /// <summary>
    /// فئة مساعدة لمعالجة الأخطاء بشكل موحد
    /// </summary>
    public static class ErrorHandler
    {
        // خدمة الإشعارات الثابتة للاستخدام في الدوال الثابتة
        private static INotificationService? _notificationService;
        private static IAuthService? _authService;

        /// <summary>
        /// تهيئة ErrorHandler مع خدمات الإشعارات والمصادقة
        /// </summary>
        public static void Initialize(INotificationService notificationService, IAuthService authService)
        {
            _notificationService = notificationService;
            _authService = authService;
        }
        /// <summary>
        /// أنواع الأخطاء
        /// </summary>
        public enum ErrorType
        {
            Database,
            Network,
            FileSystem,
            Validation,
            Permission,
            Unknown
        }

        /// <summary>
        /// معالجة الاستثناء وعرض رسالة مناسبة للمستخدم
        /// </summary>
        /// <param name="exception">الاستثناء</param>
        /// <param name="operation">اسم العملية التي فشلت</param>
        /// <param name="source">مصدر الخطأ</param>
        /// <param name="showToUser">هل يتم عرض الرسالة للمستخدم</param>
        /// <returns>نوع الخطأ المحدد</returns>
        public static ErrorType HandleException(Exception exception, string operation, string? source = null, bool showToUser = true)
        {
            var errorType = DetermineErrorType(exception);
            var userMessage = GetUserFriendlyMessage(exception, operation, errorType);
            
            // تسجيل الخطأ
            LoggingHelper.LogError($"خطأ في العملية '{operation}': {exception.Message}", exception, source);
            
            // عرض الرسالة للمستخدم إذا كان مطلوباً
            if (showToUser)
            {
                ShowErrorToUser(userMessage, operation, errorType);
            }
            
            return errorType;
        }

        /// <summary>
        /// معالجة الاستثناء بشكل غير متزامن
        /// </summary>
        public static async Task<ErrorType> HandleExceptionAsync(Exception exception, string operation, string? source = null, bool showToUser = true)
        {
            return await Task.Run(() => HandleException(exception, operation, source, showToUser));
        }

        /// <summary>
        /// تنفيذ عملية مع معالجة الأخطاء
        /// </summary>
        /// <param name="action">العملية المراد تنفيذها</param>
        /// <param name="operationName">اسم العملية</param>
        /// <param name="source">مصدر العملية</param>
        /// <param name="showErrorToUser">هل يتم عرض الأخطاء للمستخدم</param>
        /// <returns>true إذا نجحت العملية، false إذا فشلت</returns>
        public static bool ExecuteWithErrorHandling(Action action, string operationName, string? source = null, bool showErrorToUser = true)
        {
            try
            {
                LoggingHelper.LogOperationStart(operationName, source);
                var startTime = DateTime.Now;
                
                action();
                
                var duration = DateTime.Now - startTime;
                LoggingHelper.LogOperationSuccess(operationName, source);
                LoggingHelper.LogPerformance(operationName, duration, source);
                
                return true;
            }
            catch (Exception ex)
            {
                HandleException(ex, operationName, source, showErrorToUser);
                return false;
            }
        }

        /// <summary>
        /// تنفيذ عملية غير متزامنة مع معالجة الأخطاء
        /// </summary>
        public static async Task<bool> ExecuteWithErrorHandlingAsync(Func<Task> action, string operationName, string? source = null, bool showErrorToUser = true)
        {
            try
            {
                LoggingHelper.LogOperationStart(operationName, source);
                var startTime = DateTime.Now;
                
                await action();
                
                var duration = DateTime.Now - startTime;
                LoggingHelper.LogOperationSuccess(operationName, source);
                LoggingHelper.LogPerformance(operationName, duration, source);
                
                return true;
            }
            catch (Exception ex)
            {
                HandleException(ex, operationName, source, showErrorToUser);
                return false;
            }
        }

        /// <summary>
        /// تنفيذ عملية مع إرجاع نتيجة ومعالجة الأخطاء
        /// </summary>
        public static T? ExecuteWithErrorHandling<T>(Func<T> func, string operationName, string? source = null, bool showErrorToUser = true, T? defaultValue = default)
        {
            try
            {
                LoggingHelper.LogOperationStart(operationName, source);
                var startTime = DateTime.Now;
                
                var result = func();
                
                var duration = DateTime.Now - startTime;
                LoggingHelper.LogOperationSuccess(operationName, source);
                LoggingHelper.LogPerformance(operationName, duration, source);
                
                return result;
            }
            catch (Exception ex)
            {
                HandleException(ex, operationName, source, showErrorToUser);
                return defaultValue;
            }
        }

        /// <summary>
        /// تنفيذ عملية غير متزامنة مع إرجاع نتيجة ومعالجة الأخطاء
        /// </summary>
        public static async Task<T?> ExecuteWithErrorHandlingAsync<T>(Func<Task<T>> func, string operationName, string? source = null, bool showErrorToUser = true, T? defaultValue = default)
        {
            try
            {
                LoggingHelper.LogOperationStart(operationName, source);
                var startTime = DateTime.Now;
                
                var result = await func();
                
                var duration = DateTime.Now - startTime;
                LoggingHelper.LogOperationSuccess(operationName, source);
                LoggingHelper.LogPerformance(operationName, duration, source);
                
                return result;
            }
            catch (Exception ex)
            {
                HandleException(ex, operationName, source, showErrorToUser);
                return defaultValue;
            }
        }

        private static ErrorType DetermineErrorType(Exception exception)
        {
            return exception switch
            {
                Microsoft.EntityFrameworkCore.DbUpdateException => ErrorType.Database,
                System.Data.Common.DbException => ErrorType.Database,
                System.Net.Http.HttpRequestException => ErrorType.Network,
                System.Net.NetworkInformation.NetworkInformationException => ErrorType.Network,
                System.IO.DirectoryNotFoundException => ErrorType.FileSystem,
                System.IO.FileNotFoundException => ErrorType.FileSystem,
                System.IO.IOException => ErrorType.FileSystem,
                System.Security.SecurityException => ErrorType.Permission,
                System.UnauthorizedAccessException => ErrorType.Permission,
                ArgumentNullException => ErrorType.Validation,
                ArgumentException => ErrorType.Validation,
                InvalidOperationException => ErrorType.Validation,
                _ => ErrorType.Unknown
            };
        }

        private static string GetUserFriendlyMessage(Exception exception, string operation, ErrorType errorType)
        {
            var baseMessage = $"حدث خطأ أثناء {operation}";
            
            return errorType switch
            {
                ErrorType.Database => $"{baseMessage}\n\nخطأ في قاعدة البيانات. يرجى التحقق من الاتصال والمحاولة مرة أخرى.",
                ErrorType.Network => $"{baseMessage}\n\nخطأ في الشبكة. يرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى.",
                ErrorType.FileSystem => $"{baseMessage}\n\nخطأ في الوصول للملف. يرجى التحقق من الصلاحيات ووجود الملف.",
                ErrorType.Permission => $"{baseMessage}\n\nليس لديك صلاحية كافية لتنفيذ هذه العملية.",
                ErrorType.Validation => $"{baseMessage}\n\nبيانات غير صحيحة. يرجى التحقق من البيانات المدخلة.",
                ErrorType.Unknown => $"{baseMessage}\n\nخطأ غير متوقع. يرجى المحاولة مرة أخرى أو الاتصال بالدعم الفني.",
                _ => $"{baseMessage}\n\n{exception.Message}"
            };
        }

        private static void ShowErrorToUser(string message, string operation, ErrorType errorType)
        {
            var icon = errorType switch
            {
                ErrorType.Permission => MessageBoxImage.Warning,
                ErrorType.Validation => MessageBoxImage.Warning,
                _ => MessageBoxImage.Error
            };

            var title = errorType switch
            {
                ErrorType.Database => "خطأ في قاعدة البيانات",
                ErrorType.Network => "خطأ في الشبكة",
                ErrorType.FileSystem => "خطأ في الملف",
                ErrorType.Permission => "خطأ في الصلاحيات",
                ErrorType.Validation => "خطأ في البيانات",
                _ => "خطأ"
            };

            try
            {
                if (Application.Current?.Dispatcher?.CheckAccess() == true)
                {
                    MessageBox.Show(message, title, MessageBoxButton.OK, icon);
                }
                else
                {
                    Application.Current?.Dispatcher?.Invoke(() =>
                    {
                        MessageBox.Show(message, title, MessageBoxButton.OK, icon);
                    });
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LogError("فشل في عرض رسالة الخطأ للمستخدم", ex, "ErrorHandler");
            }
        }

        /// <summary>
        /// تسجيل تحذير بدون عرض رسالة للمستخدم
        /// </summary>
        public static void LogWarning(string message, string? source = null)
        {
            LoggingHelper.LogWarning(message, null, source);
        }

        /// <summary>
        /// تسجيل معلومات
        /// </summary>
        public static void LogInfo(string message, string? source = null)
        {
            LoggingHelper.LogInfo(message, source);
        }

        // ===== دوال الإشعارات الجديدة =====

        /// <summary>
        /// معالجة الاستثناء مع إشعار متقدم
        /// </summary>
        public static async Task<ErrorType> HandleExceptionWithNotificationAsync(Exception exception, string operation, string? source = null, string? networkId = null)
        {
            var errorType = DetermineErrorType(exception);
            var userMessage = GetUserFriendlyMessage(exception, operation, errorType);

            // تسجيل الخطأ
            LoggingHelper.LogError($"خطأ في العملية '{operation}': {exception.Message}", exception, source);

            // عرض إشعار الخطأ
            if (_notificationService != null && _authService != null)
            {
                var userName = _authService.CurrentUser?.Name ?? "المستخدم";
                var userId = _authService.CurrentUser?.Id;

                await _notificationService.ShowOperationAsync(
                    $"خطأ في {operation}",
                    $"فشل {userName} في {operation}: {userMessage}",
                    NotificationType.Error,
                    userName, userId, operation, null, networkId);
            }
            else
            {
                // العودة للطريقة التقليدية إذا لم تكن الخدمات متاحة
                ShowErrorToUser(userMessage, operation, errorType);
            }

            return errorType;
        }

        /// <summary>
        /// عرض تحذير مع إشعار متقدم
        /// </summary>
        public static async Task ShowWarningWithNotificationAsync(string operation, string warning, string? source = null, string? networkId = null)
        {
            // تسجيل التحذير
            LoggingHelper.LogWarning($"تحذير في العملية '{operation}': {warning}", null, source);

            // عرض إشعار التحذير
            if (_notificationService != null && _authService != null)
            {
                var userName = _authService.CurrentUser?.Name ?? "المستخدم";
                var userId = _authService.CurrentUser?.Id;

                await _notificationService.ShowOperationAsync(
                    $"تحذير في {operation}",
                    $"{userName}: {warning}",
                    NotificationType.Warning,
                    userName, userId, operation, null, networkId);
            }
            else
            {
                // العودة للطريقة التقليدية
                MessageBox.Show(warning, $"تحذير في {operation}", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }
    }
}
