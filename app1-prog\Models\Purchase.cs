using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NetworkManagement.Models
{
    public class Purchase
    {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        [Required]
        [MaxLength(100)]
        public string ItemType { get; set; } = string.Empty;

        [Required]
        public decimal Price { get; set; }

        [Required]
        public DateTime Date { get; set; } = DateTime.Now;

        [Required]
        public int Quantity { get; set; } = 1;

        [MaxLength(20)]
        public string? Unit { get; set; }

        [MaxLength(200)]
        public string? Supplier { get; set; }

        [MaxLength(500)]
        public string? Description { get; set; }

        [MaxLength(50)]
        public string? InvoiceNumber { get; set; }

        [MaxLength(20)]
        public string? Category { get; set; }

        // Network relationship
        public string? NetworkId { get; set; }
        public virtual Network? Network { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        // Display properties for Yemeni Rial
        [NotMapped]
        public string PriceDisplay => $"{Price:N0} ر.ي";

        [NotMapped]
        public decimal TotalPrice => Price * Quantity;

        [NotMapped]
        public string TotalPriceDisplay => $"{TotalPrice:N0} ر.ي";

        [NotMapped]
        public string DateDisplay => Date.ToString("dd/MM/yyyy");

        [NotMapped]
        public string QuantityDisplay => $"{Quantity} {Unit ?? "قطعة"}";

        [NotMapped]
        public string NetworkName => Network?.Name ?? "غير محدد";

        [NotMapped]
        public string CategoryDisplay => Category ?? "غير محدد";

        [NotMapped]
        public string SupplierDisplay => Supplier ?? "غير محدد";
    }
}
