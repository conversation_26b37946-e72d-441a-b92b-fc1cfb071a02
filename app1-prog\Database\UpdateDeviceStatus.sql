-- تحديث حالات الأجهزة إلى النظام الجديد
-- يجب تشغيل هذا السكريبت مرة واحدة فقط بعد التحديث

-- تحديث الحالات القديمة إلى الحالات الجديدة
UPDATE Devices 
SET Status = CASE 
    WHEN Status = 'active' OR Status = 'نشط' THEN 'يعمل'
    WHEN Status = 'inactive' OR Status = 'غير نشط' THEN 'عاطل'
    WHEN Status = 'maintenance' OR Status = 'صيانة' THEN 'عاطل'
    WHEN Status = 'disabled' OR Status = 'معطل' THEN 'عاطل'
    WHEN Status = 'متصل' THEN 'متصل'
    WHEN Status = 'غير متصل' THEN 'غير متصل'
    ELSE 'يعمل'
END
WHERE Status IS NOT NULL;

-- تحديث الحالات الفارغة أو NULL إلى الحالة الافتراضية
UPDATE Devices 
SET Status = 'يعمل'
WHERE Status IS NULL OR Status = '';

-- عرض إحصائيات الحالات بعد التحديث
SELECT 
    Status,
    COUNT(*) as DeviceCount
FROM Devices 
GROUP BY Status
ORDER BY DeviceCount DESC;

PRINT 'تم تحديث حالات الأجهزة بنجاح إلى النظام الجديد';
PRINT 'الحالات المتاحة الآن: يعمل، عاطل، مسحوب، متصل، غير متصل';
