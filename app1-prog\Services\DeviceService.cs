using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using NetworkManagement.Data;
using NetworkManagement.Models;
using Task = System.Threading.Tasks.Task;

namespace NetworkManagement.Services
{
    public class DeviceService : IDeviceService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ICacheService _cacheService;

        public DeviceService(IServiceProvider serviceProvider, ICacheService cacheService)
        {
            _serviceProvider = serviceProvider;
            _cacheService = cacheService;
        }

        public async Task<IEnumerable<Device>> GetAllAsync(string? networkFilter = null)
        {
            // استخدام التخزين المؤقت
            var cacheKey = CacheKeys.GetNetworkKey(CacheKeys.DevicesByNetwork, networkFilter);

            return await _cacheService.GetOrCreateAsync(cacheKey, async () =>
            {
                var scope = _serviceProvider.CreateScope();
                try
                {
                    var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                    if (context.Devices == null)
                        throw new InvalidOperationException("DbSet<Device> is null");

                    var query = context.Devices.Include(d => d.Site).Include(d => d.Network).AsQueryable();

                    if (!string.IsNullOrEmpty(networkFilter))
                    {
                        query = query.Where(d => d.NetworkId == networkFilter);
                    }

                    return await query.OrderBy(d => d.Location).ToListAsync().ConfigureAwait(false);
                }
                finally
                {
                    scope.Dispose();
                }
            }, TimeSpan.FromMinutes(10)); // تخزين لمدة 10 دقائق
        }

        public async Task<Device?> GetByIdAsync(string id)
        {
            var scope = _serviceProvider.CreateScope();
            try
            {
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                if (context.Devices == null)
                    return null;

                return await context.Devices
                    .Include(d => d.Site)
                    .Include(d => d.Network)
                    .FirstOrDefaultAsync(d => d.Id == id)
                    .ConfigureAwait(false);
            }
            finally
            {
                scope.Dispose();
            }
        }

        public async Task<Device> CreateAsync(Device device)
        {
            var scope = _serviceProvider.CreateScope();
            try
            {
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                if (context.Devices == null)
                    throw new InvalidOperationException("DbSet<Device> is null");



                device.Id = Guid.NewGuid().ToString();
                device.CreatedAt = DateTime.Now;
                device.UpdatedAt = DateTime.Now;

                context.Devices.Add(device);
                await context.SaveChangesAsync().ConfigureAwait(false);

                // خصم الكميات من المخزون (دعم القيم السالبة)
                try
                {
                    var deviceInventoryService = scope.ServiceProvider.GetRequiredService<IDeviceInventoryService>();
                    await deviceInventoryService.DeductInventoryForNewDeviceAsync(device, device.NetworkId);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"تحذير: فشل في تحديث المخزون: {ex.Message}");
                    // لا نمنع إضافة الجهاز حتى لو فشل تحديث المخزون
                }

                // مسح الذاكرة المؤقتة للأجهزة
                ClearDeviceCache(device.NetworkId);

                return device;
            }
            finally
            {
                scope.Dispose();
            }
        }

        public async Task<Device> UpdateAsync(Device device)
        {
            var scope = _serviceProvider.CreateScope();
            try
            {
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                if (context.Devices == null)
                    throw new InvalidOperationException("DbSet<Device> is null");

                // الحصول على الجهاز القديم
                var oldDevice = await context.Devices.AsNoTracking().FirstOrDefaultAsync(d => d.Id == device.Id);
                if (oldDevice == null)
                    throw new ArgumentException("الجهاز غير موجود", nameof(device));

                // تحديث المخزون بناءً على التغييرات (دعم القيم السالبة)
                try
                {
                    var deviceInventoryService = scope.ServiceProvider.GetRequiredService<IDeviceInventoryService>();
                    await deviceInventoryService.UpdateInventoryForDeviceModificationAsync(oldDevice, device, device.NetworkId);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"تحذير: فشل في تحديث المخزون: {ex.Message}");
                    // لا نمنع تحديث الجهاز حتى لو فشل تحديث المخزون
                }

                device.UpdatedAt = DateTime.Now;
                context.Devices.Update(device);
                await context.SaveChangesAsync().ConfigureAwait(false);

                // مسح الذاكرة المؤقتة للأجهزة
                ClearDeviceCache(device.NetworkId);

                return device;
            }
            finally
            {
                scope.Dispose();
            }
        }

        public async Task<bool> DeleteAsync(string id)
        {
            var scope = _serviceProvider.CreateScope();
            try
            {
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                if (context.Devices == null)
                    throw new InvalidOperationException("DbSet<Device> is null");

                var device = await context.Devices.FindAsync(id).ConfigureAwait(false);
                if (device == null) return false;

                var networkId = device.NetworkId; // حفظ معرف الشبكة قبل الحذف

                // إرجاع الكميات إلى المخزون (دعم القيم السالبة)
                try
                {
                    var deviceInventoryService = scope.ServiceProvider.GetRequiredService<IDeviceInventoryService>();
                    await deviceInventoryService.RestoreInventoryForDeletedDeviceAsync(device, networkId);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"تحذير: فشل في إرجاع المخزون: {ex.Message}");
                    // لا نمنع حذف الجهاز حتى لو فشل تحديث المخزون
                }

                context.Devices.Remove(device);
                await context.SaveChangesAsync().ConfigureAwait(false);

                // مسح الذاكرة المؤقتة للأجهزة
                ClearDeviceCache(networkId);

                return true;
            }
            finally
            {
                scope.Dispose();
            }
        }

        public async Task<IEnumerable<Device>> SearchAsync(string searchTerm, string? networkFilter = null)
        {
            var scope = _serviceProvider.CreateScope();
            try
            {
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                if (context.Devices == null)
                    throw new InvalidOperationException("DbSet<Device> is null");

                var query = context.Devices.Include(d => d.Site).Include(d => d.Network).AsQueryable();

                if (!string.IsNullOrEmpty(networkFilter))
                {
                    query = query.Where(d => d.NetworkId == networkFilter);
                }

                if (!string.IsNullOrEmpty(searchTerm))
                {
                    query = query.Where(d =>
                        (d.Location ?? "").Contains(searchTerm) ||
                        (d.Type ?? "").Contains(searchTerm) ||
                        (d.Ip ?? "").Contains(searchTerm) ||
                        (d.Responsible ?? "").Contains(searchTerm));
                }

                return await query.OrderBy(d => d.Location).ToListAsync().ConfigureAwait(false);
            }
            finally
            {
                scope.Dispose();
            }
        }

        public async Task<IEnumerable<Device>> GetBySiteIdAsync(string siteId)
        {
            var scope = _serviceProvider.CreateScope();
            try
            {
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                if (context.Devices == null)
                    throw new InvalidOperationException("DbSet<Device> is null");

                return await context.Devices
                    .Where(d => d.SiteId == siteId)
                    .OrderBy(d => d.Location)
                    .ToListAsync()
                    .ConfigureAwait(false);
            }
            finally
            {
                scope.Dispose();
            }
        }

        public async Task<IEnumerable<Device>> GetByStatusAsync(string status, string? networkFilter = null)
        {
            var scope = _serviceProvider.CreateScope();
            try
            {
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                if (context.Devices == null)
                    throw new InvalidOperationException("DbSet<Device> is null");

                var query = context.Devices.Include(d => d.Site).Include(d => d.Network).AsQueryable();

                if (!string.IsNullOrEmpty(networkFilter))
                {
                    query = query.Where(d => d.NetworkId == networkFilter);
                }

                query = query.Where(d => d.Status == status);

                return await query.OrderBy(d => d.Location).ToListAsync().ConfigureAwait(false);
            }
            finally
            {
                scope.Dispose();
            }
        }

        public async Task<IEnumerable<Device>> GetDevicesWithIpAsync()
        {
            var scope = _serviceProvider.CreateScope();
            try
            {
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                if (context.Devices == null)
                    throw new InvalidOperationException("DbSet<Device> is null");

                return await context.Devices
                    .Include(d => d.Site)
                    .Where(d => !string.IsNullOrEmpty(d.Ip))
                    .OrderBy(d => d.Location)
                    .ToListAsync()
                    .ConfigureAwait(false);
            }
            finally
            {
                scope.Dispose();
            }
        }

        public async Task<Device?> GetDeviceByIpAsync(string ipAddress)
        {
            var scope = _serviceProvider.CreateScope();
            try
            {
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                if (context.Devices == null)
                    throw new InvalidOperationException("DbSet<Device> is null");

                return await context.Devices
                    .Include(d => d.Site)
                    .Include(d => d.Network)
                    .FirstOrDefaultAsync(d => d.Ip == ipAddress.Trim())
                    .ConfigureAwait(false);
            }
            finally
            {
                scope.Dispose();
            }
        }

        public async Task<Device> UpdateDeviceStatusAsync(string deviceId, string status)
        {
            var scope = _serviceProvider.CreateScope();
            try
            {
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                if (context.Devices == null)
                    throw new InvalidOperationException("DbSet<Device> is null");

                var device = await context.Devices.FindAsync(deviceId).ConfigureAwait(false);
                if (device == null)
                    throw new ArgumentException("الجهاز غير موجود", nameof(deviceId));

                device.Status = status;
                device.LastCheck = DateTime.Now;

                await context.SaveChangesAsync().ConfigureAwait(false);
                return device;
            }
            finally
            {
                scope.Dispose();
            }
        }

        public async Task<int> UpdateMultipleDeviceStatusesAsync(Dictionary<string, string> deviceStatuses)
        {
            var scope = _serviceProvider.CreateScope();
            try
            {
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                if (context.Devices == null)
                    throw new InvalidOperationException("DbSet<Device> is null");

                // استخدام Transaction لضمان التحديث الآمن
                using var transaction = await context.Database.BeginTransactionAsync().ConfigureAwait(false);

                try
                {
                    int updatedCount = 0;
                    var updateTime = DateTime.Now;

                    // تحميل جميع الأجهزة المطلوب تحديثها مرة واحدة
                    var deviceIds = deviceStatuses.Keys.ToList();
                    var devices = await context.Devices
                        .Where(d => deviceIds.Contains(d.Id))
                        .ToListAsync()
                        .ConfigureAwait(false);

                    // تحديث الأجهزة
                    foreach (var device in devices)
                    {
                        if (deviceStatuses.TryGetValue(device.Id, out var newStatus))
                        {
                            device.Status = newStatus;
                            device.LastCheck = updateTime;
                            device.UpdatedAt = updateTime;
                            updatedCount++;
                        }
                    }

                    await context.SaveChangesAsync().ConfigureAwait(false);
                    await transaction.CommitAsync().ConfigureAwait(false);

                    return updatedCount;
                }
                catch
                {
                    await transaction.RollbackAsync().ConfigureAwait(false);
                    throw;
                }
            }
            finally
            {
                scope.Dispose();
            }
        }

        /// <summary>
        /// الحصول على جميع أنواع الأجهزة (الافتراضية والمخصصة)
        /// </summary>
        public async System.Threading.Tasks.Task<IEnumerable<string>> GetAllDeviceTypesAsync()
        {
            const string cacheKey = "device_types_all";

            return await _cacheService.GetOrCreateAsync(cacheKey, async () =>
            {
                var scope = _serviceProvider.CreateScope();
                try
                {
                    var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                    if (context.Devices == null)
                        throw new InvalidOperationException("DbSet<Device> is null");

                    // الحصول على الأنواع الافتراضية
                    var defaultTypes = new List<string> { "Router", "Switch", "Access Point", "Firewall", "Server", "أخرى" };

                    // الحصول على الأنواع المخصصة من قاعدة البيانات
                    var customTypes = await context.Devices
                        .Where(d => !string.IsNullOrEmpty(d.Type))
                        .Select(d => d.Type!)
                        .Distinct()
                        .Where(type => !defaultTypes.Contains(type))
                        .OrderBy(type => type)
                        .ToListAsync()
                        .ConfigureAwait(false);

                    // دمج الأنواع الافتراضية والمخصصة
                    var allTypes = new List<string>();
                    allTypes.AddRange(defaultTypes);
                    allTypes.AddRange(customTypes);

                    return allTypes.AsEnumerable();
                }
                finally
                {
                    scope.Dispose();
                }
            }, TimeSpan.FromMinutes(30)); // تخزين لمدة 30 دقيقة
        }

        /// <summary>
        /// حفظ نوع جهاز مخصص (مسح الذاكرة المؤقتة لإعادة التحميل)
        /// </summary>
        public async System.Threading.Tasks.Task SaveCustomDeviceTypeAsync(string deviceType)
        {
            if (string.IsNullOrWhiteSpace(deviceType))
                return;

            // مسح ذاكرة التخزين المؤقت لأنواع الأجهزة لإعادة تحميلها
            _cacheService.Remove("device_types_all");

            // لا نحتاج لحفظ النوع بشكل منفصل لأنه سيتم حفظه عند حفظ الجهاز
            // هذه الوظيفة موجودة للتوافق المستقبلي إذا أردنا إضافة جدول منفصل لأنواع الأجهزة
            await Task.CompletedTask;
        }

        /// <summary>
        /// مسح الذاكرة المؤقتة للأجهزة
        /// </summary>
        private void ClearDeviceCache(string? networkId)
        {
            try
            {
                // مسح جميع البيانات المؤقتة المتعلقة بالأجهزة بشكل موحد
                _cacheService.RemoveByPattern("devices_");
                _cacheService.Remove(CacheKeys.AllDevices);
                _cacheService.Remove("device_types_all");

                // مسح البيانات المؤقتة للشبكة المحددة فقط إذا لزم الأمر
                if (!string.IsNullOrEmpty(networkId))
                {
                    var networkCacheKey = CacheKeys.GetNetworkKey(CacheKeys.DevicesByNetwork, networkId);
                    _cacheService.Remove(networkCacheKey);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في مسح الذاكرة المؤقتة للأجهزة: {ex.Message}");
            }
        }

    }
}
