using System;
using System.Linq;
using System.Threading.Tasks;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using NetworkManagement.Services;
using NetworkManagement.Helpers;

namespace NetworkManagement.ViewModels
{
    public partial class DashboardViewModel : ObservableObject
    {
        private readonly IDeviceService _deviceService;
        private readonly ISiteService _siteService;
        private readonly ITaskService _taskService;
        private readonly IPurchaseService _purchaseService;
        private readonly IInventoryService _inventoryService;
        private readonly IAuthService _authService;

        [ObservableProperty]
        private int totalDevices = 0;

        [ObservableProperty]
        private int activeDevices = 0;

        [ObservableProperty]
        private int totalSites = 0;

        [ObservableProperty]
        private int pendingTasks = 0;

        [ObservableProperty]
        private decimal monthlySpending = 0;

        [ObservableProperty]
        private int lowStockItems = 0;

        [ObservableProperty]
        private bool isLoading = false;

        public string MonthlySpendingDisplay => $"{MonthlySpending:N0} ر.ي";

        public DashboardViewModel(
            IDeviceService deviceService,
            ISiteService siteService,
            ITaskService taskService,
            IPurchaseService purchaseService,
            IInventoryService inventoryService,
            IAuthService authService)
        {
            _deviceService = deviceService ?? throw new ArgumentNullException(nameof(deviceService));
            _siteService = siteService ?? throw new ArgumentNullException(nameof(siteService));
            _taskService = taskService ?? throw new ArgumentNullException(nameof(taskService));
            _purchaseService = purchaseService ?? throw new ArgumentNullException(nameof(purchaseService));
            _inventoryService = inventoryService ?? throw new ArgumentNullException(nameof(inventoryService));
            _authService = authService ?? throw new ArgumentNullException(nameof(authService));
        }

        public async Task InitializeAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("DashboardViewModel.InitializeAsync started");
                IsLoading = true;
                await LoadDataAsync();
                System.Diagnostics.Debug.WriteLine("DashboardViewModel.InitializeAsync completed successfully");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in InitializeAsync: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack trace: {ex.StackTrace}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task LoadDataAsync()
        {
            try
            {
                // الحصول على فلتر الشبكة حسب الصلاحيات
                var networkFilter = PermissionHelper.GetNetworkFilter(_authService);

                // تحميل الأجهزة مع تطبيق فلترة الصلاحيات
                var devices = await _deviceService.GetAllAsync(networkFilter);
                var filteredDevices = PermissionHelper.ApplyPermissionFilter(devices, _authService, d => d.NetworkId);
                TotalDevices = filteredDevices?.Count() ?? 0;

                // تحديد الأجهزة النشطة بشكل صحيح
                ActiveDevices = filteredDevices?.Count(d =>
                    d.Status == "يعمل" || d.Status == "متصل" || d.Status == "active" || d.Status == "نشط") ?? 0;

                // تحميل المواقع مع تطبيق فلترة الصلاحيات
                var sites = await _siteService.GetAllAsync(networkFilter);
                var filteredSites = PermissionHelper.ApplyPermissionFilter(sites, _authService, s => s.NetworkId);
                TotalSites = filteredSites?.Count() ?? 0;

                // تحميل المهام مع تطبيق فلترة الصلاحيات
                var tasks = await _taskService.GetAllAsync();
                var filteredTasks = PermissionHelper.ApplyPermissionFilter(tasks, _authService, t => t.NetworkId);
                PendingTasks = filteredTasks?.Count(t => t.Status == "pending") ?? 0;

                // تحميل المصروفات مع تطبيق فلترة الصلاحيات
                var now = DateTime.Now;
                var startOfMonth = new DateTime(now.Year, now.Month, 1);
                var endOfMonth = startOfMonth.AddMonths(1).AddDays(-1);
                MonthlySpending = await _purchaseService.GetTotalSpentAsync(networkFilter, startOfMonth, endOfMonth);

                // تحميل المخزون المنخفض مع تطبيق فلترة الصلاحيات
                var lowStock = await _inventoryService.GetLowStockItemsAsync(networkFilter);
                var filteredLowStock = PermissionHelper.ApplyPermissionFilter(lowStock, _authService, i => i.NetworkId);
                LowStockItems = filteredLowStock?.Count() ?? 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading data: {ex.Message}");
            }
        }

        [RelayCommand]
        private async Task RefreshData()
        {
            await LoadDataAsync();
        }

        partial void OnMonthlySpendingChanged(decimal value)
        {
            OnPropertyChanged(nameof(MonthlySpendingDisplay));
        }
    }
}
