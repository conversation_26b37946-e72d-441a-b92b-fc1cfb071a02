# برومت مفصل لصفحة تقييم المستخدمين - UserEvaluationView

## وصف الصفحة العام
صفحة تقييم المستخدمين مخصصة للمديرين (Admin و Manager) لتقييم أداء الموظفين بنظام نقاط من 1-10. تتضمن نظام صلاحيات متدرج، إحصائيات متقدمة، وتقارير شاملة مع دعم التقييم اليومي فقط.

## المتطلبات التقنية
- **النوع**: UserControl
- **الصلاحيات**: Admin (جميع المستخدمين)، Manager (الفنيين في شبكته فقط)
- **التقييم**: مرة واحدة يومياً لكل مستخدم
- **النقاط**: من 1 إلى 10 نقاط
- **التخطيط**: Grid مع تقسيمات متجاوبة
- **الثيم**: دعم الثيم الفاتح والداكن
- **اللغة**: عربية كاملة مع تخطيط RTL

## التصميم المرئي

### الألوان:
- **خلفية البطاقات**: أبيض (#FFFFFF) للثيم الفاتح، رمادي داكن (#424242) للثيم الداكن
- **ألوان النقاط**:
  - 9-10 نقاط: أخضر داكن (#2E7D32)
  - 7-8 نقاط: أخضر (#4CAF50)
  - 5-6 نقاط: برتقالي (#FF9800)
  - 3-4 نقاط: أحمر فاتح (#FF5722)
  - 1-2 نقاط: أحمر داكن (#D32F2F)
- **لون التحديد**: أزرق فاتح (#E3F2FD)
- **لون الحدود**: رمادي فاتح (#E0E0E0)

### الخطوط:
- **عناوين الأقسام**: Segoe UI، 18px، وزن Bold
- **أسماء المستخدمين**: Segoe UI، 14px، وزن Bold
- **النقاط**: Segoe UI، 24px، وزن Bold
- **النص العادي**: Tahoma، 13px، وزن Normal

## هيكل الصفحة

### 1. شريط الأدوات العلوي (Top Toolbar):
#### أ. العنوان والإحصائيات:
- **عنوان الصفحة**: "تقييم المستخدمين"
- **إحصائيات سريعة**:
  - عدد المستخدمين القابلين للتقييم: عدد مع أيقونة People
  - التقييمات اليوم: عدد التقييمات المكتملة اليوم
  - متوسط النقاط العام: متوسط جميع التقييمات
  - آخر تقييم: اسم المستخدم ووقت آخر تقييم

#### ب. أزرار الإجراءات الرئيسية:
1. **زر تقييم سريع**:
   - النص: "تقييم سريع"
   - الأيقونة: Speed icon
   - اللون: أخضر
   - الوظيفة: فتح نافذة تقييم سريع لعدة مستخدمين

2. **زر الإحصائيات المتقدمة**:
   - النص: "الإحصائيات"
   - الأيقونة: Analytics icon
   - اللون: أزرق
   - الوظيفة: فتح نافذة الإحصائيات المفصلة

3. **زر التصدير**:
   - النص: "تصدير التقارير"
   - الأيقونة: Export icon
   - اللون: برتقالي
   - القائمة المنسدلة: Excel, PDF, CSV
   - الوظيفة: تصدير تقارير التقييم

4. **زر الطباعة**:
   - النص: "طباعة"
   - الأيقونة: Print icon
   - اللون: رمادي
   - الاختصار: Ctrl+P
   - الوظيفة: طباعة تقرير التقييمات

### 2. شريط الفلترة والبحث (Filter and Search Bar):
#### أ. مربع البحث:
- **العرض**: 300 بكسل
- **Placeholder**: "البحث في المستخدمين (الاسم، اسم المستخدم...)"
- **الأيقونة**: Search icon
- **البحث**: فوري أثناء الكتابة
- **الاختصار**: Ctrl+F للتركيز

#### ب. فلاتر سريعة:
1. **فلتر الدور**:
   - النوع: ComboBox
   - الخيارات للـ Admin: الكل، Manager، Technician، User
   - الخيارات للـ Manager: الكل، Technician فقط
   - الافتراضي: الكل

2. **فلتر حالة التقييم اليوم**:
   - النوع: ComboBox
   - الخيارات: الكل، مُقيم اليوم، غير مُقيم اليوم
   - الافتراضي: غير مُقيم اليوم

3. **فلتر نطاق النقاط**:
   - النوع: RangeSlider
   - النطاق: 1-10
   - الافتراضي: 1-10
   - عرض النطاق المحدد

4. **فلتر الفترة الزمنية**:
   - النوع: ComboBox
   - الخيارات: اليوم، هذا الأسبوع، هذا الشهر، آخر 3 أشهر، مخصص
   - الافتراضي: هذا الشهر

### 3. قائمة المستخدمين للتقييم (Users Evaluation List):
#### أ. مواصفات القائمة:
- **التخطيط**: Grid مع بطاقات المستخدمين
- **عدد الأعمدة**: 3-4 بطاقات في الصف (حسب حجم الشاشة)
- **حجم البطاقة**: 300x200 بكسل
- **المسافات**: 15px بين البطاقات
- **التمرير**: عمودي

#### ب. تصميم بطاقة المستخدم:
1. **رأس البطاقة**:
   - **صورة المستخدم**: 60x60 بكسل (افتراضية إذا لم تكن متوفرة)
   - **اسم المستخدم**: خط Bold، 14px
   - **اسم المستخدم (Username)**: خط عادي، 12px، لون رمادي
   - **الدور**: شارة ملونة حسب الدور

2. **منطقة الإحصائيات**:
   - **متوسط النقاط الشهري**: رقم كبير مع لون حسب الأداء
   - **عدد التقييمات**: إجمالي التقييمات
   - **آخر تقييم**: تاريخ ونقاط آخر تقييم
   - **الاتجاه**: سهم صاعد/هابط مع النسبة

3. **منطقة التقييم**:
   - **حالة التقييم اليوم**:
     - إذا لم يُقيم: "لم يُقيم اليوم" (أحمر)
     - إذا قُيم: "تم التقييم: X نقاط" (أخضر)
   - **زر التقييم**:
     - النص: "تقييم" أو "تعديل التقييم"
     - اللون: أخضر أو أزرق
     - الحالة: مفعل أو معطل حسب حالة التقييم

4. **شريط الأداء**:
   - **شريط تقدم**: يعرض متوسط الأداء من 10
   - **اللون**: متدرج من الأحمر للأخضر
   - **النسبة المئوية**: نسبة الأداء

### 4. نافذة التقييم (Evaluation Dialog):
#### أ. معلومات المستخدم:
- **الاسم الكامل**: عرض فقط
- **اسم المستخدم**: عرض فقط
- **الدور**: عرض فقط
- **الشبكة**: عرض فقط
- **آخر تقييم**: التاريخ والنقاط

#### ب. منطقة التقييم:
1. **تاريخ التقييم**:
   - النوع: DatePicker
   - الافتراضي: اليوم الحالي
   - القيود: لا يمكن تقييم نفس المستخدم مرتين في نفس اليوم

2. **النقاط**:
   - النوع: Slider مع TextBox
   - النطاق: 1-10
   - الخطوة: 1
   - عرض النقطة الحالية بخط كبير وملون

3. **مؤشر الأداء البصري**:
   - **النجوم**: 10 نجوم تتلون حسب النقاط المحددة
   - **الألوان**: تدرج من الأحمر للأخضر
   - **النص التوضيحي**: وصف مستوى الأداء

4. **الملاحظات**:
   - النوع: TextBox متعدد الأسطر
   - الارتفاع: 80px
   - Placeholder: "أضف ملاحظات حول الأداء (اختياري)"
   - الحد الأقصى: 500 حرف

#### ج. الرسائل التحفيزية:
- **عرض رسالة تحفيزية** حسب النقاط المحددة:
  - 9-10 نقاط: "أداء ممتاز! استمر على هذا المستوى الرائع"
  - 7-8 نقاط: "أداء جيد جداً! يمكنك الوصول للامتياز"
  - 5-6 نقاط: "أداء مقبول، حاول تحسينه أكثر"
  - 3-4 نقاط: "أداء يحتاج تحسين، نحن نثق بقدراتك"
  - 1-2 نقاط: "يحتاج لتحسين كبير، لا تستسلم!"

#### د. أزرار النافذة:
1. **زر الحفظ**:
   - النص: "حفظ التقييم"
   - اللون: أخضر
   - الاختصار: Ctrl+S
   - الوظيفة: حفظ التقييم وإرسال إشعار للمستخدم

2. **زر الإلغاء**:
   - النص: "إلغاء"
   - اللون: رمادي
   - الاختصار: Escape
   - الوظيفة: إغلاق النافذة بدون حفظ

3. **زر عرض التاريخ**:
   - النص: "عرض تاريخ التقييمات"
   - اللون: أزرق
   - الوظيفة: عرض جميع تقييمات المستخدم السابقة

### 5. نافذة الإحصائيات المتقدمة (Advanced Statistics):
#### أ. الإحصائيات العامة:
1. **بطاقة المتوسط العام**:
   - متوسط جميع التقييمات
   - مقارنة مع الشهر الماضي
   - رسم بياني صغير للاتجاه

2. **بطاقة أعلى أداء**:
   - المستخدم صاحب أعلى متوسط
   - النقاط والنسبة المئوية
   - صورة المستخدم

3. **بطاقة التحسن الأكبر**:
   - المستخدم الذي حقق أكبر تحسن
   - نسبة التحسن
   - مقارنة الفترات

4. **بطاقة عدد التقييمات**:
   - إجمالي التقييمات
   - التقييمات هذا الشهر
   - معدل التقييم اليومي

#### ب. الرسوم البيانية:
1. **رسم بياني للأداء الشهري**:
   - النوع: Line Chart
   - البيانات: متوسط النقاط لآخر 6 أشهر
   - الألوان: تدرج أزرق

2. **رسم بياني لتوزيع النقاط**:
   - النوع: Bar Chart
   - البيانات: عدد المستخدمين في كل نطاق نقاط
   - الألوان: من الأحمر للأخضر

3. **رسم بياني للمقارنة بين الأدوار**:
   - النوع: Column Chart
   - البيانات: متوسط النقاط لكل دور
   - الألوان: ألوان مختلفة لكل دور

#### ج. جدول أفضل المؤدين:
- **الترتيب**: من 1 إلى 10
- **الاسم**: اسم المستخدم
- **المتوسط**: متوسط النقاط
- **عدد التقييمات**: إجمالي التقييمات
- **الاتجاه**: تحسن أم تراجع

### 6. نافذة التقييم السريع (Quick Evaluation):
#### أ. قائمة المستخدمين:
- **عرض جميع المستخدمين** غير المُقيمين اليوم
- **تحديد متعدد**: إمكانية تحديد عدة مستخدمين
- **نقاط موحدة**: إعطاء نفس النقاط للجميع
- **ملاحظات موحدة**: ملاحظة واحدة للجميع

#### ب. إعدادات التقييم السريع:
- **النقاط**: slider من 1-10
- **الملاحظات**: نص موحد
- **التاريخ**: تاريخ التقييم
- **تأكيد**: رسالة تأكيد قبل الحفظ

## الوظائف المطلوبة

### 1. نظام الصلاحيات:
```csharp
- Admin: يرى ويقيم جميع المستخدمين في جميع الشبكات
- Manager: يرى ويقيم الفنيين في شبكته فقط
- فلترة تلقائية للمستخدمين حسب الصلاحيات
- منع الوصول غير المصرح به
```

### 2. التقييم اليومي:
```csharp
- فحص عدم وجود تقييم سابق لنفس المستخدم في نفس اليوم
- السماح بتعديل التقييم في نفس اليوم
- منع التقييم المتكرر
- تسجيل تاريخ ووقت كل تقييم
```

### 3. حساب الإحصائيات:
```csharp
- حساب متوسط النقاط لكل مستخدم
- حساب المتوسط العام للشبكة
- تتبع الاتجاهات والتحسن
- مقارنة الفترات الزمنية
```

### 4. الإشعارات:
```csharp
- إشعار للمستخدم عند تلقي تقييم جديد
- إشعار للمدير عند اكتمال التقييمات اليومية
- رسائل تحفيزية حسب مستوى الأداء
```

### 5. التصدير والتقارير:
```csharp
- تصدير تقارير التقييم بصيغ مختلفة
- تقارير مفصلة لكل مستخدم
- تقارير مقارنة بين المستخدمين
- تقارير الاتجاهات والتحسن
```

## التفاعلات والأحداث

### 1. أحداث التقييم:
- النقر على زر التقييم: فتح نافذة التقييم
- تغيير النقاط: تحديث الرسالة التحفيزية
- حفظ التقييم: إرسال إشعار وتحديث الإحصائيات

### 2. أحداث البحث والفلترة:
- بحث فوري أثناء الكتابة
- تطبيق فوري للفلاتر
- حفظ الفلاتر المفضلة

### 3. أحداث الإحصائيات:
- تحديث تلقائي للإحصائيات
- تفاعل مع الرسوم البيانية
- تصدير الإحصائيات

## الأمان والتحقق

### 1. التحقق من الصلاحيات:
```csharp
- فحص دور المستخدم قبل عرض البيانات
- منع تقييم المستخدمين خارج الصلاحية
- تسجيل جميع عمليات التقييم
```

### 2. التحقق من البيانات:
```csharp
- التحقق من صحة النقاط (1-10)
- التحقق من عدم التكرار في نفس اليوم
- التحقق من صحة التاريخ
- تنظيف الملاحظات من المحتوى الضار
```

## الأداء والتحسين

### 1. تحسين العرض:
- تحميل كسول للبطاقات
- تخزين مؤقت للإحصائيات
- تحديث جزئي للبيانات
- ضغط الصور والبيانات

### 2. تحسين الاستعلامات:
- فهرسة جداول التقييمات
- استعلامات محسنة للإحصائيات
- تجميع البيانات المترابطة
- تخزين مؤقت للنتائج المتكررة

هذا البرومت المفصل يغطي جميع جوانب صفحة تقييم المستخدمين مع التركيز على نظام الصلاحيات المتدرج والتقييم اليومي.
