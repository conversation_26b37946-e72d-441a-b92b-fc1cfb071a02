using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Caching.Memory;

namespace NetworkManagement.Services
{
    /// <summary>
    /// خدمة التخزين المؤقت لتحسين الأداء
    /// </summary>
    public class CacheService : ICacheService
    {
        private readonly IMemoryCache _cache;
        private readonly ConcurrentDictionary<string, bool> _cacheKeys = new();
        private readonly TimeSpan _defaultExpiration = TimeSpan.FromMinutes(30);

        public CacheService(IMemoryCache cache)
        {
            _cache = cache;
        }

        public T? Get<T>(string key)
        {
            try
            {
                return _cache.Get<T>(key);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في استرجاع البيانات من الذاكرة المؤقتة: {ex.Message}");
                return default;
            }
        }

        public void Set<T>(string key, T value, TimeSpan? expiration = null)
        {
            try
            {
                var options = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = expiration ?? _defaultExpiration,
                    Priority = CacheItemPriority.Normal
                };

                // إضافة callback لحذف المفتاح من القاموس عند انتهاء الصلاحية
                options.RegisterPostEvictionCallback((key, value, reason, state) =>
                {
                    _cacheKeys.TryRemove(key.ToString() ?? "", out _);
                });

                _cache.Set(key, value, options);
                _cacheKeys.TryAdd(key, true);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ البيانات في الذاكرة المؤقتة: {ex.Message}");
            }
        }

        public async Task<T> GetOrCreateAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiration = null)
        {
            try
            {
                if (_cache.TryGetValue(key, out T? cachedValue) && cachedValue != null)
                {
                    return cachedValue;
                }

                var value = await factory();
                Set(key, value, expiration);
                return value;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في GetOrCreateAsync: {ex.Message}");
                return await factory(); // العودة للطريقة الأصلية في حالة الخطأ
            }
        }

        public void Remove(string key)
        {
            try
            {
                _cache.Remove(key);
                _cacheKeys.TryRemove(key, out _);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حذف البيانات من الذاكرة المؤقتة: {ex.Message}");
            }
        }

        public void RemoveByPattern(string pattern)
        {
            try
            {
                var keysToRemove = _cacheKeys.Keys
                    .Where(key => key.Contains(pattern, StringComparison.OrdinalIgnoreCase))
                    .ToList();

                foreach (var key in keysToRemove)
                {
                    Remove(key);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حذف البيانات بالنمط: {ex.Message}");
            }
        }

        public void Clear()
        {
            try
            {
                var keysToRemove = _cacheKeys.Keys.ToList();
                foreach (var key in keysToRemove)
                {
                    Remove(key);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في مسح الذاكرة المؤقتة: {ex.Message}");
            }
        }

        public bool Exists(string key)
        {
            try
            {
                return _cache.TryGetValue(key, out _);
            }
            catch
            {
                return false;
            }
        }
    }

    /// <summary>
    /// مفاتيح الذاكرة المؤقتة المستخدمة في التطبيق
    /// </summary>
    public static class CacheKeys
    {
        public const string AllDevices = "devices_all";
        public const string AllSites = "sites_all";
        public const string AllUsers = "users_all";
        public const string AllTasks = "tasks_all";
        public const string AllPurchases = "purchases_all";
        public const string AllInventory = "inventory_all";
        public const string AllNetworks = "networks_all";
        
        public const string DevicesByNetwork = "devices_network_{0}";
        public const string SitesByNetwork = "sites_network_{0}";
        public const string TasksByNetwork = "tasks_network_{0}";
        public const string PurchasesByNetwork = "purchases_network_{0}";
        public const string InventoryByNetwork = "inventory_network_{0}";
        
        public const string DashboardStats = "dashboard_stats_{0}";
        public const string MonthlySpending = "monthly_spending_{0}_{1}";
        
        public static string GetNetworkKey(string baseKey, string? networkId)
        {
            return string.Format(baseKey, networkId ?? "all");
        }
    }
}
