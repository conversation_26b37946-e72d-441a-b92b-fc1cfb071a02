{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {"NetworkManagement/2.1.0": {"dependencies": {"BCrypt.Net-Next": "4.0.3", "CommunityToolkit.Mvvm": "8.2.2", "EPPlus": "7.0.0", "MaterialDesignThemes": "4.9.0", "Microsoft.EntityFrameworkCore": "6.0.7", "Microsoft.EntityFrameworkCore.Relational": "6.0.7", "Microsoft.EntityFrameworkCore.Tools": "6.0.7", "Microsoft.Extensions.Caching.Memory": "7.0.0", "Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.Hosting": "7.0.1", "Microsoft.Xaml.Behaviors.Wpf": "1.1.39", "MySqlConnector": "2.1.13", "Pomelo.EntityFrameworkCore.MySql": "6.0.2", "QuestPDF": "2023.12.6", "System.Drawing.Common": "7.0.0", "System.Text.Json": "8.0.5"}, "runtime": {"NetworkManagement.dll": {}}}, "BCrypt.Net-Next/4.0.3": {"runtime": {"lib/net6.0/BCrypt.Net-Next.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "CommunityToolkit.Mvvm/8.2.2": {"runtime": {"lib/net6.0/CommunityToolkit.Mvvm.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "EPPlus/7.0.0": {"dependencies": {"EPPlus.Interfaces": "6.1.1", "EPPlus.System.Drawing": "6.1.1", "Microsoft.Extensions.Configuration.Json": "7.0.0", "Microsoft.IO.RecyclableMemoryStream": "2.3.2", "System.Security.Cryptography.Pkcs": "6.0.3"}, "runtime": {"lib/net6.0/EPPlus.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "EPPlus.Interfaces/6.1.1": {"runtime": {"lib/net6.0/EPPlus.Interfaces.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "EPPlus.System.Drawing/6.1.1": {"dependencies": {"EPPlus.Interfaces": "6.1.1", "System.Drawing.Common": "7.0.0"}, "runtime": {"lib/net6.0/EPPlus.System.Drawing.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "HarfBuzzSharp/7.3.0.1": {"dependencies": {"HarfBuzzSharp.NativeAssets.Win32": "7.3.0.1", "HarfBuzzSharp.NativeAssets.macOS": "7.3.0.1"}, "runtime": {"lib/net6.0/HarfBuzzSharp.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "7.3.0.1"}}}, "HarfBuzzSharp.NativeAssets.macOS/7.3.0.1": {"runtimeTargets": {"runtimes/osx/native/libHarfBuzzSharp.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "HarfBuzzSharp.NativeAssets.Win32/7.3.0.1": {"runtimeTargets": {"runtimes/win-arm64/native/libHarfBuzzSharp.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/libHarfBuzzSharp.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/libHarfBuzzSharp.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "Humanizer.Core/2.8.26": {"runtime": {"lib/netstandard2.0/Humanizer.dll": {"assemblyVersion": "2.8.0.0", "fileVersion": "2.8.26.1919"}}}, "MaterialDesignColors/2.1.4": {"runtime": {"lib/net6.0/MaterialDesignColors.dll": {"assemblyVersion": "2.1.4.0", "fileVersion": "2.1.4.0"}}}, "MaterialDesignThemes/4.9.0": {"dependencies": {"MaterialDesignColors": "2.1.4", "Microsoft.Xaml.Behaviors.Wpf": "1.1.39"}, "runtime": {"lib/net6.0/MaterialDesignThemes.Wpf.dll": {"assemblyVersion": "4.9.0.0", "fileVersion": "4.9.0.0"}}}, "Microsoft.EntityFrameworkCore/6.0.7": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "6.0.7", "Microsoft.EntityFrameworkCore.Analyzers": "6.0.7", "Microsoft.Extensions.Caching.Memory": "7.0.0", "Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "6.0.7.0", "fileVersion": "6.0.722.31501"}}}, "Microsoft.EntityFrameworkCore.Abstractions/6.0.7": {"runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "6.0.7.0", "fileVersion": "6.0.722.31501"}}}, "Microsoft.EntityFrameworkCore.Analyzers/6.0.7": {}, "Microsoft.EntityFrameworkCore.Design/6.0.7": {"dependencies": {"Humanizer.Core": "2.8.26", "Microsoft.EntityFrameworkCore.Relational": "6.0.7"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Design.dll": {"assemblyVersion": "6.0.7.0", "fileVersion": "6.0.722.31501"}}}, "Microsoft.EntityFrameworkCore.Relational/6.0.7": {"dependencies": {"Microsoft.EntityFrameworkCore": "6.0.7", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "6.0.7.0", "fileVersion": "6.0.722.31501"}}}, "Microsoft.EntityFrameworkCore.Tools/6.0.7": {"dependencies": {"Microsoft.EntityFrameworkCore.Design": "6.0.7"}}, "Microsoft.Extensions.Caching.Abstractions/7.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Caching.Memory/7.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.1", "Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Configuration/7.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Configuration.Abstractions/7.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Configuration.Binder/7.0.3": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.323.6910"}}}, "Microsoft.Extensions.Configuration.CommandLine/7.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "7.0.0", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/7.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "7.0.0", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Configuration.FileExtensions/7.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "7.0.0", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "7.0.0", "Microsoft.Extensions.FileProviders.Physical": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Configuration.Json/7.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "7.0.0", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "7.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "7.0.0", "System.Text.Json": "8.0.5"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Configuration.UserSecrets/7.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.Configuration.Json": "7.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "7.0.0", "Microsoft.Extensions.FileProviders.Physical": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.DependencyInjection/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/7.0.0": {"runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.FileProviders.Abstractions/7.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.FileProviders.Physical/7.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "7.0.0", "Microsoft.Extensions.FileSystemGlobbing": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.FileSystemGlobbing/7.0.0": {"runtime": {"lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Hosting/7.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "7.0.0", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.Configuration.Binder": "7.0.3", "Microsoft.Extensions.Configuration.CommandLine": "7.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "7.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "7.0.0", "Microsoft.Extensions.Configuration.Json": "7.0.0", "Microsoft.Extensions.Configuration.UserSecrets": "7.0.0", "Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "7.0.0", "Microsoft.Extensions.FileProviders.Physical": "7.0.0", "Microsoft.Extensions.Hosting.Abstractions": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Configuration": "7.0.0", "Microsoft.Extensions.Logging.Console": "7.0.0", "Microsoft.Extensions.Logging.Debug": "7.0.0", "Microsoft.Extensions.Logging.EventLog": "7.0.0", "Microsoft.Extensions.Logging.EventSource": "7.0.0", "Microsoft.Extensions.Options": "7.0.1", "System.Diagnostics.DiagnosticSource": "7.0.1"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.323.6910"}}}, "Microsoft.Extensions.Hosting.Abstractions/7.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Logging/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.1"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Logging.Abstractions/7.0.0": {"runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Logging.Configuration/7.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "7.0.0", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.Configuration.Binder": "7.0.3", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.1", "Microsoft.Extensions.Options.ConfigurationExtensions": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Logging.Console/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Configuration": "7.0.0", "Microsoft.Extensions.Options": "7.0.1", "System.Text.Json": "8.0.5"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Console.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Logging.Debug/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Logging.EventLog/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.1", "System.Diagnostics.EventLog": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Logging.EventSource/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.1", "Microsoft.Extensions.Primitives": "7.0.0", "System.Text.Json": "8.0.5"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.EventSource.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Options/7.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.323.6910"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/7.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.Configuration.Binder": "7.0.3", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.1", "Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Primitives/7.0.0": {"runtime": {"lib/net6.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.IO.RecyclableMemoryStream/2.3.2": {"runtime": {"lib/net5.0/Microsoft.IO.RecyclableMemoryStream.dll": {"assemblyVersion": "2.3.2.0", "fileVersion": "2.3.2.0"}}}, "Microsoft.Win32.SystemEvents/7.0.0": {"runtime": {"lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Xaml.Behaviors.Wpf/1.1.39": {"runtime": {"lib/net5.0-windows7.0/Microsoft.Xaml.Behaviors.dll": {"assemblyVersion": "1.1.0.0", "fileVersion": "1.1.39.4716"}}}, "MySqlConnector/2.1.13": {"runtime": {"lib/net6.0/MySqlConnector.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.1.13.0"}}}, "Pomelo.EntityFrameworkCore.MySql/6.0.2": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "6.0.7", "Microsoft.Extensions.DependencyInjection": "7.0.0", "MySqlConnector": "2.1.13"}, "runtime": {"lib/net6.0/Pomelo.EntityFrameworkCore.MySql.dll": {"assemblyVersion": "6.0.2.0", "fileVersion": "6.0.2.0"}}}, "QuestPDF/2023.12.6": {"dependencies": {"SkiaSharp": "2.88.7", "SkiaSharp.HarfBuzz": "2.88.7"}, "runtime": {"lib/net6.0/QuestPDF.dll": {"assemblyVersion": "2023.12.6.0", "fileVersion": "2023.12.6.0"}}}, "SkiaSharp/2.88.7": {"dependencies": {"SkiaSharp.NativeAssets.Win32": "2.88.7", "SkiaSharp.NativeAssets.macOS": "2.88.7"}, "runtime": {"lib/net6.0/SkiaSharp.dll": {"assemblyVersion": "2.88.0.0", "fileVersion": "2.88.7.0"}}}, "SkiaSharp.HarfBuzz/2.88.7": {"dependencies": {"HarfBuzzSharp": "7.3.0.1", "SkiaSharp": "2.88.7"}, "runtime": {"lib/net6.0/SkiaSharp.HarfBuzz.dll": {"assemblyVersion": "2.88.0.0", "fileVersion": "2.88.7.0"}}}, "SkiaSharp.NativeAssets.macOS/2.88.7": {"runtimeTargets": {"runtimes/osx/native/libSkiaSharp.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SkiaSharp.NativeAssets.Win32/2.88.7": {"runtimeTargets": {"runtimes/win-arm64/native/libSkiaSharp.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/libSkiaSharp.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/libSkiaSharp.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "System.Diagnostics.DiagnosticSource/7.0.1": {"runtime": {"lib/net6.0/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.323.6910"}}}, "System.Diagnostics.EventLog/7.0.0": {"runtime": {"lib/net6.0/System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Diagnostics.EventLog.Messages.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "0.0.0.0"}, "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Drawing.Common/7.0.0": {"dependencies": {"Microsoft.Win32.SystemEvents": "7.0.0"}, "runtime": {"lib/net6.0/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Drawing.Common.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Security.Cryptography.Pkcs/6.0.3": {"runtime": {"lib/net6.0/System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.1823.26907"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Security.Cryptography.Pkcs.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "6.0.0.0", "fileVersion": "6.0.1823.26907"}}}, "System.Text.Encodings.Web/8.0.0": {"runtime": {"lib/net6.0/System.Text.Encodings.Web.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/browser/lib/net6.0/System.Text.Encodings.Web.dll": {"rid": "browser", "assetType": "runtime", "assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}}, "System.Text.Json/8.0.5": {"dependencies": {"System.Text.Encodings.Web": "8.0.0"}, "runtime": {"lib/net6.0/System.Text.Json.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.1024.46610"}}}}}, "libraries": {"NetworkManagement/2.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "BCrypt.Net-Next/4.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-W+U9WvmZQgi5cX6FS5GDtDoPzUCV4LkBLkywq/kRZhuDwcbavOzcDAr3LXJFqHUi952Yj3LEYoWW0jbEUQChsA==", "path": "bcrypt.net-next/4.0.3", "hashPath": "bcrypt.net-next.4.0.3.nupkg.sha512"}, "CommunityToolkit.Mvvm/8.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-r0g0k9tGYdrnz8R7T3x5UiokDffeevzK/2P/9SBL6fqLgN8B157MIi/bVUWI1KAz6ZorZrK9AdABCWUeXZZsvA==", "path": "communitytoolkit.mvvm/8.2.2", "hashPath": "communitytoolkit.mvvm.8.2.2.nupkg.sha512"}, "EPPlus/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fqDPDAB0v+sU1m0VxDy4jBKhH77BoiaGjglnzflUVoHDL3Bnf8Xf/V8/dm3uYhMwChExnqFXxYscslu46seZQQ==", "path": "epplus/7.0.0", "hashPath": "epplus.7.0.0.nupkg.sha512"}, "EPPlus.Interfaces/6.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-y7dkrOoE1ZR9Vgy1Jf2rEIaTf3SHlUjYt01NklP+F5Qh7S2ruPbzTcpYLRWMeXiG8XL8h2jqX4CyIkFt3NQGZw==", "path": "epplus.interfaces/6.1.1", "hashPath": "epplus.interfaces.6.1.1.nupkg.sha512"}, "EPPlus.System.Drawing/6.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-lRF5gHYrmkHOOiLMI0t6q8zNYjUrzRgAM5BCXumv5xiqXko8fx3AWI+HCNZfhEqVFGOop+42KfR5GiUcCoyoMw==", "path": "epplus.system.drawing/6.1.1", "hashPath": "epplus.system.drawing.6.1.1.nupkg.sha512"}, "HarfBuzzSharp/7.3.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-/6z6TQBqWWbVpLUw7E/82HZKLT3OAzg/w6z8I6JFNFcYa3nM9RnZ8XGos9nD4buAzThLHpqPw/rb8FdZXEORAQ==", "path": "harfbuzzsharp/7.3.0.1", "hashPath": "harfbuzzsharp.7.3.0.1.nupkg.sha512"}, "HarfBuzzSharp.NativeAssets.macOS/7.3.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-4i6e8WhXF04At1rZ904xjt5g10W86u6B2SpzWAw+cfY4mzfyUV0hgZHIWsE6PsW8a9bP/bSdsZx/Rt4UIroi4w==", "path": "harfbuzzsharp.nativeassets.macos/7.3.0.1", "hashPath": "harfbuzzsharp.nativeassets.macos.7.3.0.1.nupkg.sha512"}, "HarfBuzzSharp.NativeAssets.Win32/7.3.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-NxzjhGcY7fYCu51BsanQurXXyqGogZiMzFBpDrZg49jNhT/S0QWYe/uvmP9YbwagprKQ+2tbGT0cw1BvxJciXQ==", "path": "harfbuzzsharp.nativeassets.win32/7.3.0.1", "hashPath": "harfbuzzsharp.nativeassets.win32.7.3.0.1.nupkg.sha512"}, "Humanizer.Core/2.8.26": {"type": "package", "serviceable": true, "sha512": "sha512-OiKusGL20vby4uDEswj2IgkdchC1yQ6rwbIkZDVBPIR6al2b7n3pC91elBul9q33KaBgRKhbZH3+2Ur4fnWx2A==", "path": "humanizer.core/2.8.26", "hashPath": "humanizer.core.2.8.26.nupkg.sha512"}, "MaterialDesignColors/2.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-C4Oy+qkjMoMPoZKyqYdCnIYtK8c0OSIHmNP73Vgc69NjiUG093xTkE7W/Ks54cTDS7fmWOtUHfwISTVTtb/YKg==", "path": "materialdesigncolors/2.1.4", "hashPath": "materialdesigncolors.2.1.4.nupkg.sha512"}, "MaterialDesignThemes/4.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-Bp9Auw70j+9V7WsUMT4pc8ulVzfL0Eav/tyGgICDirxxhKJwhqtC/6PRkTUm+R1t9611xiDuk5pSUNdDV6vfOQ==", "path": "materialdesignthemes/4.9.0", "hashPath": "materialdesignthemes.4.9.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/6.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-9BsvGSpTzxvqnxH19wLBFivK5TzWmsHZQc/1cQ4b2e+k85aIG9R4FYewQLHZdPrAxNQImXjTyW5nRI3s1rpt6A==", "path": "microsoft.entityframeworkcore/6.0.7", "hashPath": "microsoft.entityframeworkcore.6.0.7.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/6.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-bjU0CkTqldgpVPTSj9M+R/3EaTz+u0jMeQMIC91YdGYDbpX/tAN5UYx+Ihzk4AtP8gmhburQUgMTdnmCE9c5sA==", "path": "microsoft.entityframeworkcore.abstractions/6.0.7", "hashPath": "microsoft.entityframeworkcore.abstractions.6.0.7.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/6.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-VAOrGma8mRspUb/9quwIr21UZVqfWOcRQqhcYNkHBUD7woenwFTBvntiC9h2Ebtvj/BrRfezqjaHpWVvPSg4dw==", "path": "microsoft.entityframeworkcore.analyzers/6.0.7", "hashPath": "microsoft.entityframeworkcore.analyzers.6.0.7.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Design/6.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-chshSATT8ugtqCiYryx5qDefK99o2ojyMCOIlQgetDhe83czL9Sp7s1a5MkvUyN5D4F0Vv8gXU77Wv+cplUJuw==", "path": "microsoft.entityframeworkcore.design/6.0.7", "hashPath": "microsoft.entityframeworkcore.design.6.0.7.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/6.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-0uo4fPDHutMbd9AJJEKl2q/2fYuFGA8tEBE2fQeQFaNDd+F/aUjaXc1FUD84J7Wcax8WP40rZo1E0u9A0yPPZw==", "path": "microsoft.entityframeworkcore.relational/6.0.7", "hashPath": "microsoft.entityframeworkcore.relational.6.0.7.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Tools/6.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-Gdv70a73pXNYwvlo278uPFU4zo5Dnl5sB1eiAHyc7AUd3xeWueqx9ke9F4n1CHnEaB9h21x/c2waQAdMJqcYCQ==", "path": "microsoft.entityframeworkcore.tools/6.0.7", "hashPath": "microsoft.entityframeworkcore.tools.6.0.7.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IeimUd0TNbhB4ded3AbgBLQv2SnsiVugDyGV1MvspQFVlA07nDC7Zul7kcwH5jWN3JiTcp/ySE83AIJo8yfKjg==", "path": "microsoft.extensions.caching.abstractions/7.0.0", "hashPath": "microsoft.extensions.caching.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xpidBs2KCE2gw1JrD0quHE72kvCaI3xFql5/Peb2GRtUuZX+dYPoK/NTdVMiM67Svym0M0Df9A3xyU0FbMQhHw==", "path": "microsoft.extensions.caching.memory/7.0.0", "hashPath": "microsoft.extensions.caching.memory.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tldQUBWt/xeH2K7/hMPPo5g8zuLc3Ro9I5d4o/XrxvxOCA2EZBtW7bCHHTc49fcBtvB8tLAb/Qsmfrq+2SJ4vA==", "path": "microsoft.extensions.configuration/7.0.0", "hashPath": "microsoft.extensions.configuration.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-f34u2eaqIjNO9YLHBz8rozVZ+TcFiFs0F3r7nUJd7FRkVSxk8u4OpoK226mi49MwexHOR2ibP9MFvRUaLilcQQ==", "path": "microsoft.extensions.configuration.abstractions/7.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/7.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-1eRFwJBrkkncTpvh6mivB8zg4uBVm6+Y6stEJERrVEqZZc8Hvf+N1iIgj2ySYDUQko4J1Gw1rLf1M8bG83F0eA==", "path": "microsoft.extensions.configuration.binder/7.0.3", "hashPath": "microsoft.extensions.configuration.binder.7.0.3.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-a8Iq8SCw5m8W5pZJcPCgBpBO4E89+NaObPng+ApIhrGSv9X4JPrcFAaGM4sDgR0X83uhLgsNJq8VnGP/wqhr8A==", "path": "microsoft.extensions.configuration.commandline/7.0.0", "hashPath": "microsoft.extensions.configuration.commandline.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-RIkfqCkvrAogirjsqSrG1E1FxgrLsOZU2nhRbl07lrajnxzSU2isj2lwQah0CtCbLWo/pOIukQzM1GfneBUnxA==", "path": "microsoft.extensions.configuration.environmentvariables/7.0.0", "hashPath": "microsoft.extensions.configuration.environmentvariables.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xk2lRJ1RDuqe57BmgvRPyCt6zyePKUmvT6iuXqiHR+/OIIgWVR8Ff5k2p6DwmqY8a17hx/OnrekEhziEIeQP6Q==", "path": "microsoft.extensions.configuration.fileextensions/7.0.0", "hashPath": "microsoft.extensions.configuration.fileextensions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LDNYe3uw76W35Jci+be4LDf2lkQZe0A7EEYQVChFbc509CpZ4Iupod8li4PUXPBhEUOFI/rlQNf5xkzJRQGvtA==", "path": "microsoft.extensions.configuration.json/7.0.0", "hashPath": "microsoft.extensions.configuration.json.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-33HPW1PmB2RS0ietBQyvOxjp4O3wlt+4tIs8KPyMn1kqp04goiZGa7+3mc69NRLv6bphkLDy0YR7Uw3aZyf8Zw==", "path": "microsoft.extensions.configuration.usersecrets/7.0.0", "hashPath": "microsoft.extensions.configuration.usersecrets.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-elNeOmkeX3eDVG6pYVeV82p29hr+UKDaBhrZyWvWLw/EVZSYEkZlQdkp0V39k/Xehs2Qa0mvoCvkVj3eQxNQ1Q==", "path": "microsoft.extensions.dependencyinjection/7.0.0", "hashPath": "microsoft.extensions.dependencyinjection.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-h3j/QfmFN4S0w4C2A6X7arXij/M/OVw3uQHSOFxnND4DyAzO1F9eMX7Eti7lU/OkSthEE0WzRsfT/Dmx86jzCw==", "path": "microsoft.extensions.dependencyinjection.abstractions/7.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NyawiW9ZT/liQb34k9YqBSNPLuuPkrjMgQZ24Y/xXX1RoiBkLUdPMaQTmxhZ5TYu8ZKZ9qayzil75JX95vGQUg==", "path": "microsoft.extensions.fileproviders.abstractions/7.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-K8D2MTR+EtzkbZ8z80LrG7Ur64R7ZZdRLt1J5cgpc/pUWl0C6IkAUapPuK28oionHueCPELUqq0oYEvZfalNdg==", "path": "microsoft.extensions.fileproviders.physical/7.0.0", "hashPath": "microsoft.extensions.fileproviders.physical.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-2jONjKHiF+E92ynz2ZFcr9OvxIw+rTGMPEH+UZGeHTEComVav93jQUWGkso8yWwVBcEJGcNcZAaqY01FFJcj7w==", "path": "microsoft.extensions.filesystemglobbing/7.0.0", "hashPath": "microsoft.extensions.filesystemglobbing.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting/7.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-aoeMou6XSW84wiqd895OdaGyO9PfH6nohQJ0XBcshRDafbdIU6PQIVl8TpOCssPYq3ciRseP5064hbFyCR9J9w==", "path": "microsoft.extensions.hosting/7.0.1", "hashPath": "microsoft.extensions.hosting.7.0.1.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-43n9Je09z0p/7ViPxfRqs5BUItRLNVh5b6JH40F2Agkh2NBsY/jpNYTtbCcxrHCsA3oRmbR6RJBzUutB4VZvNQ==", "path": "microsoft.extensions.hosting.abstractions/7.0.0", "hashPath": "microsoft.extensions.hosting.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nw2muoNrOG5U5qa2ZekXwudUn2BJcD41e65zwmDHb1fQegTX66UokLWZkJRpqSSHXDOWZ5V0iqhbxOEky91atA==", "path": "microsoft.extensions.logging/7.0.0", "hashPath": "microsoft.extensions.logging.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-kmn78+LPVMOWeITUjIlfxUPDsI0R6G0RkeAMBmQxAJ7vBJn4q2dTva7pWi65ceN5vPGjJ9q/Uae2WKgvfktJAw==", "path": "microsoft.extensions.logging.abstractions/7.0.0", "hashPath": "microsoft.extensions.logging.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FLDA0HcffKA8ycoDQLJuCNGIE42cLWPxgdQGRBaSzZrYTkMBjnf9zrr8pGT06psLq9Q+RKWmmZczQ9bCrXEBcA==", "path": "microsoft.extensions.logging.configuration/7.0.0", "hashPath": "microsoft.extensions.logging.configuration.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Console/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qt5n8bHLZPUfuRnFxJKW5q9ZwOTncdh96rtWzWpX3Y/064MlxzCSw2ELF5Jlwdo+Y4wK3I47NmUTFsV7Sg8rqg==", "path": "microsoft.extensions.logging.console/7.0.0", "hashPath": "microsoft.extensions.logging.console.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Debug/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tFGGyPDpJ8ZdQdeckCArP7nZuoY3am9zJWuvp4OD1bHq65S0epW9BNHzAWeaIO4eYwWnGm1jRNt3vRciH8H6MA==", "path": "microsoft.extensions.logging.debug/7.0.0", "hashPath": "microsoft.extensions.logging.debug.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventLog/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Rp7cYL9xQRVTgjMl77H5YDxszAaO+mlA+KT0BnLSVhuCoKQQOOs1sSK2/x8BK2dZ/lKeAC/CVF+20Ef2dpKXwg==", "path": "microsoft.extensions.logging.eventlog/7.0.0", "hashPath": "microsoft.extensions.logging.eventlog.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventSource/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MxQXndQFviIyOPqyMeLNshXnmqcfzEHE2wWcr7BF1unSisJgouZ3tItnq+aJLGPojrW8OZSC/ZdRoR6wAq+c7w==", "path": "microsoft.extensions.logging.eventsource/7.0.0", "hashPath": "microsoft.extensions.logging.eventsource.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/7.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-pZRDYdN1FpepOIfHU62QoBQ6zdAoTvnjxFfqAzEd9Jhb2dfhA5i6jeTdgGgcgTWFRC7oT0+3XrbQu4LjvgX1Nw==", "path": "microsoft.extensions.options/7.0.1", "hashPath": "microsoft.extensions.options.7.0.1.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-95UnxZkkFdXxF6vSrtJsMHCzkDeSMuUWGs2hDT54cX+U5eVajrCJ3qLyQRW+CtpTt5OJ8bmTvpQVHu1DLhH+cA==", "path": "microsoft.extensions.options.configurationextensions/7.0.0", "hashPath": "microsoft.extensions.options.configurationextensions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-um1KU5kxcRp3CNuI8o/GrZtD4AIOXDk+RLsytjZ9QPok3ttLUelLKpilVPuaFT3TFjOhSibUAso0odbOaCDj3Q==", "path": "microsoft.extensions.primitives/7.0.0", "hashPath": "microsoft.extensions.primitives.7.0.0.nupkg.sha512"}, "Microsoft.IO.RecyclableMemoryStream/2.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-Oh1qXXFdJFcHozvb4H6XYLf2W0meZFuG0A+TfapFPj9z5fd4vxiARGEhAaLj/6XWQaMYIv4SH/9Q6H78Hw0E2Q==", "path": "microsoft.io.recyclablememorystream/2.3.2", "hashPath": "microsoft.io.recyclablememorystream.2.3.2.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-2nXPrhdAyAzir0gLl8Yy8S5Mnm/uBSQQA7jEsILOS1MTyS7DbmV1NgViMtvV1sfCD1ebITpNwb1NIinKeJgUVQ==", "path": "microsoft.win32.systemevents/7.0.0", "hashPath": "microsoft.win32.systemevents.7.0.0.nupkg.sha512"}, "Microsoft.Xaml.Behaviors.Wpf/1.1.39": {"type": "package", "serviceable": true, "sha512": "sha512-8PZKqw9QOcu42xk8puY4P1+EXHL9YGOR9b7qhaYx5cILHul456H073tj99vyPcCt0W0781T9RwHqkx507ZyUpQ==", "path": "microsoft.xaml.behaviors.wpf/1.1.39", "hashPath": "microsoft.xaml.behaviors.wpf.1.1.39.nupkg.sha512"}, "MySqlConnector/2.1.13": {"type": "package", "serviceable": true, "sha512": "sha512-Y3qts7mnuZ4EGEQOI4xJ1n+9ZJOmrcwD2CdqyAXuQONBkrc2ENDywE6ZBysFaRojXrnWzLXpc595+GYlN/PI9Q==", "path": "mysqlconnector/2.1.13", "hashPath": "mysqlconnector.2.1.13.nupkg.sha512"}, "Pomelo.EntityFrameworkCore.MySql/6.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-KvlZ800CnEuEGnxj5OT1fCKGjQXxW5kpPlCP91JqBYG+2Z3927eqXmlX6LLKUt4swqE8ZsEQ+Zkpab8bqstf4g==", "path": "pomelo.entityframeworkcore.mysql/6.0.2", "hashPath": "pomelo.entityframeworkcore.mysql.6.0.2.nupkg.sha512"}, "QuestPDF/2023.12.6": {"type": "package", "serviceable": true, "sha512": "sha512-6WYu+g7Q+q8ZKnoqpu7BhVUdoS8n2TD7tqpbm3ZCczDhk+SuatI3rwIuJKTrADI1kSMrrkOCVvr9nrWd3jyZXw==", "path": "questpdf/2023.12.6", "hashPath": "questpdf.2023.12.6.nupkg.sha512"}, "SkiaSharp/2.88.7": {"type": "package", "serviceable": true, "sha512": "sha512-LJHAMrbWO00J7jXWLWehyjqFo29T4VzABimfJb4nICqpEe3c/KvQGWL4ItON8ymzhxYOeFgyxeRzuNzO4GHSug==", "path": "skiasharp/2.88.7", "hashPath": "skiasharp.2.88.7.nupkg.sha512"}, "SkiaSharp.HarfBuzz/2.88.7": {"type": "package", "serviceable": true, "sha512": "sha512-4Vd+GpokorZNpDvUp09zjK6CMXN35zyJtNvzkGtS1TsXlG/5yA3lWTsQIU0nSQjfgpYql0tSEU5+WTsjxljugg==", "path": "skiasharp.harfbuzz/2.88.7", "hashPath": "skiasharp.harfbuzz.2.88.7.nupkg.sha512"}, "SkiaSharp.NativeAssets.macOS/2.88.7": {"type": "package", "serviceable": true, "sha512": "sha512-3jNzco4VjcYPFNxR9aNWcgweFXbTSdM1VpNRzCS4X0i1A1OuNqcaulrAvmntNpujeWxHo9e6WGh6FN8Jf5+XhA==", "path": "skiasharp.nativeassets.macos/2.88.7", "hashPath": "skiasharp.nativeassets.macos.2.88.7.nupkg.sha512"}, "SkiaSharp.NativeAssets.Win32/2.88.7": {"type": "package", "serviceable": true, "sha512": "sha512-BCXmWdQ0oVck9vRwC8U3ocSaTHEx28VB+6qw9OxGIMQ86iO5bp4Flqk3IXH0l9Pbr7vWAUOpI212iaL9mTaUZQ==", "path": "skiasharp.nativeassets.win32/2.88.7", "hashPath": "skiasharp.nativeassets.win32.2.88.7.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/7.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-T9SLFxzDp0SreCffRDXSAS5G+lq6E8qP4knHS2IBjwCdx2KEvGnGZsq7gFpselYOda7l6gXsJMD93TQsFj/URA==", "path": "system.diagnostics.diagnosticsource/7.0.1", "hashPath": "system.diagnostics.diagnosticsource.7.0.1.nupkg.sha512"}, "System.Diagnostics.EventLog/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eUDP47obqQm3SFJfP6z+Fx2nJ4KKTQbXB4Q9Uesnzw9SbYdhjyoGXuvDn/gEmFY6N5Z3bFFbpAQGA7m6hrYJCw==", "path": "system.diagnostics.eventlog/7.0.0", "hashPath": "system.diagnostics.eventlog.7.0.0.nupkg.sha512"}, "System.Drawing.Common/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-K<PERSON>+oBU38pxkKPxvLcLfIkOV5Ien8ReN78wro7OF5/erwcmortzeFx+iBswlh2Vz6gVne0khocQudGwaO1Ey6A==", "path": "system.drawing.common/7.0.0", "hashPath": "system.drawing.common.7.0.0.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/6.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-18UT1BdZ4TYFBHk/wuq7JzCdE3X75z81X+C2rXqIlmEnC21Pm60spGV/dKQSzbyomstqYE33EuN5hfnC86VFxA==", "path": "system.security.cryptography.pkcs/6.0.3", "hashPath": "system.security.cryptography.pkcs.6.0.3.nupkg.sha512"}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ==", "path": "system.text.encodings.web/8.0.0", "hashPath": "system.text.encodings.web.8.0.0.nupkg.sha512"}, "System.Text.Json/8.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-0f1B50Ss7rqxXiaBJyzUu9bWFOO2/zSlifZ/UNMdiIpDYe4cY4LQQicP4nirK1OS31I43rn062UIJ1Q9bpmHpg==", "path": "system.text.json/8.0.5", "hashPath": "system.text.json.8.0.5.nupkg.sha512"}}}