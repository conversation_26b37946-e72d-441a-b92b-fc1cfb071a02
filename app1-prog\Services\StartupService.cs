using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Extensions.DependencyInjection;
using NetworkManagement.Data;
using NetworkManagement.Models;
using NetworkManagement.ViewModels;
using Microsoft.EntityFrameworkCore;

namespace NetworkManagement.Services
{
    /// <summary>
    /// خدمة بدء التشغيل السريع
    /// </summary>
    public class StartupService : IStartupService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ICacheService _cacheService;
        private readonly ISettingsService _settingsService;

        public StartupService(IServiceProvider serviceProvider, ICacheService cacheService, ISettingsService settingsService)
        {
            _serviceProvider = serviceProvider;
            _cacheService = cacheService;
            _settingsService = settingsService;
        }

        public async System.Threading.Tasks.Task InitializeAsync()
        {
            try
            {
                // تشغيل المهام بشكل متوازي لتسريع البدء
                var tasks = new[]
                {
                    IsDatabaseReadyAsync(),
                    OptimizeStartupAsync(),
                    PreloadEssentialDataAsync()
                };

                await System.Threading.Tasks.Task.WhenAll(tasks);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تهيئة التطبيق: {ex.Message}");
                throw;
            }
        }

        public async System.Threading.Tasks.Task<bool> IsDatabaseReadyAsync()
        {
            try
            {
                // فحص سريع للاتصال بقاعدة البيانات
                return await _settingsService.TestDatabaseConnectionAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في فحص قاعدة البيانات: {ex.Message}");
                return false;
            }
        }

        public async System.Threading.Tasks.Task PreloadEssentialDataAsync()
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                using var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                // تحميل البيانات الأساسية في الخلفية
                _ = System.Threading.Tasks.Task.Run(async () =>
                {
                    try
                    {
                        // تحميل الشبكات النشطة فقط
                        var activeNetworks = new List<Network>();
                        if (context.Networks != null)
                        {
                            activeNetworks = await context.Networks
                                .Where(n => n.IsActive)
                                .Take(10)
                                .ToListAsync();
                        }

                        // حفظ في الذاكرة المؤقتة
                        _cacheService.Set("active_networks", activeNetworks, TimeSpan.FromMinutes(30));

                        // تحميل إحصائيات سريعة
                        var deviceCount = 0;
                        var siteCount = 0;

                        if (context.Devices != null)
                        {
                            deviceCount = await context.Devices.CountAsync();
                        }

                        if (context.Sites != null)
                        {
                            siteCount = await context.Sites.CountAsync();
                        }
                        
                        _cacheService.Set("quick_stats", new { DeviceCount = deviceCount, SiteCount = siteCount }, TimeSpan.FromMinutes(15));
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"خطأ في تحميل البيانات الأساسية: {ex.Message}");
                    }
                });

                await System.Threading.Tasks.Task.CompletedTask;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل البيانات الأساسية: {ex.Message}");
            }
        }

        public async System.Threading.Tasks.Task OptimizeStartupAsync()
        {
            try
            {
                // تحسينات الأداء
                await System.Threading.Tasks.Task.Run(() =>
                {
                    // تنظيف الذاكرة المؤقتة القديمة
                    GC.Collect();
                    GC.WaitForPendingFinalizers();
                    GC.Collect();

                    // تحسين إعدادات .NET
                    System.Runtime.GCSettings.LatencyMode = System.Runtime.GCLatencyMode.Interactive;
                });

                // تحميل التجميعات المطلوبة مسبقاً
                _ = System.Threading.Tasks.Task.Run(() =>
                {
                    try
                    {
                        // تحميل التجميعات الأساسية
                        var assemblies = new[]
                        {
                            typeof(Device).Assembly,
                            typeof(DeviceService).Assembly,
                            typeof(DashboardViewModel).Assembly
                        };

                        foreach (var assembly in assemblies)
                        {
                            _ = assembly.GetTypes();
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"خطأ في تحميل التجميعات: {ex.Message}");
                    }
                });

                await System.Threading.Tasks.Task.CompletedTask;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحسين البدء: {ex.Message}");
            }
        }
    }
}
