using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using NetworkManagement.Data;
using NetworkManagement.Models;
using System.Text.Json;

namespace NetworkManagement.Services
{
    public class SiteInventoryService : ISiteInventoryService
    {
        private readonly IServiceProvider _serviceProvider;

        public SiteInventoryService(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        public async Task<SiteInventoryCheckResult> CheckInventoryAvailabilityAsync(Site site, string? networkId = null)
        {
            var result = new SiteInventoryCheckResult();
            var scope = _serviceProvider.CreateScope();

            try
            {
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                // التحقق من البطارية - دعم القيم السالبة
                if (!string.IsNullOrEmpty(site.BatteryType))
                {
                    var batteryItem = await FindInventoryItemByNameAsync(site.BatteryType, networkId);
                    result.RequiredQuantities[site.BatteryType] = 1;
                    result.AvailableQuantities[site.BatteryType] = batteryItem?.Quantity ?? 0;

                    // إضافة معلومات للعرض فقط (لا يمنع الحفظ)
                    if (batteryItem == null)
                    {
                        result.InsufficientItems.Add($"البطارية: {site.BatteryType} - غير موجودة في المخزون");
                    }
                    else if (batteryItem.Quantity < 1)
                    {
                        result.InsufficientItems.Add($"البطارية: {site.BatteryType} - الكمية المتاحة: {batteryItem.Quantity}");
                    }
                }

                // التحقق من القواعد - دعم القيم السالبة
                if (!string.IsNullOrEmpty(site.BaseType) && site.BaseCount.HasValue && site.BaseCount > 0)
                {
                    var baseItem = await FindInventoryItemByNameAsync(site.BaseType, networkId);
                    result.RequiredQuantities[site.BaseType] = site.BaseCount.Value;
                    result.AvailableQuantities[site.BaseType] = baseItem?.Quantity ?? 0;

                    if (baseItem == null)
                    {
                        result.InsufficientItems.Add($"القواعد: {site.BaseType} - غير موجودة في المخزون");
                    }
                    else if (baseItem.Quantity < site.BaseCount.Value)
                    {
                        result.InsufficientItems.Add($"القواعد: {site.BaseType} - المطلوب: {site.BaseCount} قطعة، المتاح: {baseItem.Quantity} قطعة");
                    }
                }

                // التحقق من الصناديق - دعم القيم السالبة
                if (!string.IsNullOrEmpty(site.BoxType) && site.BoxCount.HasValue && site.BoxCount > 0)
                {
                    var boxItem = await FindInventoryItemByNameAsync(site.BoxType, networkId);
                    result.RequiredQuantities[site.BoxType] = site.BoxCount.Value;
                    result.AvailableQuantities[site.BoxType] = boxItem?.Quantity ?? 0;

                    if (boxItem == null)
                    {
                        result.InsufficientItems.Add($"الصناديق: {site.BoxType} - غير موجودة في المخزون");
                    }
                    else if (boxItem.Quantity < site.BoxCount.Value)
                    {
                        result.InsufficientItems.Add($"الصناديق: {site.BoxType} - المطلوب: {site.BoxCount} قطعة، المتاح: {boxItem.Quantity} قطعة");
                    }
                }

                // إزالة فحص أسلاك الشبكة والكهرباء - لم تعد مطلوبة

                // التحقق من العناصر الإضافية
                if (!string.IsNullOrEmpty(site.AdditionalInventoryItems))
                {
                    try
                    {
                        var additionalItems = JsonSerializer.Deserialize<List<AdditionalInventoryItem>>(site.AdditionalInventoryItems);
                        if (additionalItems != null)
                        {
                            foreach (var item in additionalItems)
                            {
                                var inventoryItem = context.Inventory != null ? await context.Inventory
                                    .Where(i => i.Id == item.InventoryId)
                                    .Where(i => networkId == null || i.NetworkId == networkId)
                                    .FirstOrDefaultAsync() : null;

                                var itemName = inventoryItem?.Name ?? "عنصر غير معروف";
                                result.RequiredQuantities[itemName] = item.Quantity;
                                result.AvailableQuantities[itemName] = inventoryItem?.Quantity ?? 0;

                                if (inventoryItem == null)
                                {
                                    result.InsufficientItems.Add($"{itemName} - غير موجود في المخزون");
                                }
                                else if (inventoryItem.Quantity < item.Quantity)
                                {
                                    result.InsufficientItems.Add($"{itemName} - المطلوب: {item.Quantity}، المتاح: {inventoryItem.Quantity}");
                                }
                            }
                        }
                    }
                    catch (JsonException)
                    {
                        // تجاهل أخطاء JSON
                    }
                }

                // السماح بالحفظ دائماً (دعم القيم السالبة)
                result.HasSufficientStock = true;
                return result;
            }
            finally
            {
                scope.Dispose();
            }
        }

        public async Task<SiteInventoryDeductionResult> DeductInventoryForNewSiteAsync(Site site, string? networkId = null)
        {
            var result = new SiteInventoryDeductionResult();
            var scope = _serviceProvider.CreateScope();
            
            try
            {
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();
                
                // خصم البطارية
                if (!string.IsNullOrEmpty(site.BatteryType))
                {
                    var success = await DeductInventoryItemAsync(context, site.BatteryType, 1, networkId);
                    if (success)
                        result.DeductedItems.Add($"البطارية: {site.BatteryType} - 1 قطعة");
                }

                // خصم القواعد
                if (!string.IsNullOrEmpty(site.BaseType) && site.BaseCount.HasValue && site.BaseCount > 0)
                {
                    var success = await DeductInventoryItemAsync(context, site.BaseType, site.BaseCount.Value, networkId);
                    if (success)
                        result.DeductedItems.Add($"القواعد: {site.BaseType} - {site.BaseCount} قطعة");
                }

                // خصم الصناديق
                if (!string.IsNullOrEmpty(site.BoxType) && site.BoxCount.HasValue && site.BoxCount > 0)
                {
                    var success = await DeductInventoryItemAsync(context, site.BoxType, site.BoxCount.Value, networkId);
                    if (success)
                        result.DeductedItems.Add($"الصناديق: {site.BoxType} - {site.BoxCount} قطعة");
                }

                // إزالة خصم أسلاك الشبكة والكهرباء - لم تعد مطلوبة

                // خصم العناصر الإضافية
                if (!string.IsNullOrEmpty(site.AdditionalInventoryItems))
                {
                    await DeductAdditionalInventoryItemsAsync(context, site.AdditionalInventoryItems, result, networkId);
                }

                await context.SaveChangesAsync();
                result.Success = true;
                return result;
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = $"خطأ في خصم المخزون: {ex.Message}";
                return result;
            }
            finally
            {
                scope.Dispose();
            }
        }

        public async Task<SiteInventoryDeductionResult> UpdateInventoryForSiteModificationAsync(Site oldSite, Site newSite, string? networkId = null)
        {
            var result = new SiteInventoryDeductionResult();
            var scope = _serviceProvider.CreateScope();
            
            try
            {
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();
                
                // إرجاع الكميات القديمة
                await RestoreInventoryForSiteAsync(context, oldSite, networkId);
                
                // خصم الكميات الجديدة
                var deductResult = await DeductInventoryForNewSiteAsync(newSite, networkId);
                
                if (!deductResult.Success)
                {
                    // إذا فشل الخصم، نعيد الكميات القديمة
                    await RestoreInventoryForSiteAsync(context, oldSite, networkId);
                    return deductResult;
                }

                result.Success = true;
                result.DeductedItems = deductResult.DeductedItems;
                return result;
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = $"خطأ في تحديث المخزون: {ex.Message}";
                return result;
            }
            finally
            {
                scope.Dispose();
            }
        }

        public async Task<SiteInventoryDeductionResult> RestoreInventoryForDeletedSiteAsync(Site site, string? networkId = null)
        {
            var result = new SiteInventoryDeductionResult();
            var scope = _serviceProvider.CreateScope();
            
            try
            {
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();
                await RestoreInventoryForSiteAsync(context, site, networkId);
                await context.SaveChangesAsync();
                
                result.Success = true;
                return result;
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = $"خطأ في إرجاع المخزون: {ex.Message}";
                return result;
            }
            finally
            {
                scope.Dispose();
            }
        }

        public async Task<List<string>> GetAvailableBatteryTypesAsync(string? networkId = null)
        {
            return await GetAvailableItemsByCategory("أدوات", networkId);
        }

        public async Task<List<string>> GetAvailableBaseTypesAsync(string? networkId = null)
        {
            return await GetAvailableItemsByCategory("أدوات", networkId);
        }

        public async Task<List<string>> GetAvailableBoxTypesAsync(string? networkId = null)
        {
            return await GetAvailableItemsByCategory("أدوات", networkId);
        }

        // إزالة دوال أسلاك الشبكة والكهرباء - لم تعد مطلوبة

        public async Task<List<Inventory>> GetAvailableInventoryItemsAsync(string? networkId = null)
        {
            var scope = _serviceProvider.CreateScope();
            
            try
            {
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();
                
                var query = context.Inventory?.AsQueryable();
                if (query == null) return new List<Inventory>();

                if (!string.IsNullOrEmpty(networkId))
                {
                    query = query.Where(i => i.NetworkId == networkId);
                }

                return await query
                    .OrderBy(i => i.Category)
                    .ThenBy(i => i.Name)
                    .ToListAsync();
            }
            finally
            {
                scope.Dispose();
            }
        }

        private async Task<Inventory?> FindInventoryItemByNameAsync(string itemName, string? networkId = null)
        {
            var scope = _serviceProvider.CreateScope();

            try
            {
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                var query = context.Inventory?.AsQueryable();
                if (query == null) return null;

                if (!string.IsNullOrEmpty(networkId))
                {
                    query = query.Where(i => i.NetworkId == networkId);
                }

                return await query
                    .Where(i => i.Name == itemName)
                    .FirstOrDefaultAsync();
            }
            finally
            {
                scope.Dispose();
            }
        }

        private async Task<List<string>> GetAvailableItemsByCategory(string category, string? networkId = null)
        {
            var scope = _serviceProvider.CreateScope();

            try
            {
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                var query = context.Inventory?.AsQueryable();
                if (query == null) return new List<string>();

                if (!string.IsNullOrEmpty(networkId))
                {
                    query = query.Where(i => i.NetworkId == networkId);
                }

                // إزالة شرط الكمية لدعم القيم السالبة - عرض جميع العناصر
                return await query
                    .Where(i => i.Category == category)
                    .Select(i => i.Name)
                    .Distinct()
                    .OrderBy(name => name)
                    .ToListAsync();
            }
            finally
            {
                scope.Dispose();
            }
        }

        private async Task<bool> DeductInventoryItemAsync(NetworkDbContext context, string itemName, int quantity, string? networkId = null)
        {
            if (context.Inventory == null) return false;

            var item = await context.Inventory
                .Where(i => i.Name == itemName)
                .Where(i => networkId == null || i.NetworkId == networkId)
                .FirstOrDefaultAsync();

            if (item != null)
            {
                var oldQuantity = item.Quantity;
                item.Quantity -= quantity;
                item.LastUpdated = DateTime.Now;
                item.UpdatedAt = DateTime.Now;

                System.Diagnostics.Debug.WriteLine($"[SiteInventoryService] خصم عنصر أساسي - {itemName} (ID: {item.Id}): {oldQuantity} -> {item.Quantity} (خصم: {quantity})");
                return true;
            }

            System.Diagnostics.Debug.WriteLine($"[SiteInventoryService] لم يتم العثور على العنصر الأساسي - {itemName}");
            return false;
        }

        private async System.Threading.Tasks.Task RestoreInventoryForSiteAsync(NetworkDbContext context, Site site, string? networkId = null)
        {
            // إرجاع البطارية
            if (!string.IsNullOrEmpty(site.BatteryType))
            {
                await RestoreInventoryItemAsync(context, site.BatteryType, 1, networkId);
            }

            // إرجاع القواعد
            if (!string.IsNullOrEmpty(site.BaseType) && site.BaseCount.HasValue && site.BaseCount > 0)
            {
                await RestoreInventoryItemAsync(context, site.BaseType, site.BaseCount.Value, networkId);
            }

            // إرجاع الصناديق
            if (!string.IsNullOrEmpty(site.BoxType) && site.BoxCount.HasValue && site.BoxCount > 0)
            {
                await RestoreInventoryItemAsync(context, site.BoxType, site.BoxCount.Value, networkId);
            }

            // إزالة إرجاع أسلاك الشبكة والكهرباء - لم تعد مطلوبة

            // إرجاع العناصر الإضافية
            if (!string.IsNullOrEmpty(site.AdditionalInventoryItems))
            {
                await RestoreAdditionalInventoryItemsAsync(context, site.AdditionalInventoryItems, networkId);
            }
        }



        private async System.Threading.Tasks.Task RestoreInventoryItemAsync(NetworkDbContext context, string itemName, int quantity, string? networkId = null)
        {
            if (context.Inventory == null) return;

            var item = await context.Inventory
                .Where(i => i.Name == itemName)
                .Where(i => networkId == null || i.NetworkId == networkId)
                .FirstOrDefaultAsync();

            if (item != null)
            {
                item.Quantity += quantity;
                item.LastUpdated = DateTime.Now;
                item.UpdatedAt = DateTime.Now;
            }
        }

        private async System.Threading.Tasks.Task DeductAdditionalInventoryItemsAsync(NetworkDbContext context, string additionalItemsJson, SiteInventoryDeductionResult result, string? networkId = null)
        {
            try
            {
                var additionalItems = JsonSerializer.Deserialize<List<AdditionalInventoryItem>>(additionalItemsJson);
                if (additionalItems != null)
                {
                    System.Diagnostics.Debug.WriteLine($"[SiteInventoryService] خصم العناصر الإضافية - عدد العناصر: {additionalItems.Count}");

                    foreach (var item in additionalItems)
                    {
                        var inventoryItem = context.Inventory != null ? await context.Inventory
                            .Where(i => i.Id == item.InventoryId)
                            .Where(i => networkId == null || i.NetworkId == networkId)
                            .FirstOrDefaultAsync() : null;

                        if (inventoryItem != null)
                        {
                            var oldQuantity = inventoryItem.Quantity;
                            inventoryItem.Quantity -= item.Quantity;
                            inventoryItem.LastUpdated = DateTime.Now;
                            inventoryItem.UpdatedAt = DateTime.Now;
                            result.DeductedItems.Add($"{inventoryItem.Name} - {item.Quantity} {inventoryItem.Unit ?? "قطعة"}");

                            System.Diagnostics.Debug.WriteLine($"[SiteInventoryService] خصم عنصر إضافي - {inventoryItem.Name} (ID: {item.InventoryId}): {oldQuantity} -> {inventoryItem.Quantity} (خصم: {item.Quantity})");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"[SiteInventoryService] لم يتم العثور على العنصر الإضافي - ID: {item.InventoryId}");
                        }
                    }
                }
            }
            catch (JsonException ex)
            {
                System.Diagnostics.Debug.WriteLine($"[SiteInventoryService] خطأ في تحليل JSON للعناصر الإضافية: {ex.Message}");
            }
        }

        private async System.Threading.Tasks.Task RestoreAdditionalInventoryItemsAsync(NetworkDbContext context, string additionalItemsJson, string? networkId = null)
        {
            try
            {
                var additionalItems = JsonSerializer.Deserialize<List<AdditionalInventoryItem>>(additionalItemsJson);
                if (additionalItems != null)
                {
                    foreach (var item in additionalItems)
                    {
                        var inventoryItem = context.Inventory != null ? await context.Inventory
                            .Where(i => i.Id == item.InventoryId)
                            .Where(i => networkId == null || i.NetworkId == networkId)
                            .FirstOrDefaultAsync() : null;

                        if (inventoryItem != null)
                        {
                            inventoryItem.Quantity += item.Quantity;
                            inventoryItem.LastUpdated = DateTime.Now;
                            inventoryItem.UpdatedAt = DateTime.Now;
                        }
                    }
                }
            }
            catch (JsonException)
            {
                // تجاهل أخطاء JSON
            }
        }
    }

    public class AdditionalInventoryItem
    {
        public string InventoryId { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public int Quantity { get; set; }
    }
}
