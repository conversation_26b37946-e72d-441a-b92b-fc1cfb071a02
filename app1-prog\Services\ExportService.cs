using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Win32;
using NetworkManagement.Models;

namespace NetworkManagement.Services
{
    public interface IExportService
    {
        Task<bool> ExportToCsvAsync(IEnumerable<Device> devices, string? filePath = null);
        Task<bool> ExportToExcelAsync(IEnumerable<Device> devices, string? filePath = null);
        string GetDefaultFileName(string format);
    }

    public class ExportService : IExportService
    {
        public async Task<bool> ExportToCsvAsync(IEnumerable<Device> devices, string? filePath = null)
        {
            try
            {
                if (string.IsNullOrEmpty(filePath))
                {
                    var saveDialog = new SaveFileDialog
                    {
                        Filter = "CSV files (*.csv)|*.csv",
                        DefaultExt = "csv",
                        FileName = GetDefaultFileName("csv")
                    };

                    if (saveDialog.ShowDialog() != true)
                        return false;

                    filePath = saveDialog.FileName;
                }

                var csv = new StringBuilder();

                // Headers
                csv.AppendLine("الحالة,المسؤول,رقم الهاتف,عنوان IP,مكان الجهاز,الشبكة (الملكية),إسم شبكة البث,الموقع,النوع,تاريخ التركيب,القناة,آخر فحص,طول سلك الشبكة,طول سلك الكهرباء,طريقة الربط,الشبكة المرتبطة,مصدر الطاقة,نوع المحول,عدد الأجهزة المتصلة");

                foreach (var device in devices)
                {
                    csv.AppendLine($"\"{device.StatusDisplay}\"," +
                                 $"\"{device.Responsible ?? ""}\"," +
                                 $"\"{device.Phone ?? ""}\"," +
                                 $"\"{device.Ip ?? ""}\"," +
                                 $"\"{device.Location ?? ""}\"," +
                                 $"\"{device.Network?.Name ?? ""}\"," +
                                 $"\"{device.BroadcastNetworkName ?? ""}\"," +
                                 $"\"{device.Site?.Name ?? ""}\"," +
                                 $"\"{device.Type ?? ""}\"," +
                                 $"\"{device.InstallDateDisplay}\"," +
                                 $"\"{device.ChannelDisplay}\"," +
                                 $"\"{device.LastCheckDisplay}\"," +
                                 $"\"{device.NetworkCableLengthDisplay}\"," +
                                 $"\"{device.PowerCableLengthDisplay}\"," +
                                 $"\"{device.ConnectionMethod ?? ""}\"," +
                                 $"\"{device.LinkedNetwork ?? ""}\"," +
                                 $"\"{device.PowerConnection ?? ""}\"," +
                                 $"\"{device.AdapterType ?? ""}\"," +
                                 $"\"{device.ConnectedDevicesDisplay}\"");
                }

                await File.WriteAllTextAsync(filePath, csv.ToString(), Encoding.UTF8);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> ExportToExcelAsync(IEnumerable<Device> devices, string? filePath = null)
        {
            try
            {
                if (string.IsNullOrEmpty(filePath))
                {
                    var saveDialog = new SaveFileDialog
                    {
                        Filter = "Excel files (*.xlsx)|*.xlsx",
                        DefaultExt = "xlsx",
                        FileName = GetDefaultFileName("xlsx")
                    };

                    if (saveDialog.ShowDialog() != true)
                        return false;

                    filePath = saveDialog.FileName;
                }

                // For now, we'll export as CSV with .xlsx extension
                // In a real implementation, you'd use a library like EPPlus or ClosedXML
                return await ExportToCsvAsync(devices, filePath);
            }
            catch
            {
                return false;
            }
        }

        public string GetDefaultFileName(string format)
        {
            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            return $"devices_export_{timestamp}.{format}";
        }
    }
}
