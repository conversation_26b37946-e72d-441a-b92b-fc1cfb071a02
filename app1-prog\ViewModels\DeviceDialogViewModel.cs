using System;
using System.Collections.Generic;
using System.Linq;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using NetworkManagement.Helpers;
using NetworkManagement.Models;
using NetworkManagement.Services;

namespace NetworkManagement.ViewModels
{
    public partial class DeviceDialogViewModel : ObservableObject
    {
        private readonly IDeviceService _deviceService;
        private readonly ISiteService _siteService;
        private readonly IAuthService _authService;
        private readonly INetworkService _networkService;
        private readonly INotificationService _notificationService;
        private readonly IDeviceInventoryService _deviceInventoryService;

        [ObservableProperty]
        private string title = "إضافة جهاز جديد";

        [ObservableProperty]
        private string responsible = string.Empty;

        [ObservableProperty]
        private string type = string.Empty;

        [ObservableProperty]
        private string ipAddress = string.Empty;

        [ObservableProperty]
        private string location = string.Empty;

        [ObservableProperty]
        private string phone = string.Empty;

        [ObservableProperty]
        private string status = "نشط";

        [ObservableProperty]
        private string selectedNetworkId = string.Empty;

        [ObservableProperty]
        private List<Network> availableNetworks = new();

        [ObservableProperty]
        private Site? selectedSite;

        [ObservableProperty]
        private DateTime? installDate;

        [ObservableProperty]
        private string connectionMethod = string.Empty;

        [ObservableProperty]
        private string linkedNetwork = string.Empty;

        [ObservableProperty]
        private string channel = string.Empty;

        [ObservableProperty]
        private string connectedDevices = string.Empty;

        [ObservableProperty]
        private string networkCableLength = string.Empty;

        [ObservableProperty]
        private string powerCableLength = string.Empty;

        [ObservableProperty]
        private string installedAdapterType = string.Empty;

        [ObservableProperty]
        private string networkCableType = string.Empty;

        [ObservableProperty]
        private string powerCableType = string.Empty;

        [ObservableProperty]
        private bool isLoading = false;

        [ObservableProperty]
        private string errorMessage = string.Empty;

        public List<Site> Sites { get; private set; } = new();

        [ObservableProperty]
        private List<string> deviceTypes = new();

        [ObservableProperty]
        private List<string> adapterTypes = new();

        [ObservableProperty]
        private List<string> networkCableTypes = new();

        [ObservableProperty]
        private List<string> powerCableTypes = new();

        [ObservableProperty]
        private string inventoryStatusMessage = string.Empty;

        public string[] StatusOptions { get; } = { "نشط", "غير نشط", "صيانة", "معطل" };

        // تخزين مؤقت للمواقع (instance-based بدلاً من static)
        private List<Site>? _cachedSites;
        private DateTime _lastCacheUpdate = DateTime.MinValue;
        private static readonly TimeSpan CacheExpiry = TimeSpan.FromMinutes(5);

        private Device? _editingDevice;
        public bool IsEditMode => _editingDevice != null;

        public event EventHandler<Device>? DeviceSaved;
        public event EventHandler? DialogClosed;
        public event Action? RequestClose;

        public bool? DialogResult { get; private set; }

        public DeviceDialogViewModel(IDeviceService deviceService, ISiteService siteService, IAuthService authService, INetworkService networkService, INotificationService notificationService, IDeviceInventoryService deviceInventoryService)
        {
            _deviceService = deviceService;
            _siteService = siteService;
            _authService = authService;
            _networkService = networkService;
            _notificationService = notificationService;
            _deviceInventoryService = deviceInventoryService;
        }

        public async System.Threading.Tasks.Task InitializeAsync(Device? device = null)
        {
            try
            {
                IsLoading = true;
                ErrorMessage = string.Empty;

                // تحميل البيانات
                await LoadSitesAsync();
                await LoadNetworksAsync();
                await LoadDeviceTypesAsync();
                await LoadInventoryOptionsAsync();

                // إذا تم تمرير جهاز للتعديل
                if (device != null)
                {
                    SetEditDevice(device);
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = $"خطأ في تحميل البيانات: {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"DeviceDialog: Error initializing: {ex}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        public void SetEditDevice(Device device)
        {
            _editingDevice = device;
            Title = "تحرير الجهاز";

            Responsible = device.Responsible ?? string.Empty;
            Type = device.Type ?? string.Empty;
            IpAddress = device.Ip ?? string.Empty;
            Location = device.Location ?? string.Empty;
            Phone = device.Phone ?? string.Empty;
            Status = device.Status ?? "active";
            SelectedNetworkId = device.NetworkId ?? string.Empty;
            InstallDate = device.InstallDate;
            ConnectionMethod = device.ConnectionMethod ?? string.Empty;
            LinkedNetwork = device.LinkedNetwork ?? string.Empty;
            Channel = device.Channel?.ToString() ?? string.Empty;
            ConnectedDevices = device.ConnectedDevices?.ToString() ?? string.Empty;
            NetworkCableLength = device.NetworkCableLength?.ToString() ?? string.Empty;
            PowerCableLength = device.PowerCableLength?.ToString() ?? string.Empty;
            InstalledAdapterType = device.InstalledAdapterType ?? string.Empty;
            NetworkCableType = device.NetworkCableType ?? string.Empty;
            PowerCableType = device.PowerCableType ?? string.Empty;

            // Set selected site
            if (!string.IsNullOrEmpty(device.SiteId))
            {
                SelectedSite = Sites.FirstOrDefault(s => s.Id == device.SiteId);
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task SaveAsync()
        {
            try
            {
                if (!await ValidateInputAsync())
                    return;

                IsLoading = true;
                ErrorMessage = string.Empty;



                var device = _editingDevice ?? new Device();

                // تحديث بيانات الجهاز
                UpdateDeviceFromInput(device);

                // حفظ نوع الجهاز المخصص إذا كان جديداً
                if (!string.IsNullOrWhiteSpace(device.Type) && !DeviceTypes.Contains(device.Type))
                {
                    await _deviceService.SaveCustomDeviceTypeAsync(device.Type);
                }

                // التحقق من صحة NetworkId إذا كان مطلوباً
                if (!string.IsNullOrEmpty(ErrorMessage))
                {
                    return; // خطأ في تحديد الشبكة
                }

                // Parse numeric fields
                // تحويل آمن للحقول الرقمية مع تنظيف البيانات
                device.Channel = ParseIntegerSafely(Channel);
                device.ConnectedDevices = ParseIntegerSafely(ConnectedDevices);
                device.NetworkCableLength = ParseIntegerSafely(NetworkCableLength);
                device.PowerCableLength = ParseIntegerSafely(PowerCableLength);
                device.InstalledAdapterType = string.IsNullOrWhiteSpace(InstalledAdapterType) ? null : InstalledAdapterType.Trim();
                device.NetworkCableType = string.IsNullOrWhiteSpace(NetworkCableType) ? null : NetworkCableType.Trim();
                device.PowerCableType = string.IsNullOrWhiteSpace(PowerCableType) ? null : PowerCableType.Trim();

                if (_editingDevice == null)
                {
                    // Adding new device - خصم المخزون تلقائياً
                    await _deviceService.CreateAsync(device);

                    // خصم المواد من المخزون (يسمح بالسالب)
                    var networkId = _authService.CurrentUser?.NetworkId;
                    var inventoryResult = await _deviceInventoryService.DeductInventoryForNewDeviceAsync(device, networkId);

                    if (inventoryResult.Success && inventoryResult.DeductedItems.Count > 0)
                    {
                        var deductedItemsText = string.Join(", ", inventoryResult.DeductedItems);
                        System.Diagnostics.Debug.WriteLine($"تم خصم المواد من المخزون: {deductedItemsText}");
                    }

                    // إشعار نجاح الإضافة
                    var deviceName = device.Responsible ?? device.Location ?? "الجهاز الجديد";
                    var userName = _authService.CurrentUser?.Name ?? "المستخدم";
                    var userId = _authService.CurrentUser?.Id;
                    await _notificationService.ShowOperationAsync(
                        "تم إضافة الجهاز",
                        $"قام {userName} بإضافة الجهاز: {deviceName}",
                        NotificationType.Success,
                        userName, userId, "إضافة", deviceName, device.NetworkId);
                }
                else
                {
                    // Updating existing device - تحديث المخزون
                    var networkId = _authService.CurrentUser?.NetworkId;
                    var inventoryResult = await _deviceInventoryService.UpdateInventoryForDeviceModificationAsync(_editingDevice, device, networkId);

                    if (inventoryResult.Success && inventoryResult.DeductedItems.Count > 0)
                    {
                        var deductedItemsText = string.Join(", ", inventoryResult.DeductedItems);
                        System.Diagnostics.Debug.WriteLine($"تم تحديث المخزون: {deductedItemsText}");
                    }

                    await _deviceService.UpdateAsync(device);

                    // إشعار نجاح التحديث
                    var deviceName = device.Responsible ?? device.Location ?? "الجهاز";
                    var userName = _authService.CurrentUser?.Name ?? "المستخدم";
                    var userId = _authService.CurrentUser?.Id;
                    await _notificationService.ShowOperationAsync(
                        "تم تحديث الجهاز",
                        $"قام {userName} بتحديث الجهاز: {deviceName}",
                        NotificationType.Success,
                        userName, userId, "تحديث", deviceName, device.NetworkId);
                }

                DeviceSaved?.Invoke(this, device);
                DialogResult = true;
                RequestClose?.Invoke();
                DialogClosed?.Invoke(this, EventArgs.Empty);
            }
            catch (Exception ex)
            {
                var innerMessage = ex.InnerException?.Message ?? "";
                ErrorMessage = $"خطأ في حفظ الجهاز: {ex.Message}";

                // Log detailed error for debugging
                System.Diagnostics.Debug.WriteLine($"Device save error: {ex}");

                // Show more details if available
                if (!string.IsNullOrEmpty(innerMessage))
                {
                    ErrorMessage += $"\n\nتفاصيل إضافية: {innerMessage}";
                }
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private void Cancel()
        {
            DialogResult = false;
            RequestClose?.Invoke();
            DialogClosed?.Invoke(this, EventArgs.Empty);
        }

        private async System.Threading.Tasks.Task LoadSitesAsync()
        {
            try
            {
                // التحقق من صلاحية التخزين المؤقت
                if (_cachedSites != null && DateTime.Now - _lastCacheUpdate < CacheExpiry)
                {
                    Sites.Clear();
                    Sites.AddRange(_cachedSites);
                    return;
                }

                // تحميل البيانات من قاعدة البيانات
                var sites = await _siteService.GetAllAsync();
                var sitesList = sites.ToList();

                // تحديث التخزين المؤقت
                _cachedSites = sitesList;
                _lastCacheUpdate = DateTime.Now;

                Sites.Clear();
                Sites.AddRange(sitesList);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading sites: {ex.Message}");
                // في حالة الخطأ، استخدم التخزين المؤقت إن وجد
                if (_cachedSites != null)
                {
                    Sites.Clear();
                    Sites.AddRange(_cachedSites);
                }
            }
        }

        private async System.Threading.Tasks.Task<bool> ValidateInputAsync()
        {
            ErrorMessage = string.Empty;

            if (string.IsNullOrWhiteSpace(Responsible))
            {
                ErrorMessage = "المسؤول مطلوب";
                return false;
            }

            // Validate IP Address format if provided
            if (!string.IsNullOrWhiteSpace(IpAddress))
            {
                var cleanIp = IpAddress.Trim();
                if (!System.Net.IPAddress.TryParse(cleanIp, out _))
                {
                    ErrorMessage = "عنوان IP غير صحيح";
                    return false;
                }

                // التحقق من تكرار عنوان IP
                if (await IsIpDuplicateAsync(cleanIp))
                {
                    ErrorMessage = "عنوان IP مستخدم بالفعل من قبل جهاز آخر";
                    return false;
                }
            }

            // Validate Phone format if provided
            if (!string.IsNullOrWhiteSpace(Phone))
            {
                var cleanPhone = Phone.Trim();
                if (cleanPhone.Length < 7)
                {
                    ErrorMessage = "رقم الهاتف قصير جداً (يجب أن يكون 7 أرقام على الأقل)";
                    return false;
                }

                // التحقق من أن رقم الهاتف يحتوي على أرقام فقط أو علامات مقبولة
                if (!System.Text.RegularExpressions.Regex.IsMatch(cleanPhone, @"^[\d\s\-\+\(\)]+$"))
                {
                    ErrorMessage = "رقم الهاتف يحتوي على أحرف غير صالحة";
                    return false;
                }
            }

            // التحقق من صحة تاريخ التركيب
            if (InstallDate.HasValue && InstallDate.Value.Date > DateTime.Now.Date)
            {
                ErrorMessage = "تاريخ التركيب لا يمكن أن يكون في المستقبل";
                return false;
            }

            // Validate numeric fields
            if (!ValidateNumericField(Channel, "القناة"))
                return false;

            if (!ValidateNumericField(ConnectedDevices, "عدد الأجهزة المتصلة"))
                return false;

            if (!ValidateNumericField(NetworkCableLength, "طول سلك الشبكة"))
                return false;

            if (!ValidateNumericField(PowerCableLength, "طول سلك الكهرباء"))
                return false;

            return true;
        }

        private bool ValidateNumericField(string? value, string fieldName)
        {
            if (string.IsNullOrWhiteSpace(value))
                return true; // الحقول الرقمية اختيارية

            var cleanValue = value.Trim();

            // التحقق من أن القيمة رقمية
            if (!int.TryParse(cleanValue, out var numericValue))
            {
                ErrorMessage = $"{fieldName} يجب أن يكون رقم صحيح";
                return false;
            }

            // التحقق من أن القيمة غير سالبة (السماح بالصفر)
            if (numericValue < 0)
            {
                ErrorMessage = $"{fieldName} يجب أن يكون رقم غير سالب";
                return false;
            }

            return true;
        }

        private async System.Threading.Tasks.Task LoadNetworksAsync()
        {
            try
            {
                // استخدام NetworkHelper لتحميل الشبكات مع تطبيق الصلاحيات
                var (networks, defaultNetworkId) = await NetworkHelper.LoadNetworksForSelectionAsync(
                    _authService,
                    _networkService);

                AvailableNetworks = networks;

                // تعيين الشبكة الافتراضية
                if (!string.IsNullOrEmpty(defaultNetworkId))
                {
                    SelectedNetworkId = defaultNetworkId;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"DeviceDialog: Error loading networks: {ex.Message}");
                ErrorMessage = $"خطأ في تحميل الشبكات: {ex.Message}";
            }
        }

        /// <summary>
        /// تحديد NetworkId المناسب حسب الصلاحيات والاختيار
        /// </summary>
        /// <returns>NetworkId المحدد أو null</returns>
        private string? DetermineNetworkId()
        {
            // إذا تم اختيار شبكة محددة، استخدمها
            if (!string.IsNullOrWhiteSpace(SelectedNetworkId))
            {
                return SelectedNetworkId;
            }

            var currentUser = _authService.CurrentUser;

            // للأدمن: يمكن ترك الحقل فارغ (اختياري)
            if (_authService.IsAdmin)
            {
                return null;
            }

            // للـ Network Manager: استخدم شبكته تلقائياً
            if (_authService.IsNetworkManager && !string.IsNullOrEmpty(currentUser?.NetworkId))
            {
                return currentUser.NetworkId;
            }

            // لباقي المستخدمين: يجب اختيار شبكة
            ErrorMessage = "يجب اختيار شبكة للجهاز";
            return null;
        }

        /// <summary>
        /// التحقق من تكرار عنوان IP في النظام
        /// </summary>
        /// <param name="ipAddress">عنوان IP للتحقق منه</param>
        /// <returns>true إذا كان العنوان مكرر، false إذا لم يكن مكرر</returns>
        private async System.Threading.Tasks.Task<bool> IsIpDuplicateAsync(string ipAddress)
        {
            try
            {
                // البحث عن جهاز بعنوان IP محدد
                var existingDevice = await _deviceService.GetDeviceByIpAsync(ipAddress);

                // إذا لم يتم العثور على جهاز، فالعنوان غير مكرر
                if (existingDevice == null)
                    return false;

                // إذا كنا في وضع التعديل وهذا هو نفس الجهاز، فالعنوان غير مكرر
                if (_editingDevice != null && existingDevice.Id == _editingDevice.Id)
                    return false;

                // العنوان مكرر
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error checking IP duplicate: {ex.Message}");
                // في حالة الخطأ، نسمح بالمتابعة لتجنب منع المستخدم من الحفظ
                return false;
            }
        }



        /// <summary>
        /// تحميل أنواع الأجهزة من المخزون (فئة أجهزة)
        /// </summary>
        private async System.Threading.Tasks.Task LoadDeviceTypesAsync()
        {
            try
            {
                var networkId = _authService.CurrentUser?.NetworkId;

                // تحميل أنواع الأجهزة من المخزون (فئة أجهزة)
                var inventoryTypes = await _deviceInventoryService.GetAvailableDeviceTypesAsync(networkId);

                // إضافة الأنواع الافتراضية
                var defaultTypes = new List<string> { "Router", "Switch", "Access Point", "Firewall", "Server", "أخرى" };

                // دمج الأنواع من المخزون مع الافتراضية
                var allTypes = new List<string>();
                allTypes.AddRange(defaultTypes);
                allTypes.AddRange(inventoryTypes.Where(t => !defaultTypes.Contains(t)));

                DeviceTypes = allTypes.Distinct().OrderBy(t => t).ToList();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading device types: {ex.Message}");
                // في حالة الخطأ، استخدام الأنواع الافتراضية
                DeviceTypes = new List<string> { "Router", "Switch", "Access Point", "Firewall", "Server", "أخرى" };
            }
        }

        /// <summary>
        /// تحميل خيارات المخزون المتاحة
        /// </summary>
        private async System.Threading.Tasks.Task LoadInventoryOptionsAsync()
        {
            try
            {
                var networkId = _authService.CurrentUser?.NetworkId;

                // تحميل أنواع المحولات المتاحة في المخزون
                var adapters = await _deviceInventoryService.GetAvailableAdapterTypesAsync(networkId);
                AdapterTypes = adapters;

                // تحميل أنواع أسلاك الشبكة المتاحة في المخزون
                var cables = await _deviceInventoryService.GetAvailableNetworkCableTypesAsync(networkId);
                NetworkCableTypes = cables;

                // تحميل أنواع أسلاك الكهرباء المتاحة في المخزون
                var powerCables = await _deviceInventoryService.GetAvailablePowerCableTypesAsync(networkId);
                PowerCableTypes = powerCables;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading inventory options: {ex.Message}");
                AdapterTypes = new List<string>();
                NetworkCableTypes = new List<string>();
                PowerCableTypes = new List<string>();
            }
        }

        /// <summary>
        /// فحص حالة المخزون وعرض المعلومات للمستخدم
        /// </summary>
        private async System.Threading.Tasks.Task CheckInventoryStatusAsync()
        {
            try
            {
                var device = CreateDeviceFromInput();
                var checkResult = await _deviceInventoryService.CheckInventoryAvailabilityAsync(device, device.NetworkId);

                if (checkResult.InsufficientItems.Any())
                {
                    var statusMessages = new List<string>();
                    statusMessages.Add("معلومات المخزون:");
                    statusMessages.AddRange(checkResult.InsufficientItems);
                    statusMessages.Add("ملاحظة: يمكن الحفظ حتى لو كانت الكميات غير كافية (دعم القيم السالبة)");

                    InventoryStatusMessage = string.Join("\n", statusMessages);
                }
                else
                {
                    InventoryStatusMessage = "جميع المواد متوفرة في المخزون";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error checking inventory status: {ex.Message}");
                InventoryStatusMessage = string.Empty;
            }
        }

        /// <summary>
        /// تحويل آمن للنصوص إلى أرقام مع تنظيف البيانات
        /// </summary>
        private static int? ParseIntegerSafely(string? value)
        {
            if (string.IsNullOrWhiteSpace(value))
                return null;

            // تنظيف النص من المسافات والأحرف غير المرغوبة
            var cleanValue = value.Trim();

            // إزالة أي أحرف غير رقمية
            cleanValue = System.Text.RegularExpressions.Regex.Replace(cleanValue, @"[^\d]", "");

            if (string.IsNullOrEmpty(cleanValue))
                return null;

            // محاولة التحويل (السماح بالصفر)
            if (int.TryParse(cleanValue, out var result) && result >= 0)
                return result;

            return null;
        }

        /// <summary>
        /// إنشاء كائن جهاز من البيانات المدخلة للتحقق من المخزون
        /// </summary>
        private Device CreateDeviceFromInput()
        {
            var device = new Device();
            UpdateDeviceFromInput(device);
            return device;
        }

        /// <summary>
        /// تحديث كائن الجهاز من البيانات المدخلة
        /// </summary>
        private void UpdateDeviceFromInput(Device device)
        {
            device.Responsible = string.IsNullOrWhiteSpace(Responsible) ? null : Responsible.Trim();
            device.Type = string.IsNullOrWhiteSpace(Type) ? null : Type.Trim();
            device.Ip = string.IsNullOrWhiteSpace(IpAddress) ? null : IpAddress.Trim();
            device.Location = string.IsNullOrWhiteSpace(Location) ? null : Location.Trim();
            device.Phone = string.IsNullOrWhiteSpace(Phone) ? null : Phone.Trim();
            device.Status = Status ?? "نشط";
            device.InstallDate = InstallDate;
            device.ConnectionMethod = string.IsNullOrWhiteSpace(ConnectionMethod) ? null : ConnectionMethod.Trim();
            device.LinkedNetwork = string.IsNullOrWhiteSpace(LinkedNetwork) ? null : LinkedNetwork.Trim();
            device.InstalledAdapterType = string.IsNullOrWhiteSpace(InstalledAdapterType) ? null : InstalledAdapterType.Trim();
            device.NetworkCableType = string.IsNullOrWhiteSpace(NetworkCableType) ? null : NetworkCableType.Trim();
            device.PowerCableType = string.IsNullOrWhiteSpace(PowerCableType) ? null : PowerCableType.Trim();

            // تعيين NetworkId باستخدام منطق موحد
            device.NetworkId = DetermineNetworkId();
            device.SiteId = SelectedSite?.Id;

            // Parse numeric fields
            device.Channel = ParseIntegerSafely(Channel);
            device.ConnectedDevices = ParseIntegerSafely(ConnectedDevices);
            device.NetworkCableLength = ParseIntegerSafely(NetworkCableLength);
            device.PowerCableLength = ParseIntegerSafely(PowerCableLength);
        }

        // دوال التفاعل مع تغيير البيانات لفحص المخزون
        partial void OnTypeChanged(string value)
        {
            _ = CheckInventoryStatusAsync();
        }

        partial void OnInstalledAdapterTypeChanged(string value)
        {
            _ = CheckInventoryStatusAsync();
        }

        partial void OnNetworkCableTypeChanged(string value)
        {
            _ = CheckInventoryStatusAsync();
        }

        partial void OnNetworkCableLengthChanged(string value)
        {
            _ = CheckInventoryStatusAsync();
        }

        partial void OnPowerCableTypeChanged(string value)
        {
            _ = CheckInventoryStatusAsync();
        }

        partial void OnPowerCableLengthChanged(string value)
        {
            _ = CheckInventoryStatusAsync();
        }
    }
}
