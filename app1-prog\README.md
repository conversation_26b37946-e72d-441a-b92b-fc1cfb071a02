# نظام إدارة الشبكات المتقدم - Shabaka Pro

تطبيق متكامل لإدارة الشبكات والأجهزة بنظام Windows، يقدم حلولاً متقدمة للمؤسسات الصغيرة والمتوسطة.

## المميزات الرئيسية

### 🚀 الإدارة الشاملة
- **لوحة تحكم تفاعلية** مع رسوم بيانية حية
- **مراقبة في الوقت الحقيقي** لحالة الأجهزة والشبكة
- **إشعارات ذكية** تصنف التنبيهات حسب الأولوية

### 🛠 أدوات متقدمة
- **بحث ذكي** بفلترة متعددة المعايير
- **تحديث تلقائي** دون فقدان البيانات
- **نسخ احتياطي** مجدول وقابل للتخصيص

### 🔒 أمان محسن
- **مصادقة متقدمة** مع تحكم دقيق بالصلاحيات
- **تشفير البيانات** الحساسة
- **سجل تدقيق** للتغييرات المهمة

## آلية التثبيت

### التثبيت الاحترافي (مُوصى به)
```bash
# بناء ملف التثبيت
build-shabaka-pro.bat

# تشغيل المثبت
Output/ShabakaPro-Setup-v1.0.0.exe
```

### التشغيل من المصدر
```bash
dotnet restore
dotnet build
dotnet run
```

## نظام التحديثات

يتضمن التطبيق نظام تحديث سلس:
1. **اكتشاف التحديثات** تلقائياً عند التشغيل
2. **تنزيل في الخلفية** دون إزعاج المستخدم
3. **تثبيت صامت** يحافظ على:
   - إعدادات المستخدم
   - قاعدة البيانات
   - التخصيصات

## قاعدة البيانات

يستخدم التطبيق **MySQL** كقاعدة بيانات رئيسية:
```ini
الإعدادات الافتراضية:
- الخادم: localhost
- قاعدة البيانات: shabakaty
- المستخدم: root
```

## التقنيات المستخدمة

- **.NET 6.0** - بيئة التشغيل الأساسية
- **WPF** - واجهات المستخدم
- **Entity Framework Core** - إدارة قواعد البيانات
- **Material Design** - تصميم الواجهات
- **Inno Setup** - حزم التثبيت

## فريق التطوير

تم تطوير النظام بواسطة **فريق شبكة برو** ليكون حلًا متكاملاً لإدارة البنية التحتية التقنية.

## الترخيص

هذا المشروع مفتوح المصدر تحت ترخيص MIT.
