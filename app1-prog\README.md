# نظام إدارة الشبكات المتقدم - Shabaka Pro v2.0.0

## 📋 نظرة عامة

**Shabaka Pro** هو نظام إدارة شبكات متكامل ومتقدم مصمم خصيصاً للمؤسسات والشركات في اليمن. يوفر النظام حلولاً شاملة لإدارة الشبكات، الأجهزة، المواقع، المخزون، والموارد البشرية بواجهة عربية حديثة ونظام صلاحيات متقدم.

### 🎯 الهدف من النظام
- إدارة شاملة للبنية التحتية للشبكات
- تتبع دقيق للأجهزة والمعدات
- إدارة متكاملة للمخزون مع ربط تلقائي
- نظام تقييم الموظفين المتقدم
- تقارير احترافية وتحليلات مفصلة
- دعم العملة اليمنية (ريال يمني)

## ✨ المميزات الرئيسية

### 🏢 إدارة الشبكات المتعددة
- **دعم الشبكات المتعددة** - إدارة عدة شبكات من واجهة واحدة
- **نظام صلاحيات متدرج** - Admin, Manager, Technician, User
- **فصل البيانات** - كل شبكة لها بياناتها المستقلة
- **إدارة المستخدمين** - تحكم كامل في صلاحيات المستخدمين

### 🖥️ إدارة الأجهزة المتقدمة
- **تتبع شامل للأجهزة** - معلومات تفصيلية لكل جهاز
- **مراقبة الحالة** - فحص دوري تلقائي (Ping Service)
- **ربط تلقائي بالمخزون** - خصم المواد عند تركيب الأجهزة
- **إدارة عناوين IP** - تتبع وإدارة عناوين الشبكة
- **تصنيف الأجهزة** - Router, Switch, Access Point, Server, إلخ
- **تواريخ الصيانة** - تتبع تواريخ التركيب والصيانة

### 📍 إدارة المواقع الذكية
- **خرائط تفاعلية** - تحديد المواقع باستخدام GPS
- **ربط تلقائي بالمخزون** - خصم المواد عند إنشاء المواقع
- **إدارة البنية التحتية**:
  - أنظمة البطاريات والطاقة
  - القواعد والصناديق
  - أسلاك الشبكة والكهرباء
  - العناصر الإضافية المخصصة
- **تتبع الأجهزة** - ربط الأجهزة بالمواقع

### � نظام المخزون المتكامل
- **إدارة شاملة للمخزون** - تتبع دقيق للكميات والأسعار
- **فئات متعددة** - تصنيف المواد حسب النوع
- **دعم القيم السالبة** - إمكانية الخصم حتى لو كان المخزون صفر
- **ربط تلقائي**:
  - خصم تلقائي عند تركيب الأجهزة
  - خصم تلقائي عند إنشاء المواقع
  - إرجاع تلقائي عند الحذف أو التعديل
- **تتبع الموردين** - معلومات الموردين والأسعار
- **تنبيهات المخزون** - تحذيرات عند انخفاض الكميات

### 💰 إدارة المشتريات
- **تتبع المشتريات** - سجل كامل للمشتريات
- **ربط بالمخزون** - تحديث تلقائي للمخزون عند الشراء
- **إدارة الموردين** - قاعدة بيانات الموردين
- **تقارير مالية** - تحليل المصروفات والتكاليف
- **دعم العملة اليمنية** - جميع الأسعار بالريال اليمني

### ⭐ نظام تقييم الموظفين
- **تقييم يومي** - نظام تقييم من 1-10 نقاط
- **صلاحيات متدرجة**:
  - Admin: يقيم جميع المستخدمين
  - Manager: يقيم الفنيين في شبكته
  - Technician: يرى تقييماته الشخصية فقط
- **إحصائيات متقدمة** - متوسط النقاط، أعلى وأقل درجة
- **تقارير الأداء** - تحليل أداء الموظفين
- **رسائل تحفيزية** - رسائل مخصصة حسب مستوى الأداء

### 📋 إدارة المهام والأعمال
- **تتبع المهام** - إدارة شاملة للمهام والأعمال
- **تصنيف المهام** - فئات متعددة للمهام
- **تواريخ مرنة** - تاريخ الطلب وتاريخ الإنجاز
- **ربط بالأجهزة والمواقع** - ربط المهام بالعناصر ذات الصلة
- **تتبع الحالة** - حالات متعددة للمهام
- **ترتيب حسب التاريخ** - ترتيب المهام حسب التاريخ

### 📊 لوحة التحكم والإحصائيات
- **لوحة تحكم تفاعلية** - عرض شامل لحالة النظام
- **إحصائيات في الوقت الحقيقي**:
  - عدد الأجهزة النشطة/غير النشطة
  - حالة المواقع والشبكات
  - إحصائيات المخزون
  - أداء الموظفين
- **رسوم بيانية متقدمة** - تمثيل بصري للبيانات
- **تحديث تلقائي** - بيانات محدثة باستمرار

### 📈 نظام التقارير المتقدم
- **تقارير احترافية** - تقارير مفصلة لجميع جوانب النظام
- **تصدير متعدد الصيغ**:
  - PDF - تقارير احترافية للطباعة
  - Excel - للتحليل والمعالجة
  - CSV - للاستيراد في أنظمة أخرى
- **تقارير مخصصة**:
  - تقارير الأجهزة والمواقع
  - تقارير المخزون والمشتريات
  - تقارير تقييم الموظفين
  - تقارير المهام والأعمال
- **فلترة متقدمة** - تخصيص التقارير حسب الحاجة

### 🔔 نظام الإشعارات الذكي
- **إشعارات في الوقت الحقيقي** - تنبيهات فورية للأحداث المهمة
- **تصنيف الإشعارات** - نجاح، تحذير، خطأ، معلومات
- **فلترة حسب الصلاحيات** - كل مستخدم يرى إشعاراته فقط
- **سجل الإشعارات** - تتبع جميع الأنشطة
- **إشعارات العمليات**:
  - إضافة/تعديل/حذف الأجهزة
  - تغييرات المخزون
  - تقييمات الموظفين
  - حالة الشبكة

### 🎨 واجهة المستخدم المتقدمة
- **تصميم Material Design** - واجهة حديثة وأنيقة
- **دعم الثيم الداكن** - راحة للعينين
- **واجهة عربية كاملة** - دعم كامل للغة العربية
- **تخطيط RTL** - تخطيط من اليمين لليسار
- **استجابة للشاشات** - يعمل على جميع أحجام الشاشات
- **تحسين المساحة** - استغلال أمثل لمساحة العرض

### 🔧 أدوات النظام المتقدمة
- **نسخ احتياطي تلقائي** - حماية البيانات
- **إصلاح قاعدة البيانات** - أدوات صيانة متقدمة
- **تحسين الأداء** - تحسين تلقائي للنظام
- **مزامنة متعددة المستخدمين** - دعم العمل الجماعي
- **بحث ذكي** - بحث متقدم في جميع البيانات
- **تصفية متعددة المعايير** - فلترة دقيقة للبيانات

## 🏗️ البنية التقنية

### قاعدة البيانات
- **MySQL** - قاعدة بيانات قوية وموثوقة
- **اسم قاعدة البيانات**: `NetworkManagementDB`
- **تحسين الأداء** - فهارس محسنة واستعلامات مُحسنة
- **نسخ احتياطي تلقائي** - حماية البيانات
- **هيكل محسن**:
  - جداول الشبكات والمستخدمين
  - جداول الأجهزة والمواقع
  - جداول المخزون والمشتريات
  - جداول التقييمات والمهام

### التقنيات المستخدمة
- **.NET 6.0** - إطار العمل الأساسي
- **WPF (Windows Presentation Foundation)** - واجهة المستخدم
- **Entity Framework Core** - ORM لقاعدة البيانات
- **Material Design in XAML** - تصميم الواجهات
- **CommunityToolkit.Mvvm** - نمط MVVM
- **MySQL.EntityFrameworkCore** - اتصال قاعدة البيانات
- **EPPlus** - تصدير Excel
- **iTextSharp** - تصدير PDF
- **Newtonsoft.Json** - معالجة JSON
- **Inno Setup** - حزم التثبيت

### الأمان والصلاحيات
- **تشفير كلمات المرور** - حماية متقدمة للحسابات
- **نظام صلاحيات متدرج**:
  - **Super Admin**: صلاحيات كاملة على النظام
  - **Network Manager**: إدارة شبكة واحدة
  - **Technician**: عمليات محدودة في الشبكة
  - **User**: عرض البيانات فقط
- **فصل البيانات** - كل شبكة معزولة عن الأخرى
- **سجل التدقيق** - تتبع جميع العمليات

## 🚀 التثبيت والتشغيل

### متطلبات النظام
- **نظام التشغيل**: Windows 10/11 (64-bit)
- **الذاكرة**: 4 GB RAM (8 GB مُوصى به)
- **المساحة**: 500 MB مساحة فارغة
- **قاعدة البيانات**: MySQL 8.0 أو أحدث
- **.NET Runtime**: 6.0 أو أحدث

### التثبيت الاحترافي (مُوصى به)
```bash
# بناء ملف التثبيت
build-release.bat

# تشغيل المثبت
Output/ShabakaPro-Setup-v2.0.0.exe
```

### التشغيل من المصدر
```bash
# استعادة الحزم
dotnet restore

# بناء المشروع
dotnet build --configuration Release

# تشغيل التطبيق
dotnet run --project NetworkManagement.csproj
```

### إعداد قاعدة البيانات
```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE NetworkManagementDB CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- إنشاء مستخدم (اختياري)
CREATE USER 'shabaka_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON NetworkManagementDB.* TO 'shabaka_user'@'localhost';
FLUSH PRIVILEGES;
```

## ⚙️ الإعدادات والتخصيص

### إعدادات الاتصال
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=NetworkManagementDB;Uid=root;Pwd=your_password;CharSet=utf8mb4;"
  }
}
```

### إعدادات التطبيق
- **اللغة**: العربية (افتراضي)
- **العملة**: الريال اليمني (YER)
- **التاريخ**: التقويم الهجري/الميلادي
- **الثيم**: فاتح/داكن
- **التحديث التلقائي**: مُفعل افتراضياً

## 📖 دليل الاستخدام

### 🔐 تسجيل الدخول
1. **تشغيل التطبيق** - افتح Shabaka Pro
2. **إدخال البيانات**:
   - اسم المستخدم
   - كلمة المرور
3. **اختيار الشبكة** (للمديرين)
4. **تسجيل الدخول**

### 👥 إدارة المستخدمين
#### للمدير العام (Super Admin):
- إضافة مستخدمين جدد
- تعديل صلاحيات المستخدمين
- حذف المستخدمين
- إدارة جميع الشبكات

#### لمدير الشبكة (Network Manager):
- إدارة مستخدمي شبكته فقط
- إضافة فنيين جدد
- تقييم الفنيين

### 🖥️ إدارة الأجهزة
1. **إضافة جهاز جديد**:
   - اختيار نوع الجهاز
   - إدخال عنوان IP
   - تحديد الموقع
   - اختيار المواد من المخزون (تلقائي)
2. **تعديل الأجهزة**:
   - تحديث المعلومات
   - تغيير الموقع
   - تحديث حالة الجهاز
3. **مراقبة الأجهزة**:
   - فحص الاتصال (Ping)
   - تتبع حالة الأجهزة
   - تنبيهات الأعطال

### 📍 إدارة المواقع
1. **إضافة موقع جديد**:
   - إدخال معلومات الموقع
   - تحديد الإحداثيات (GPS)
   - اختيار البنية التحتية:
     - نوع البطارية وحجمها
     - نوع وعدد القواعد
     - نوع وعدد الصناديق
     - أسلاك الشبكة والكهرباء
   - إضافة عناصر إضافية من المخزون
2. **الخصم التلقائي**:
   - خصم المواد من المخزون تلقائياً
   - دعم القيم السالبة
   - تحديث فوري للمخزون

### 📦 إدارة المخزون
1. **إضافة مواد جديدة**:
   - اسم المادة وفئتها
   - الكمية والوحدة
   - السعر والمورد
2. **تتبع الكميات**:
   - عرض الكميات الحالية
   - تنبيهات النفاد
   - سجل الحركات
3. **الربط التلقائي**:
   - خصم عند تركيب الأجهزة
   - خصم عند إنشاء المواقع
   - إرجاع عند الحذف

### 💰 إدارة المشتريات
1. **تسجيل مشترية جديدة**:
   - اختيار المورد
   - إدخال تفاصيل المشترية
   - تحديد السعر الإجمالي
2. **تحديث المخزون**:
   - إضافة تلقائية للمخزون
   - تحديث الأسعار
   - تتبع المصروفات

### ⭐ نظام التقييم
#### للمدير/المدير العام:
1. **تقييم الموظفين**:
   - اختيار الموظف
   - إعطاء درجة من 1-10
   - إضافة ملاحظات
2. **عرض الإحصائيات**:
   - متوسط الدرجات
   - أفضل وأسوأ أداء
   - تقارير الأداء

#### للفني:
- عرض التقييمات الشخصية
- إحصائيات الأداء
- رسائل تحفيزية

### 📊 التقارير والإحصائيات
1. **تقارير الأجهزة**:
   - قائمة الأجهزة وحالتها
   - إحصائيات الأعطال
   - تقارير الصيانة
2. **تقارير المخزون**:
   - حالة المخزون
   - تقارير النفاد
   - تقارير الحركات
3. **تقارير المالية**:
   - تقارير المشتريات
   - تحليل التكاليف
   - إحصائيات الإنفاق
4. **تقارير الموظفين**:
   - تقييمات الأداء
   - إحصائيات الإنتاجية
   - تقارير مقارنة

## 🔧 الصيانة والدعم

### النسخ الاحتياطي
```bash
# نسخ احتياطي يدوي
mysqldump -u root -p NetworkManagementDB > backup_$(date +%Y%m%d).sql

# استعادة النسخة الاحتياطية
mysql -u root -p NetworkManagementDB < backup_file.sql
```

### تحسين الأداء
- **تنظيف قاعدة البيانات** - حذف البيانات القديمة
- **إعادة فهرسة الجداول** - تحسين سرعة الاستعلامات
- **تحديث الإحصائيات** - تحسين أداء المحرك
- **ضغط البيانات** - توفير مساحة التخزين

### استكشاف الأخطاء
#### مشاكل الاتصال:
- التحقق من إعدادات قاعدة البيانات
- فحص اتصال الشبكة
- التأكد من تشغيل خدمة MySQL

#### مشاكل الأداء:
- مراقبة استخدام الذاكرة
- فحص سرعة قاعدة البيانات
- تحسين الاستعلامات البطيئة

## 🆕 التحديثات والإصدارات

### الإصدار الحالي: v2.0.0
#### الميزات الجديدة:
- ✅ نظام تقييم الموظفين المتقدم
- ✅ ربط تلقائي للمخزون مع المواقع والأجهزة
- ✅ دعم القيم السالبة في المخزون
- ✅ واجهة محسنة مع Material Design
- ✅ نظام إشعارات متقدم
- ✅ تقارير PDF احترافية
- ✅ خرائط تفاعلية للمواقع
- ✅ نظام صلاحيات محسن

#### التحسينات:
- 🔧 أداء محسن لقاعدة البيانات
- 🔧 واجهة مستخدم محسنة
- 🔧 استقرار أفضل للنظام
- 🔧 أمان محسن

### خارطة الطريق المستقبلية:
- 📱 تطبيق الهاتف المحمول
- 🌐 واجهة ويب
- 📡 مراقبة متقدمة للشبكة
- 🤖 ذكاء اصطناعي للتنبؤ بالأعطال
- 📈 تحليلات متقدمة
- 🔗 تكامل مع أنظمة خارجية

## 👨‍💻 فريق التطوير

تم تطوير **Shabaka Pro** بواسطة فريق متخصص من المطورين اليمنيين لخدمة السوق المحلي وتلبية احتياجات الشركات والمؤسسات في اليمن.

### المساهمون:
- **تطوير النظام**: فريق شبكة برو
- **تصميم الواجهات**: متخصصون في UX/UI
- **اختبار الجودة**: فريق ضمان الجودة
- **الدعم الفني**: فريق الدعم المتخصص

## 📞 الدعم والتواصل

### الدعم الفني:
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +967-1-234567
- **الموقع الإلكتروني**: www.shabakapro.ye

### التقارير والاقتراحات:
- **تقارير الأخطاء**: <EMAIL>
- **طلبات الميزات**: <EMAIL>
- **التحسينات**: <EMAIL>

## 📄 الترخيص والحقوق

هذا النظام محمي بحقوق الطبع والنشر وهو ملكية خاصة لفريق شبكة برو. جميع الحقوق محفوظة.

### شروط الاستخدام:
- ✅ الاستخدام التجاري مسموح للمرخص لهم
- ✅ التخصيص والتعديل حسب الحاجة
- ❌ إعادة التوزيع بدون إذن
- ❌ الهندسة العكسية محظورة

---

**© 2024 Shabaka Pro - جميع الحقوق محفوظة**

*نظام إدارة الشبكات المتقدم - صُنع في اليمن، للسوق اليمني* 🇾🇪
