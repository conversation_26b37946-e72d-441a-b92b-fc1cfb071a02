using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using NetworkManagement.Models;
using NetworkManagement.Services;
using System.Collections.ObjectModel;
using NetworkManagement.Views;
using NetworkManagement.Helpers;
using Microsoft.Extensions.DependencyInjection;

namespace NetworkManagement.ViewModels
{
    public partial class SitesViewModel : ObservableObject
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IAuthService _authService;
        private readonly INotificationService _notificationService;
        private readonly ISiteInventoryService _siteInventoryService;
        private readonly IInventoryService _inventoryService;

        [ObservableProperty]
        private ObservableCollection<Site> sites = new();

        [ObservableProperty]
        private Site? selectedSite;

        [ObservableProperty]
        private string searchText = string.Empty;

        [ObservableProperty]
        private bool isLoading = false;

        // خصائص الصلاحيات
        public bool CanAddSites => _authService.CanAddData();
        public bool CanEditSites => _authService.CanEditData();
        public bool CanDeleteSites => _authService.CanDeleteData();
        public bool CanManageSites => _authService.CanManageSites;

        // Threading control
        private readonly System.Threading.SemaphoreSlim _loadSemaphore = new System.Threading.SemaphoreSlim(1, 1);
        private System.Threading.CancellationTokenSource? _loadCancellationTokenSource;
        private readonly object _loadLock = new object();

        public SitesViewModel(IServiceProvider serviceProvider, IAuthService authService, INotificationService notificationService, ISiteInventoryService siteInventoryService, IInventoryService inventoryService)
        {
            _serviceProvider = serviceProvider;
            _authService = authService;
            _notificationService = notificationService;
            _siteInventoryService = siteInventoryService;
            _inventoryService = inventoryService;

            // الاشتراك في تغيير المستخدم لتحديث الصلاحيات
            _authService.UserChanged += OnUserChanged;

        }

        // Method to initialize data - called from View's Loaded event
        public async System.Threading.Tasks.Task InitializeAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("SitesViewModel.InitializeAsync: بدء التهيئة");
                await LoadSitesAsync();
                System.Diagnostics.Debug.WriteLine("SitesViewModel.InitializeAsync: تمت التهيئة بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"SitesViewModel.InitializeAsync: خطأ - {ex.Message}");
                System.Windows.MessageBox.Show(
                    $"حدث خطأ أثناء تحميل البيانات:\n{ex.Message}",
                    "خطأ في التحميل",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Error);
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task LoadSitesAsync()
        {
            await LoadSitesInternalAsync(async (siteService) => await siteService.GetAllAsync());
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task SearchSitesAsync()
        {
            await LoadSitesInternalAsync(async (siteService) => await siteService.SearchAsync(SearchText));
        }

        private async System.Threading.Tasks.Task LoadSitesInternalAsync(Func<ISiteService, System.Threading.Tasks.Task<System.Collections.Generic.IEnumerable<Site>>> dataLoader)
        {
            // منع التداخل في العمليات
            if (!await _loadSemaphore.WaitAsync(100))
            {
                System.Diagnostics.Debug.WriteLine("LoadSitesInternalAsync: عملية أخرى قيد التنفيذ، تم التجاهل");
                return;
            }

            try
            {
                // إلغاء أي عملية سابقة
                lock (_loadLock)
                {
                    _loadCancellationTokenSource?.Cancel();
                    _loadCancellationTokenSource = new System.Threading.CancellationTokenSource();
                }

                IsLoading = true;
                var cancellationToken = _loadCancellationTokenSource.Token;

                // إنشاء scope جديد للحصول على SiteService
                using var scope = _serviceProvider.CreateScope();
                var siteService = scope.ServiceProvider.GetRequiredService<ISiteService>();

                var siteList = await dataLoader(siteService);

                // التحقق من الإلغاء
                cancellationToken.ThrowIfCancellationRequested();

                // فلترة المواقع حسب الصلاحيات
                var filteredSites = siteList.Where(site => _authService.CanViewData(site.NetworkId));

                // التحقق من الإلغاء مرة أخرى
                cancellationToken.ThrowIfCancellationRequested();

                // تحديث UI على الـ UI thread
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    Sites.Clear();
                    foreach (var site in filteredSites)
                    {
                        Sites.Add(site);
                    }
                }, System.Windows.Threading.DispatcherPriority.Normal, cancellationToken);
            }
            catch (System.OperationCanceledException)
            {
                System.Diagnostics.Debug.WriteLine("LoadSitesInternalAsync: تم إلغاء العملية");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading sites: {ex.Message}");

                // عرض رسالة الخطأ على UI thread
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    System.Windows.MessageBox.Show(
                        $"خطأ في تحميل المواقع: {ex.Message}",
                        "خطأ",
                        System.Windows.MessageBoxButton.OK,
                        System.Windows.MessageBoxImage.Error);
                });
            }
            finally
            {
                IsLoading = false;
                _loadSemaphore.Release();
            }
        }

        [RelayCommand]
        private void AddSite()
        {
            // التحقق من صلاحية إضافة المواقع
            if (!CanAddSites)
            {
                PermissionHelper.ShowPermissionDeniedMessage("إضافة", "مواقع جديدة");
                return;
            }

            var dialogViewModel = App.GetService<SiteDialogViewModel>();
            var dialog = new SiteDialog(dialogViewModel);

            dialogViewModel.SiteSaved += async (s, site) =>
            {
                await LoadSitesAsync();
            };

            dialog.ShowDialog();
        }

        [RelayCommand]
        private void EditSite()
        {
            var siteToEdit = SelectedSite;
            if (siteToEdit == null) return;

            // التحقق من صلاحية تعديل الموقع
            if (!PermissionHelper.CanEditItem(_authService, siteToEdit.NetworkId))
            {
                PermissionHelper.ShowPermissionDeniedMessage("تعديل", "هذا الموقع");
                return;
            }

            var dialogViewModel = App.GetService<SiteDialogViewModel>();
            dialogViewModel.SetEditSite(siteToEdit);
            var dialog = new SiteDialog(dialogViewModel);

            dialogViewModel.SiteSaved += async (s, site) =>
            {
                await LoadSitesAsync();
            };

            dialog.ShowDialog();
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task DeleteSiteAsync()
        {
            var siteToDelete = SelectedSite;
            if (siteToDelete == null) return;

            // التحقق من صلاحية حذف الموقع
            if (!PermissionHelper.CanDeleteItem(_authService, siteToDelete.NetworkId))
            {
                PermissionHelper.ShowPermissionDeniedMessage("حذف", "هذا الموقع");
                return;
            }

            var result = System.Windows.MessageBox.Show(
                $"هل أنت متأكد من حذف الموقع '{siteToDelete.Name}'؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
                "تأكيد الحذف",
                System.Windows.MessageBoxButton.YesNo,
                System.Windows.MessageBoxImage.Warning);

            if (result == System.Windows.MessageBoxResult.Yes)
            {
                try
                {
                    using var scope = _serviceProvider.CreateScope();
                    var siteService = scope.ServiceProvider.GetRequiredService<ISiteService>();

                    // إرجاع المواد إلى المخزون قبل الحذف
                    var networkId = _authService.CurrentUser?.NetworkId;
                    var inventoryResult = await _siteInventoryService.RestoreInventoryForDeletedSiteAsync(siteToDelete, networkId);

                    if (inventoryResult.Success)
                    {
                        System.Diagnostics.Debug.WriteLine($"تم إرجاع المواد إلى المخزون عند حذف الموقع: {siteToDelete.Name}");
                    }

                    // إرجاع العناصر الإضافية للمخزون
                    await ReturnAdditionalInventoryItemsOnDelete(siteToDelete.AdditionalInventoryItems, networkId);

                    await siteService.DeleteAsync(siteToDelete.Id);
                    await LoadSitesAsync();

                    // إشعار حذف الموقع
                    var userName = _authService.CurrentUser?.Name ?? "المستخدم";
                    var userId = _authService.CurrentUser?.Id;
                    await _notificationService.ShowOperationAsync(
                        "تم حذف الموقع",
                        $"قام {userName} بحذف الموقع: {siteToDelete.Name}",
                        NotificationType.Success,
                        userName, userId, "حذف", siteToDelete.Name, siteToDelete.NetworkId);
                }
                catch (Exception ex)
                {
                    await ErrorHandler.HandleExceptionWithNotificationAsync(ex, "حذف الموقع", "SitesViewModel", siteToDelete.NetworkId);
                }
            }
        }

        private async void OnUserChanged(object? sender, User? user)
        {
            try
            {
                // تحديث خصائص الصلاحيات على UI thread
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    OnPropertyChanged(nameof(CanAddSites));
                    OnPropertyChanged(nameof(CanEditSites));
                    OnPropertyChanged(nameof(CanDeleteSites));
                    OnPropertyChanged(nameof(CanManageSites));
                });

                // تأخير قصير لتجنب التحديثات المتكررة
                await System.Threading.Tasks.Task.Delay(100);

                // إعادة تحميل البيانات حسب الصلاحيات الجديدة
                await LoadSitesAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in OnUserChanged: {ex.Message}");
            }
        }

        public void Dispose()
        {
            // إلغاء الاشتراك في تغيير المستخدم
            _authService.UserChanged -= OnUserChanged;

            // إلغاء أي عمليات تحميل جارية
            lock (_loadLock)
            {
                _loadCancellationTokenSource?.Cancel();
                _loadCancellationTokenSource?.Dispose();
            }

            // تنظيف SemaphoreSlim
            _loadSemaphore?.Dispose();
        }

        /// <summary>
        /// إرجاع العناصر الإضافية للمخزون عند حذف الموقع
        /// </summary>
        private async System.Threading.Tasks.Task ReturnAdditionalInventoryItemsOnDelete(string? additionalItemsJson, string? networkId)
        {
            if (string.IsNullOrEmpty(additionalItemsJson)) return;

            try
            {
                var additionalItems = System.Text.Json.JsonSerializer.Deserialize<List<Models.AdditionalInventoryItem>>(additionalItemsJson);
                if (additionalItems == null || additionalItems.Count == 0) return;

                foreach (var item in additionalItems)
                {
                    try
                    {
                        var inventoryItem = await _inventoryService.GetByIdAsync(item.InventoryId);
                        if (inventoryItem != null)
                        {
                            inventoryItem.Quantity += item.Quantity; // إرجاع الكمية
                            inventoryItem.UpdatedAt = DateTime.Now;
                            await _inventoryService.UpdateAsync(inventoryItem);
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"خطأ في إرجاع العنصر {item.Name}: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في معالجة إرجاع العناصر الإضافية عند الحذف: {ex.Message}");
            }
        }
    }
}

