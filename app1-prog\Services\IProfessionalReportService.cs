using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using NetworkManagement.Models;
using Task = System.Threading.Tasks.Task;

namespace NetworkManagement.Services
{
    public interface IProfessionalReportService
    {
        // Excel Reports
        Task<string> GenerateDevicesExcelReportAsync(IEnumerable<Device> devices, string filePath);
        Task<string> GenerateUsersExcelReportAsync(IEnumerable<User> users, string filePath);
        Task<string> GenerateTasksExcelReportAsync(IEnumerable<NetworkManagement.Models.Task> tasks, string filePath);
        Task<string> GenerateSitesExcelReportAsync(IEnumerable<Site> sites, string filePath);
        Task<string> GenerateInventoryExcelReportAsync(IEnumerable<Inventory> inventory, string filePath);
        Task<string> GeneratePurchasesExcelReportAsync(IEnumerable<Purchase> purchases, string filePath);
        Task<string> GenerateStatisticsExcelReportAsync(string filePath);

        // PDF Reports
        Task<string> GenerateDevicesPdfReportAsync(IEnumerable<Device> devices, string filePath);
        Task<string> GenerateUsersPdfReportAsync(IEnumerable<User> users, string filePath);
        Task<string> GenerateTasksPdfReportAsync(IEnumerable<NetworkManagement.Models.Task> tasks, string filePath);
        Task<string> GenerateSitesPdfReportAsync(IEnumerable<Site> sites, string filePath);
        Task<string> GenerateInventoryPdfReportAsync(IEnumerable<Inventory> inventory, string filePath);
        Task<string> GeneratePurchasesPdfReportAsync(IEnumerable<Purchase> purchases, string filePath);
        Task<string> GenerateStatisticsPdfReportAsync(string filePath);

        // Print Reports
        Task PrintDevicesReportAsync(IEnumerable<Device> devices);
        Task PrintUsersReportAsync(IEnumerable<User> users);
        Task PrintTasksReportAsync(IEnumerable<NetworkManagement.Models.Task> tasks);
        Task PrintSitesReportAsync(IEnumerable<Site> sites);
        Task PrintInventoryReportAsync(IEnumerable<Inventory> inventory);
        Task PrintPurchasesReportAsync(IEnumerable<Purchase> purchases);
        Task PrintStatisticsReportAsync();

        // Preview Reports
        Task PreviewDevicesReportAsync(IEnumerable<Device> devices);
        Task PreviewUsersReportAsync(IEnumerable<User> users);
        Task PreviewTasksReportAsync(IEnumerable<NetworkManagement.Models.Task> tasks);
        Task PreviewSitesReportAsync(IEnumerable<Site> sites);
        Task PreviewInventoryReportAsync(IEnumerable<Inventory> inventory);
        Task PreviewPurchasesReportAsync(IEnumerable<Purchase> purchases);
        Task PreviewStatisticsReportAsync();

        // Utility Methods
        Task<string> GetSaveFilePathAsync(string defaultFileName, string filter);
        Task<bool> OpenFileAsync(string filePath);
    }
}
