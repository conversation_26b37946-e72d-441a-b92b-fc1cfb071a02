using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using NetworkManagement.ViewModels;
using NetworkManagement.Models;

namespace NetworkManagement.Views
{
    public partial class SiteDialog : Window
    {
        public SiteDialog()
        {
            InitializeComponent();
        }

        public SiteDialog(SiteDialogViewModel viewModel) : this()
        {
            DataContext = viewModel;

            // Subscribe to events
            viewModel.DialogClosed += (s, e) => Close();
        }
    }

    /// <summary>
    /// نافذة اختيار عنصر من المخزون
    /// </summary>
    public class InventorySelectionDialog : Window
    {
        public Inventory? SelectedInventoryItem { get; private set; }
        public int SelectedQuantity { get; private set; } = 1;

        public InventorySelectionDialog(List<Inventory> availableItems)
        {
            Title = "اختيار عنصر من المخزون";
            Width = 500;
            Height = 400;
            WindowStartupLocation = WindowStartupLocation.CenterOwner;
            ResizeMode = ResizeMode.NoResize;

            var grid = new Grid();
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

            // عنوان
            var titleBlock = new TextBlock
            {
                Text = "اختر عنصر من المخزون:",
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(10)
            };
            Grid.SetRow(titleBlock, 0);
            grid.Children.Add(titleBlock);

            // قائمة العناصر
            var listBox = new ListBox
            {
                Margin = new Thickness(10),
                ItemsSource = availableItems.Where(i => i.Quantity > 0).ToList()
            };

            // تخصيص عرض العناصر
            var template = new DataTemplate();
            var factory = new FrameworkElementFactory(typeof(StackPanel));
            factory.SetValue(StackPanel.OrientationProperty, Orientation.Horizontal);

            var nameFactory = new FrameworkElementFactory(typeof(TextBlock));
            nameFactory.SetBinding(TextBlock.TextProperty, new System.Windows.Data.Binding("Name"));
            nameFactory.SetValue(TextBlock.FontWeightProperty, FontWeights.Bold);
            nameFactory.SetValue(TextBlock.MarginProperty, new Thickness(0, 0, 10, 0));
            factory.AppendChild(nameFactory);

            var quantityFactory = new FrameworkElementFactory(typeof(TextBlock));
            quantityFactory.SetBinding(TextBlock.TextProperty, new System.Windows.Data.Binding("QuantityDisplay"));
            quantityFactory.SetValue(TextBlock.ForegroundProperty, System.Windows.Media.Brushes.Gray);
            factory.AppendChild(quantityFactory);

            template.VisualTree = factory;
            listBox.ItemTemplate = template;

            Grid.SetRow(listBox, 1);
            grid.Children.Add(listBox);

            // حقل الكمية
            var quantityPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Margin = new Thickness(10),
                HorizontalAlignment = HorizontalAlignment.Center
            };

            quantityPanel.Children.Add(new TextBlock
            {
                Text = "الكمية المطلوبة:",
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(0, 0, 10, 0)
            });

            var quantityTextBox = new TextBox
            {
                Text = "1",
                Width = 100,
                VerticalAlignment = VerticalAlignment.Center
            };
            quantityPanel.Children.Add(quantityTextBox);

            Grid.SetRow(quantityPanel, 2);
            grid.Children.Add(quantityPanel);

            // أزرار
            var buttonPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(10)
            };

            var okButton = new Button
            {
                Content = "موافق",
                Width = 80,
                Height = 30,
                Margin = new Thickness(5),
                IsDefault = true
            };

            var cancelButton = new Button
            {
                Content = "إلغاء",
                Width = 80,
                Height = 30,
                Margin = new Thickness(5),
                IsCancel = true
            };

            okButton.Click += (s, e) =>
            {
                if (listBox.SelectedItem is Inventory selected && int.TryParse(quantityTextBox.Text, out int qty) && qty > 0)
                {
                    SelectedInventoryItem = selected;
                    SelectedQuantity = qty;
                    DialogResult = true;
                }
                else
                {
                    MessageBox.Show("يرجى اختيار عنصر وإدخال كمية صحيحة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            };

            cancelButton.Click += (s, e) => DialogResult = false;

            buttonPanel.Children.Add(okButton);
            buttonPanel.Children.Add(cancelButton);

            Grid.SetRow(buttonPanel, 3);
            grid.Children.Add(buttonPanel);

            Content = grid;
        }
    }
}
