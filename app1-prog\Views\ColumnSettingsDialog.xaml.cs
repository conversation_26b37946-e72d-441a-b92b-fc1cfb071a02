using System.Windows;
using NetworkManagement.ViewModels;

namespace NetworkManagement.Views
{
    public partial class ColumnSettingsDialog : Window
    {
        public ColumnSettingsDialog()
        {
            InitializeComponent();
        }

        public ColumnSettingsDialog(ColumnSettingsDialogViewModel viewModel) : this()
        {
            DataContext = viewModel;
            
            // Subscribe to events
            viewModel.DialogClosed += (s, e) => Close();
        }
    }
}
