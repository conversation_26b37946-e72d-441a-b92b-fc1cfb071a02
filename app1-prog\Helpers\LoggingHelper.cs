using System;
using System.IO;
using System.Threading.Tasks;

namespace NetworkManagement.Helpers
{
    /// <summary>
    /// فئة مساعدة للتسجيل والـ Logging
    /// </summary>
    public static class LoggingHelper
    {
        private static readonly string LogDirectory = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            "ShabakaPro", "Logs");

        private static readonly object LogLock = new object();

        static LoggingHelper()
        {
            try
            {
                if (!Directory.Exists(LogDirectory))
                {
                    Directory.CreateDirectory(LogDirectory);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"LoggingHelper: فشل في إنشاء مجلد السجلات: {ex.Message}");
            }
        }

        /// <summary>
        /// مستويات التسجيل
        /// </summary>
        public enum LogLevel
        {
            Debug,
            Info,
            Warning,
            Error,
            Critical
        }

        /// <summary>
        /// تسجيل رسالة مع مستوى محدد
        /// </summary>
        /// <param name="level">مستوى التسجيل</param>
        /// <param name="message">الرسالة</param>
        /// <param name="exception">الاستثناء (اختياري)</param>
        /// <param name="source">مصدر الرسالة (اختياري)</param>
        public static void Log(LogLevel level, string message, Exception? exception = null, string? source = null)
        {
            try
            {
                var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
                var levelStr = GetLevelString(level);
                var sourceStr = !string.IsNullOrEmpty(source) ? $"[{source}] " : "";
                
                var logMessage = $"{timestamp} [{levelStr}] {sourceStr}{message}";
                
                if (exception != null)
                {
                    logMessage += $"\nException: {exception.GetType().Name}: {exception.Message}";
                    if (!string.IsNullOrEmpty(exception.StackTrace))
                    {
                        logMessage += $"\nStackTrace: {exception.StackTrace}";
                    }
                    
                    // تسجيل الاستثناءات الداخلية
                    var innerEx = exception.InnerException;
                    while (innerEx != null)
                    {
                        logMessage += $"\nInner Exception: {innerEx.GetType().Name}: {innerEx.Message}";
                        innerEx = innerEx.InnerException;
                    }
                }

                // تسجيل في Debug Console
                System.Diagnostics.Debug.WriteLine(logMessage);

                // تسجيل في ملف
                WriteToFile(logMessage);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"LoggingHelper: فشل في التسجيل: {ex.Message}");
            }
        }

        /// <summary>
        /// تسجيل رسالة معلومات
        /// </summary>
        public static void LogInfo(string message, string? source = null)
        {
            Log(LogLevel.Info, message, null, source);
        }

        /// <summary>
        /// تسجيل رسالة تحذير
        /// </summary>
        public static void LogWarning(string message, Exception? exception = null, string? source = null)
        {
            Log(LogLevel.Warning, message, exception, source);
        }

        /// <summary>
        /// تسجيل رسالة خطأ
        /// </summary>
        public static void LogError(string message, Exception? exception = null, string? source = null)
        {
            Log(LogLevel.Error, message, exception, source);
        }

        /// <summary>
        /// تسجيل رسالة خطأ حرج
        /// </summary>
        public static void LogCritical(string message, Exception? exception = null, string? source = null)
        {
            Log(LogLevel.Critical, message, exception, source);
        }

        /// <summary>
        /// تسجيل رسالة تصحيح (Debug)
        /// </summary>
        public static void LogDebug(string message, string? source = null)
        {
            Log(LogLevel.Debug, message, null, source);
        }

        /// <summary>
        /// تسجيل بداية عملية
        /// </summary>
        public static void LogOperationStart(string operationName, string? source = null)
        {
            LogInfo($"بدء العملية: {operationName}", source);
        }

        /// <summary>
        /// تسجيل انتهاء عملية بنجاح
        /// </summary>
        public static void LogOperationSuccess(string operationName, string? source = null)
        {
            LogInfo($"انتهت العملية بنجاح: {operationName}", source);
        }

        /// <summary>
        /// تسجيل فشل عملية
        /// </summary>
        public static void LogOperationFailure(string operationName, Exception? exception = null, string? source = null)
        {
            LogError($"فشلت العملية: {operationName}", exception, source);
        }

        /// <summary>
        /// تسجيل أداء العملية
        /// </summary>
        public static void LogPerformance(string operationName, TimeSpan duration, string? source = null)
        {
            LogInfo($"أداء العملية '{operationName}': {duration.TotalMilliseconds:F2} مللي ثانية", source);
        }

        private static string GetLevelString(LogLevel level)
        {
            return level switch
            {
                LogLevel.Debug => "DEBUG",
                LogLevel.Info => "INFO",
                LogLevel.Warning => "WARN",
                LogLevel.Error => "ERROR",
                LogLevel.Critical => "CRITICAL",
                _ => "UNKNOWN"
            };
        }

        private static void WriteToFile(string message)
        {
            try
            {
                lock (LogLock)
                {
                    var fileName = $"NetworkManagement_{DateTime.Now:yyyy-MM-dd}.log";
                    var filePath = Path.Combine(LogDirectory, fileName);
                    
                    File.AppendAllText(filePath, message + Environment.NewLine);
                    
                    // تنظيف الملفات القديمة (أكثر من 30 يوم)
                    CleanupOldLogFiles();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"LoggingHelper: فشل في كتابة الملف: {ex.Message}");
            }
        }

        private static void CleanupOldLogFiles()
        {
            try
            {
                var cutoffDate = DateTime.Now.AddDays(-30);
                var logFiles = Directory.GetFiles(LogDirectory, "*.log");
                
                foreach (var file in logFiles)
                {
                    var fileInfo = new FileInfo(file);
                    if (fileInfo.CreationTime < cutoffDate)
                    {
                        File.Delete(file);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"LoggingHelper: فشل في تنظيف الملفات القديمة: {ex.Message}");
            }
        }

        /// <summary>
        /// تسجيل معلومات النظام عند بدء التطبيق
        /// </summary>
        public static void LogSystemInfo()
        {
            try
            {
                LogInfo("=== بدء تشغيل التطبيق ===", "System");
                LogInfo($"نظام التشغيل: {Environment.OSVersion}", "System");
                LogInfo($"إصدار .NET: {Environment.Version}", "System");
                LogInfo($"معرف المستخدم: {Environment.UserName}", "System");
                LogInfo($"اسم الجهاز: {Environment.MachineName}", "System");
                LogInfo($"مجلد العمل: {Environment.CurrentDirectory}", "System");
                LogInfo($"الذاكرة المستخدمة: {GC.GetTotalMemory(false) / 1024 / 1024} MB", "System");
            }
            catch (Exception ex)
            {
                LogError("فشل في تسجيل معلومات النظام", ex, "System");
            }
        }
    }
}
