using System.Threading.Tasks;

namespace NetworkManagement.Services
{
    public interface IThemeService
    {
        /// <summary>
        /// تطبيق المظهر المحدد
        /// </summary>
        Task ApplyThemeAsync(string themeName);

        /// <summary>
        /// الحصول على المظهر الحالي
        /// </summary>
        string GetCurrentTheme();

        /// <summary>
        /// الحصول على المظاهر المتاحة
        /// </summary>
        string[] GetAvailableThemes();

        /// <summary>
        /// تطبيق المظهر الافتراضي
        /// </summary>
        Task ApplyDefaultThemeAsync();

        /// <summary>
        /// تهيئة المظهر من الإعدادات المحفوظة
        /// </summary>
        Task InitializeThemeAsync();
    }
}
