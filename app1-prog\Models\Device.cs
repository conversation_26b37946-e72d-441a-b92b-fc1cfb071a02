using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel;

namespace NetworkManagement.Models
{
    public class Device : INotifyPropertyChanged
    {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        [MaxLength(100)]
        public string? Responsible { get; set; }

        [MaxLength(50)]
        public string? Type { get; set; }

        [MaxLength(200)]
        public string? Location { get; set; }

        [MaxLength(20)]
        public string? Phone { get; set; }

        [MaxLength(15)]
        public string? Ip { get; set; }

        public DateTime? InstallDate { get; set; }

        [MaxLength(100)]
        public string? PowerConnection { get; set; }

        [MaxLength(50)]
        public string? AdapterType { get; set; }

        [MaxLength(50)]
        public string? InstalledAdapterType { get; set; }

        [MaxLength(50)]
        public string? NetworkCableType { get; set; }

        [MaxLength(50)]
        public string? PowerCableType { get; set; }

        public int? NetworkCableLength { get; set; }

        public int? PowerCableLength { get; set; }

        [MaxLength(50)]
        public string? ConnectionMethod { get; set; }

        [MaxLength(50)]
        public string? LinkedNetwork { get; set; }

        [MaxLength(100)]
        public string? BroadcastNetworkName { get; set; }

        public int? Channel { get; set; }

        public int? ConnectedDevices { get; set; }

        [MaxLength(20)]
        public string? Status { get; set; } // يعمل, عاطل, مسحوب

        public DateTime? LastCheck { get; set; }

        [MaxLength(50)]
        public string? SiteId { get; set; }

        // Network relationship
        public string? NetworkId { get; set; }
        public virtual Network? Network { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual Site? Site { get; set; }

        // UI properties (not stored in database)
        [NotMapped]
        private bool _isSelected = false;

        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                if (_isSelected != value)
                {
                    _isSelected = value;
                    OnPropertyChanged(nameof(IsSelected));
                }
            }
        }

        protected virtual void OnPropertyChanged(string propertyName)
        {
            var handler = PropertyChanged;
            if (handler == null) return;

            var args = new PropertyChangedEventArgs(propertyName);

            // تحسين الأداء بتجنب فحص Dispatcher في كل مرة
            var dispatcher = System.Windows.Application.Current?.Dispatcher;
            if (dispatcher?.CheckAccess() == true)
            {
                handler.Invoke(this, args);
            }
            else
            {
                dispatcher?.Invoke(() => handler.Invoke(this, args));
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        // Display properties
        public string StatusDisplay => Status switch
        {
            "يعمل" => "يعمل",
            "عاطل" => "عاطل",
            "مسحوب" => "مسحوب",
            "متصل" => "متصل",
            "غير متصل" => "غير متصل",
            // للتوافق مع البيانات القديمة
            "active" => "يعمل",
            "inactive" => "عاطل",
            "maintenance" => "عاطل",
            "disabled" => "عاطل",
            null or "" => "غير محدد",
            _ => Status
        };

        public string InstallDateDisplay => InstallDate?.ToString("yyyy/MM/dd") ?? "غير محدد";
        public string LastCheckDisplay => LastCheck?.ToString("HH:mm dd/MM/yyyy") ?? "لم يتم الفحص";
        public string NetworkCableLengthDisplay => NetworkCableLength?.ToString() + " متر" ?? "غير محدد";
        public string PowerCableLengthDisplay => PowerCableLength?.ToString() + " متر" ?? "غير محدد";
        public string ChannelDisplay => Channel?.ToString() ?? "غير محدد";
        public string ConnectedDevicesDisplay => ConnectedDevices?.ToString() + " جهاز" ?? "0 جهاز";

        public string StatusColor => Status switch
        {
            "يعمل" or "متصل" => "#4CAF50", // أخضر
            "غير متصل" => "#F44336", // أحمر
            "عاطل" => "#FF9800", // برتقالي
            "مسحوب" => "#9E9E9E", // رمادي
            // للتوافق مع البيانات القديمة
            "active" or "نشط" => "#4CAF50",
            "inactive" or "غير نشط" => "#F44336",
            "maintenance" or "صيانة" => "#FF9800",
            "disabled" or "معطل" => "#9E9E9E",
            _ => "#9E9E9E" // رمادي افتراضي
        };

        // خاصية للتحقق من إمكانية عمل ping للجهاز
        [NotMapped]
        public bool CanPing => Status == "يعمل" || Status == "active";
    }
}
