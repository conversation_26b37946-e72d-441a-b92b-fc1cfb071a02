# برومت شامل لإنشاء نظام إدارة الشبكات المتقدم - Shabaka Pro

## وصف المشروع العام

أريد منك إنشاء نظام إدارة شبكات متكامل ومتقدم باسم **"Shabaka Pro"** مصمم خصيصاً للمؤسسات والشركات في اليمن. النظام يجب أن يكون تطبيق Windows Desktop باستخدام WPF مع قاعدة بيانات MySQL، ويوفر حلولاً شاملة لإدارة الشبكات، الأجهزة، المواقع، المخزون، والموارد البشرية بواجهة عربية حديثة ونظام صلاحيات متقدم.

## المتطلبات التقنية الأساسية

### التقنيات المطلوبة:
- **.NET 6.0** - إطار العمل الأساسي
- **WPF (Windows Presentation Foundation)** - واجهة المستخدم
- **Entity Framework Core** - ORM لقاعدة البيانات
- **Material Design in XAML** - تصميم الواجهات الحديثة
- **CommunityToolkit.Mvvm** - نمط MVVM
- **MySQL.EntityFrameworkCore** - اتصال قاعدة البيانات MySQL
- **EPPlus** - تصدير ملفات Excel
- **iTextSharp** - تصدير ملفات PDF
- **Newtonsoft.Json** - معالجة JSON

### قاعدة البيانات:
- **نوع قاعدة البيانات**: MySQL 8.0+
- **اسم قاعدة البيانات**: `NetworkManagementDB`
- **ترميز الأحرف**: UTF8MB4 لدعم العربية الكامل
- **الجداول المطلوبة**: Networks, Users, Sites, Devices, Tasks, Purchases, Inventory, UserEvaluations

## نظام الصلاحيات والأدوار

### الأدوار المطلوبة:
1. **Super Admin**: 
   - صلاحيات كاملة على النظام
   - إدارة جميع الشبكات والمستخدمين
   - الوصول لجميع البيانات والتقارير

2. **Network Manager**: 
   - إدارة شبكة واحدة محددة
   - إدارة مستخدمي شبكته فقط
   - تقييم الفنيين في شبكته

3. **Technician**: 
   - عمليات محدودة في شبكته
   - إضافة وتعديل الأجهزة والمواقع
   - عرض تقييماته الشخصية فقط

4. **User**: 
   - عرض البيانات فقط
   - لا يمكنه التعديل أو الحذف

### نظام فصل البيانات:
- كل شبكة معزولة عن الأخرى
- المستخدمون يرون بيانات شبكتهم فقط (عدا Super Admin)
- فلترة تلقائية للبيانات حسب NetworkId

## الواجهات والصفحات المطلوبة

### 1. صفحة تسجيل الدخول (LoginWindow):
- حقول: اسم المستخدم، كلمة المرور
- تشفير كلمات المرور
- تذكر بيانات الدخول
- واجهة أنيقة مع شعار التطبيق

### 2. النافذة الرئيسية (MainWindow):
- قائمة جانبية للتنقل
- منطقة المحتوى الرئيسي
- شريط الحالة
- نظام إشعارات
- معلومات المستخدم الحالي

### 3. لوحة التحكم (DashboardView):
- إحصائيات في الوقت الحقيقي:
  - عدد الأجهزة النشطة/غير النشطة
  - حالة المواقع والشبكات
  - إحصائيات المخزون
  - أداء الموظفين
- رسوم بيانية تفاعلية
- تحديث تلقائي للبيانات

### 4. إدارة الشبكات (NetworkManagementView):
- إضافة/تعديل/حذف الشبكات
- تفعيل/إلغاء تفعيل الشبكات
- إحصائيات كل شبكة

### 5. إدارة المستخدمين (UsersView):
- إضافة/تعديل/حذف المستخدمين
- تحديد الأدوار والصلاحيات
- ربط المستخدمين بالشبكات
- إعادة تعيين كلمات المرور

### 6. إدارة الأجهزة (DevicesView):
- إضافة/تعديل/حذف الأجهزة
- أنواع الأجهزة: Router, Switch, Access Point, Server, Camera, Printer, Other
- معلومات الجهاز: IP, MAC, الموقع، تاريخ التركيب، الحالة
- **ربط تلقائي بالمخزون**: خصم المواد عند تركيب الأجهزة
- مراقبة حالة الأجهزة (Ping Service)
- تصفية وبحث متقدم

### 7. إدارة المواقع (SitesView):
- إضافة/تعديل/حذف المواقع
- معلومات الموقع: الاسم، العنوان، الهاتف، إحداثيات GPS
- **خرائط تفاعلية** لتحديد المواقع
- **ربط تلقائي بالمخزون**:
  - أنظمة البطاريات (نوع، حجم)
  - القواعد (نوع، عدد)
  - الصناديق (نوع، عدد)
  - أسلاك الشبكة والكهرباء (نوع، طول)
  - عناصر إضافية مخصصة من المخزون
- خصم تلقائي من المخزون عند إنشاء المواقع
- إرجاع تلقائي للمخزون عند حذف المواقع

### 8. إدارة المخزون (InventoryView):
- إضافة/تعديل/حذف مواد المخزون
- معلومات المادة: الاسم، الفئة، الكمية، الوحدة، السعر، المورد
- **دعم القيم السالبة**: السماح بالخصم حتى لو كان المخزون صفر
- فئات المخزون: أجهزة، أدوات، كابلات، قطع غيار، أخرى
- تنبيهات نفاد المخزون
- سجل حركات المخزون
- **ربط تلقائي**:
  - خصم عند تركيب الأجهزة
  - خصم عند إنشاء المواقع
  - إضافة عند الشراء
  - إرجاع عند الحذف أو التعديل

### 9. إدارة المشتريات (PurchasesView):
- تسجيل المشتريات الجديدة
- معلومات المشترية: المورد، التاريخ، المواد، السعر الإجمالي
- **تحديث تلقائي للمخزون** عند تسجيل المشتريات
- تتبع المصروفات والتكاليف
- **دعم العملة اليمنية** (ريال يمني - YER)

### 10. إدارة المهام (TasksView):
- إضافة/تعديل/حذف المهام
- معلومات المهمة: العنوان، الوصف، النوع، الحالة
- تواريخ: تاريخ الطلب، تاريخ الإنجاز (قابلة للتعديل)
- ربط المهام بالأجهزة والمواقع
- **ترتيب حسب التاريخ** (الأحدث أخيراً)
- تصفية حسب الحالة والنوع

### 11. نظام تقييم الموظفين:

#### أ. إدارة التقييمات (UserEvaluationView) - للمديرين:
- تقييم المستخدمين بنقاط من 1-10
- إضافة ملاحظات للتقييم
- **صلاحيات متدرجة**:
  - Admin: يقيم جميع المستخدمين
  - Manager: يقيم الفنيين في شبكته فقط
- عرض إحصائيات التقييم:
  - متوسط النقاط
  - أعلى وأقل درجة
  - عدد التقييمات
- تصدير تقارير التقييم

#### ب. تقييماتي (MyEvaluationView) - للموظفين:
- عرض التقييمات الشخصية فقط
- إحصائيات الأداء الشخصي
- **رسائل تحفيزية** حسب مستوى الأداء:
  - 90%+: "أداء رائع! استمر على هذا المستوى المتميز"
  - 80-89%: "أداء جيد جداً! يمكنك الوصول للامتياز"
  - 70-79%: "أداء جيد، حاول تحسينه أكثر"
  - 60-69%: "أداء مقبول، يحتاج لمزيد من الجهد"
  - 50-59%: "أداء ضعيف، يجب العمل على التحسين"
  - أقل من 50%: "يحتاج لتحسين كبير، لا تستسلم!"
- فترات زمنية مختلفة للعرض

### 12. التقارير (ReportsView):
- **تقارير شاملة** لجميع جوانب النظام
- **تصدير متعدد الصيغ**:
  - PDF: تقارير احترافية للطباعة
  - Excel: للتحليل والمعالجة
  - CSV: للاستيراد في أنظمة أخرى
- **أنواع التقارير**:
  - تقارير الأجهزة والمواقع
  - تقارير المخزون والمشتريات
  - تقارير تقييم الموظفين
  - تقارير المهام والأعمال
- فلترة متقدمة حسب التاريخ والشبكة
- **ترتيب تنازلي** (الأحدث أخيراً)

### 13. الإعدادات (SettingsView):
- إعدادات قاعدة البيانات
- إعدادات التطبيق العامة
- إعدادات الثيم (فاتح/داكن)
- إعدادات اللغة والعملة
- نسخ احتياطي واستعادة

## الخدمات المطلوبة (Services)

### 1. خدمة المصادقة (AuthService):
- تسجيل الدخول والخروج
- إدارة الجلسات
- فحص الصلاحيات
- تشفير كلمات المرور

### 2. خدمة قاعدة البيانات (DatabaseService):
- إدارة الاتصال بقاعدة البيانات
- عمليات CRUD الأساسية
- إدارة المعاملات (Transactions)

### 3. خدمة الشبكات (NetworkService):
- إدارة الشبكات
- فلترة البيانات حسب الشبكة

### 4. خدمة المستخدمين (UserService):
- إدارة المستخدمين
- تحديد الأدوار والصلاحيات

### 5. خدمة الأجهزة (DeviceService):
- إدارة الأجهزة
- مراقبة حالة الأجهزة

### 6. خدمة ربط الأجهزة بالمخزون (DeviceInventoryService):
- **خصم تلقائي** من المخزون عند تركيب الأجهزة
- **إرجاع تلقائي** للمخزون عند حذف الأجهزة
- تتبع المواد المستخدمة لكل جهاز

### 7. خدمة المواقع (SiteService):
- إدارة المواقع
- ربط المواقع بالأجهزة

### 8. خدمة ربط المواقع بالمخزون (SiteInventoryService):
- **خصم تلقائي** من المخزون عند إنشاء المواقع
- **إرجاع تلقائي** للمخزون عند حذف المواقع
- **تحديث المخزون** عند تعديل المواقع
- إدارة العناصر الإضافية من المخزون
- دعم القيم السالبة في المخزون

### 9. خدمة المخزون (InventoryService):
- إدارة المخزون
- تتبع الكميات والحركات
- تنبيهات النفاد

### 10. خدمة المشتريات (PurchaseService):
- إدارة المشتريات
- **تحديث تلقائي للمخزون**

### 11. خدمة المهام (TaskService):
- إدارة المهام
- ربط المهام بالعناصر الأخرى

### 12. خدمة تقييم المستخدمين (UserEvaluationService):
- إدارة التقييمات
- حساب الإحصائيات
- **فلترة حسب الصلاحيات**:
  - Admin: يرى جميع التقييمات
  - Manager: يرى تقييمات شبكته فقط
  - Technician: يرى تقييماته الشخصية فقط

### 13. خدمة التقارير (ReportExportService):
- تصدير التقارير بصيغ مختلفة
- تنسيق التقارير الاحترافية

### 14. خدمة الإشعارات (NotificationService):
- إشعارات في الوقت الحقيقي
- تصنيف الإشعارات (نجاح، تحذير، خطأ، معلومات)
- **فلترة حسب الصلاحيات**
- سجل الإشعارات

### 15. خدمة المراقبة (PingService):
- **فحص دوري تلقائي** لحالة الأجهزة
- تحديث حالة الأجهزة
- تنبيهات الأعطال

### 16. خدمة التخزين المؤقت (CacheService):
- تحسين الأداء
- تخزين البيانات المتكررة

## النماذج المطلوبة (Models)

### 1. Network:
```csharp
- Id (string, Primary Key)
- Name (string, Required)
- Description (string, Optional)
- IsActive (bool)
- CreatedAt, UpdatedAt (DateTime)
- Navigation: Users, Sites, Devices, Tasks, Purchases, Inventory
```

### 2. User:
```csharp
- Id (string, Primary Key)
- Username (string, Required, Unique)
- PasswordHash (string, Required)
- Name (string, Required)
- Email (string, Optional)
- Phone (string, Optional)
- Role (string: Admin, Manager, Technician, User)
- IsActive (bool)
- NetworkId (string, Foreign Key)
- CreatedAt, UpdatedAt (DateTime)
- Navigation: Network
```

### 3. Site:
```csharp
- Id (string, Primary Key)
- Name (string, Required)
- Address (string, Optional)
- Phone (string, Optional)
- GpsLat, GpsLng (double, Optional)
- PowerSource (string, Optional)
// نظام البطاريات
- BatteryType, BatterySize (string, Optional)
// نظام القواعد
- BaseType (string, Optional)
- BaseCount (int, Optional)
// نظام الصناديق
- BoxType (string, Optional)
- BoxCount (int, Optional)
// أسلاك الشبكة والكهرباء
- NetworkCableType (string, Optional)
- NetworkCableLength (int, Optional)
- PowerCableType (string, Optional)
- PowerCableLength (int, Optional)
// عناصر إضافية من المخزون (JSON)
- AdditionalInventoryItems (string, JSON)
- NetworkId (string, Foreign Key)
- CreatedAt, UpdatedAt (DateTime)
- Navigation: Network, Devices
```

### 4. Device:
```csharp
- Id (string, Primary Key)
- Name (string, Required)
- Type (string: Router, Switch, AccessPoint, Server, Camera, Printer, Other)
- Ip (string, Optional)
- MacAddress (string, Optional)
- Model (string, Optional)
- SerialNumber (string, Optional)
- Location (string, Optional)
- InstallDate (DateTime, Optional)
- Status (string: Active, Inactive, Maintenance, Broken)
- LastCheck (DateTime, Optional)
- SiteId (string, Foreign Key)
- NetworkId (string, Foreign Key)
- CreatedAt, UpdatedAt (DateTime)
- Navigation: Site, Network
```

### 5. Inventory:
```csharp
- Id (string, Primary Key)
- Name (string, Required)
- Category (string, Required)
- Quantity (int, Required) // دعم القيم السالبة
- Unit (string, Required)
- UnitPrice (decimal, Optional)
- Description (string, Optional)
- Location (string, Optional)
- MinimumStock, MaximumStock (int, Optional)
- Supplier (string, Optional)
- NetworkId (string, Foreign Key)
- LastUpdated, CreatedAt, UpdatedAt (DateTime)
- Navigation: Network
```

### 6. Purchase:
```csharp
- Id (string, Primary Key)
- Date (DateTime, Required)
- Supplier (string, Required)
- TotalAmount (decimal, Required) // بالريال اليمني
- Description (string, Optional)
- Items (string, JSON) // قائمة المواد المشتراة
- NetworkId (string, Foreign Key)
- CreatedAt, UpdatedAt (DateTime)
- Navigation: Network
```

### 7. Task:
```csharp
- Id (string, Primary Key)
- Title (string, Required)
- Description (string, Optional)
- Type (string, Required)
- Status (string, Required)
- Priority (string, Optional)
- RequestDate (DateTime, Required) // قابل للتعديل
- CompletionDate (DateTime, Optional) // قابل للتعديل
- AssignedTo (string, Optional)
- RelatedDeviceId (string, Optional)
- RelatedSiteId (string, Optional)
- NetworkId (string, Foreign Key)
- CreatedAt, UpdatedAt (DateTime)
- Navigation: Network, Device, Site
```

### 8. UserEvaluation:
```csharp
- Id (string, Primary Key)
- UserId (string, Required, Foreign Key)
- EvaluatedBy (string, Required, Foreign Key)
- EvaluationDate (DateTime, Required)
- Score (int, Required, Range 1-10)
- Notes (string, Optional)
- NetworkId (string, Foreign Key)
- CreatedAt, UpdatedAt (DateTime)
- Navigation: User, EvaluatedByUser, Network
```

## متطلبات الواجهة والتصميم

### التصميم العام:
- **Material Design** - تصميم حديث وأنيق
- **دعم الثيم الداكن** - راحة للعينين
- **واجهة عربية كاملة** - دعم كامل للغة العربية
- **تخطيط RTL** - من اليمين لليسار
- **استجابة للشاشات** - يعمل على جميع الأحجام
- **تحسين المساحة** - استغلال أمثل للمساحة

### الألوان والخطوط:
- ألوان Material Design
- خطوط عربية واضحة
- تباين جيد للقراءة
- ألوان مميزة للحالات (نجاح، تحذير، خطأ)

### التفاعل:
- انتقالات سلسة
- تحميل تدريجي
- رسائل تأكيد للعمليات المهمة
- اختصارات لوحة المفاتيح

## الميزات المتقدمة المطلوبة

### 1. نظام الإشعارات:
- إشعارات فورية للأحداث المهمة
- تصنيف حسب النوع والأولوية
- فلترة حسب صلاحيات المستخدم
- سجل كامل للإشعارات

### 2. البحث والفلترة:
- بحث ذكي في جميع البيانات
- فلترة متعددة المعايير
- حفظ عمليات البحث المفضلة
- بحث سريع في الوقت الحقيقي

### 3. التصدير والاستيراد:
- تصدير البيانات بصيغ متعددة
- استيراد البيانات من ملفات خارجية
- قوالب جاهزة للتصدير
- معاينة قبل التصدير

### 4. النسخ الاحتياطي:
- نسخ احتياطي تلقائي مجدول
- استعادة سريعة للبيانات
- ضغط البيانات لتوفير المساحة
- تشفير النسخ الاحتياطية

### 5. مراقبة الأداء:
- مراقبة استخدام الموارد
- إحصائيات الأداء
- تحسين تلقائي للاستعلامات
- تنبيهات الأداء

## متطلبات الأمان

### 1. المصادقة:
- تشفير قوي لكلمات المرور
- جلسات آمنة
- انتهاء صلاحية الجلسات
- تسجيل محاولات الدخول

### 2. التفويض:
- نظام صلاحيات متدرج
- فحص الصلاحيات في كل عملية
- منع الوصول غير المصرح به
- سجل العمليات الحساسة

### 3. حماية البيانات:
- تشفير البيانات الحساسة
- حماية من SQL Injection
- تنظيف المدخلات
- نسخ احتياطي آمنة

## متطلبات الأداء

### 1. سرعة الاستجابة:
- تحميل سريع للصفحات
- استعلامات محسنة
- تخزين مؤقت ذكي
- تحميل تدريجي للبيانات الكبيرة

### 2. استخدام الذاكرة:
- إدارة فعالة للذاكرة
- تنظيف الكائنات غير المستخدمة
- تحسين استهلاك الموارد
- مراقبة تسريب الذاكرة

### 3. قاعدة البيانات:
- فهارس محسنة
- استعلامات مُحسنة
- تجميع البيانات المترابطة
- تنظيف البيانات القديمة

## متطلبات التوطين

### 1. اللغة العربية:
- دعم كامل للنصوص العربية
- تخطيط RTL في جميع الواجهات
- خطوط عربية واضحة
- تنسيق التواريخ العربية

### 2. العملة اليمنية:
- جميع الأسعار بالريال اليمني (YER)
- تنسيق الأرقام العربية
- رموز العملة المناسبة
- حسابات دقيقة للمبالغ

### 3. التقويم:
- دعم التقويم الهجري والميلادي
- تحويل بين التقاويم
- تنسيق التواريخ المحلي
- أيام الأسبوع بالعربية

## متطلبات النشر والتوزيع

### 1. حزمة التثبيت:
- مثبت احترافي باستخدام Inno Setup
- تثبيت تلقائي للمتطلبات
- إعداد قاعدة البيانات التلقائي
- اختصارات سطح المكتب

### 2. التحديثات:
- نظام تحديث تلقائي
- تنزيل التحديثات في الخلفية
- تثبيت صامت للتحديثات
- الحفاظ على البيانات والإعدادات

### 3. التوافق:
- دعم Windows 10/11
- متطلبات نظام واضحة
- اختبار على بيئات مختلفة
- دعم الشاشات عالية الدقة

## ملاحظات مهمة للتطوير

### 1. جودة الكود:
- اتباع معايير C# الحديثة
- تعليقات واضحة بالعربية والإنجليزية
- هيكل مشروع منظم
- اختبارات شاملة

### 2. معالجة الأخطاء:
- معالجة شاملة للاستثناءات
- رسائل خطأ واضحة بالعربية
- سجل مفصل للأخطاء
- استرداد تلقائي من الأخطاء

### 3. التوثيق:
- توثيق شامل للكود
- دليل المستخدم بالعربية
- دليل المطور التقني
- أمثلة عملية للاستخدام

### 4. الاختبار:
- اختبارات وحدة شاملة
- اختبارات التكامل
- اختبارات الأداء
- اختبارات الأمان

## الخلاصة

هذا النظام يجب أن يكون حلاً متكاملاً وشاملاً لإدارة الشبكات في البيئة اليمنية، مع التركيز على:
- **سهولة الاستخدام** - واجهة بديهية وبسيطة
- **الشمولية** - تغطية جميع احتياجات إدارة الشبكات
- **الأمان** - حماية قوية للبيانات والنظام
- **الأداء** - سرعة واستقرار في العمل
- **التوطين** - مناسب للبيئة والثقافة المحلية
- **القابلية للتوسع** - إمكانية إضافة ميزات جديدة
- **الصيانة** - سهولة الصيانة والتطوير المستقبلي

النظام يجب أن يكون جاهزاً للاستخدام الإنتاجي مع جميع الميزات المذكورة أعلاه مُنفذة بالكامل وبجودة عالية.

## متطلبات إضافية مهمة

### 1. ميزات خاصة بالمخزون:
- **دعم القيم السالبة الكامل**: يجب أن يسمح النظام بخصم المواد من المخزون حتى لو كانت الكمية الحالية صفر أو سالبة
- **ربط تلقائي ذكي**:
  - عند إضافة جهاز جديد: خصم المواد المطلوبة تلقائياً
  - عند إضافة موقع جديد: خصم جميع المواد المحددة (بطاريات، قواعد، صناديق، أسلاك، عناصر إضافية)
  - عند حذف جهاز أو موقع: إرجاع المواد للمخزون تلقائياً
  - عند تعديل جهاز أو موقع: إرجاع المواد القديمة وخصم المواد الجديدة
- **تتبع دقيق للحركات**: سجل مفصل لجميع عمليات الخصم والإضافة مع التواريخ والمسؤولين

### 2. نظام التقييم المتقدم:
- **تقييم يومي فقط**: لا يمكن تقييم نفس المستخدم أكثر من مرة في اليوم الواحد
- **صلاحيات دقيقة**:
  - Super Admin: يقيم جميع المستخدمين في جميع الشبكات
  - Network Manager: يقيم الفنيين في شبكته فقط
  - Technician: يرى تقييماته الشخصية فقط في صفحة منفصلة
- **إحصائيات شاملة**: متوسط النقاط، أعلى وأقل درجة، عدد التقييمات، فترات زمنية مختلفة
- **رسائل تحفيزية ذكية**: رسائل مخصصة حسب مستوى الأداء

### 3. واجهة المستخدم المحسنة:
- **تصميم Material Design كامل**: استخدام مكتبة MaterialDesignInXamlToolkit
- **دعم الثيم الداكن**: تبديل سلس بين الثيم الفاتح والداكن
- **تحسين المساحة**:
  - عرض 6-7 عناصر في الجداول مع خطوط أكبر
  - إزالة الخلفيات المتناوبة للصفوف
  - أزرار عمل واضحة بدون الحاجة للنقر في منطقة الجدول أولاً
- **تخطيط RTL كامل**: جميع العناصر من اليمين لليسار
- **خرائط تفاعلية**: لتحديد مواقع GPS للمواقع

### 4. نظام التقارير الاحترافي:
- **تصدير PDF احترافي**: تقارير منسقة للطباعة
- **تصدير Excel متقدم**: مع تنسيق وألوان
- **تصدير CSV**: للاستيراد في أنظمة أخرى
- **معاينة قبل التصدير**: عرض التقرير قبل الحفظ
- **فلترة متقدمة**: حسب التاريخ، الشبكة، النوع، الحالة

### 5. نظام الإشعارات الذكي:
- **إشعارات فورية**: لجميع العمليات المهمة
- **فلترة حسب الصلاحيات**: كل مستخدم يرى إشعاراته فقط
- **تصنيف الإشعارات**: نجاح (أخضر)، تحذير (أصفر)، خطأ (أحمر)، معلومات (أزرق)
- **سجل الإشعارات**: حفظ آخر 50 إشعار لكل مستخدم

### 6. مراقبة الأجهزة التلقائية:
- **Ping Service**: فحص دوري لحالة الأجهزة كل 5 دقائق
- **تحديث الحالة**: تلقائي حسب نتيجة الفحص
- **تنبيهات الأعطال**: إشعارات فورية عند انقطاع الأجهزة
- **سجل الحالة**: تتبع تاريخ آخر فحص لكل جهاز

### 7. إدارة قاعدة البيانات المتقدمة:
- **نسخ احتياطي تلقائي**: كل 24 ساعة
- **إصلاح قاعدة البيانات**: أدوات تشخيص وإصلاح
- **تحسين الأداء**: فهارس محسنة واستعلامات مُحسنة
- **مزامنة متعددة المستخدمين**: دعم العمل الجماعي

### 8. أمان متقدم:
- **تشفير كلمات المرور**: باستخدام BCrypt أو مشابه
- **جلسات آمنة**: انتهاء صلاحية تلقائي
- **سجل العمليات**: تتبع جميع العمليات الحساسة
- **حماية من SQL Injection**: تنظيف جميع المدخلات

### 9. دعم العملة اليمنية:
- **جميع الأسعار بالريال اليمني (YER)**
- **تنسيق الأرقام العربية**: ١٢٣٤٥٦٧٨٩٠
- **رموز العملة**: ر.ي أو YER
- **حسابات دقيقة**: دعم الكسور العشرية

### 10. ميزات خاصة بالمهام:
- **ترتيب حسب التاريخ**: الأحدث أخيراً (تنازلي)
- **تواريخ قابلة للتعديل**: تاريخ الطلب وتاريخ الإنجاز
- **ربط بالعناصر**: ربط المهام بالأجهزة والمواقع
- **حالات متعددة**: جديد، قيد التنفيذ، مكتمل، ملغي

هذا البرومت الشامل يغطي جميع جوانب النظام المطلوب بتفاصيل دقيقة لضمان إنشاء نظام متكامل وعالي الجودة.
