using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using NetworkManagement.Data;
using NetworkManagement.Models;

namespace NetworkManagement.Services
{
    public class UserEvaluationService : IUserEvaluationService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ICacheService _cacheService;

        public UserEvaluationService(IServiceProvider serviceProvider, ICacheService cacheService)
        {
            _serviceProvider = serviceProvider;
            _cacheService = cacheService;
        }

        public async Task<IEnumerable<UserEvaluation>> GetAllAsync(string? networkFilter = null)
        {
            var cacheKey = $"user_evaluations_all_{networkFilter ?? "null"}";
            
            return await _cacheService.GetOrCreateAsync<IEnumerable<UserEvaluation>>(cacheKey, async () =>
            {
                using var scope = _serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                if (context.UserEvaluations == null)
                    return Array.Empty<UserEvaluation>();

                var query = context.UserEvaluations
                    .Include(e => e.User)
                    .Include(e => e.EvaluatedByUser)
                    .Include(e => e.Network)
                    .AsQueryable();

                if (!string.IsNullOrEmpty(networkFilter))
                {
                    query = query.Where(e => e.NetworkId == networkFilter);
                }

                return await query.OrderByDescending(e => e.EvaluationDate)
                                 .ThenBy(e => e.User!.Name)
                                 .ToListAsync();
            }, TimeSpan.FromMinutes(5));
        }

        public async Task<UserEvaluation?> GetByIdAsync(string id)
        {
            if (string.IsNullOrEmpty(id))
                return null;

            using var scope = _serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

            if (context.UserEvaluations == null)
                return null;

            return await context.UserEvaluations
                .Include(e => e.User)
                .Include(e => e.EvaluatedByUser)
                .Include(e => e.Network)
                .FirstOrDefaultAsync(e => e.Id == id);
        }

        public async Task<UserEvaluation> CreateAsync(UserEvaluation evaluation)
        {
            if (evaluation == null)
                throw new ArgumentNullException(nameof(evaluation));

            using var scope = _serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

            if (context.UserEvaluations == null)
                throw new InvalidOperationException("DbSet<UserEvaluation> is null");

            // التحقق من عدم وجود تقييم لنفس المستخدم في نفس التاريخ
            var existingEvaluation = await context.UserEvaluations
                .FirstOrDefaultAsync(e => e.UserId == evaluation.UserId && 
                                         e.EvaluationDate.Date == evaluation.EvaluationDate.Date);

            if (existingEvaluation != null)
            {
                throw new InvalidOperationException($"تم تقييم المستخدم بالفعل في تاريخ {evaluation.EvaluationDate:dd/MM/yyyy}");
            }

            evaluation.Id = Guid.NewGuid().ToString();
            evaluation.CreatedAt = DateTime.Now;
            evaluation.UpdatedAt = DateTime.Now;

            context.UserEvaluations.Add(evaluation);
            await context.SaveChangesAsync();

            // مسح الكاش المتعلق
            await ClearRelatedCacheAsync(evaluation.NetworkId, evaluation.UserId);

            // مسح كاش الإحصائيات العامة
            _cacheService.RemoveByPattern("all_users_statistics_");

            return evaluation;
        }

        public async Task<UserEvaluation> UpdateAsync(UserEvaluation evaluation)
        {
            if (evaluation == null)
                throw new ArgumentNullException(nameof(evaluation));

            using var scope = _serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

            if (context.UserEvaluations == null)
                throw new InvalidOperationException("DbSet<UserEvaluation> is null");

            var existingEvaluation = await context.UserEvaluations
                .FirstOrDefaultAsync(e => e.Id == evaluation.Id);

            if (existingEvaluation == null)
                throw new InvalidOperationException("التقييم غير موجود");

            // التحقق من عدم تعارض التاريخ مع تقييم آخر
            var conflictingEvaluation = await context.UserEvaluations
                .FirstOrDefaultAsync(e => e.UserId == evaluation.UserId && 
                                         e.EvaluationDate.Date == evaluation.EvaluationDate.Date &&
                                         e.Id != evaluation.Id);

            if (conflictingEvaluation != null)
            {
                throw new InvalidOperationException($"يوجد تقييم آخر للمستخدم في تاريخ {evaluation.EvaluationDate:dd/MM/yyyy}");
            }

            // تحديث البيانات
            existingEvaluation.Score = evaluation.Score;
            existingEvaluation.Notes = evaluation.Notes;
            existingEvaluation.EvaluationDate = evaluation.EvaluationDate;
            existingEvaluation.UpdatedAt = DateTime.Now;

            await context.SaveChangesAsync();

            // مسح الكاش المتعلق
            await ClearRelatedCacheAsync(existingEvaluation.NetworkId, existingEvaluation.UserId);

            // مسح كاش الإحصائيات العامة
            _cacheService.RemoveByPattern("all_users_statistics_");

            return existingEvaluation;
        }

        public async Task<bool> DeleteAsync(string id)
        {
            if (string.IsNullOrEmpty(id))
                return false;

            using var scope = _serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

            if (context.UserEvaluations == null)
                return false;

            var evaluation = await context.UserEvaluations.FirstOrDefaultAsync(e => e.Id == id);
            if (evaluation == null)
                return false;

            var networkId = evaluation.NetworkId;
            var userId = evaluation.UserId;

            context.UserEvaluations.Remove(evaluation);
            await context.SaveChangesAsync();

            // مسح شامل للكاش
            await ClearRelatedCacheAsync(networkId, userId);

            // مسح إضافي للتأكد من تحديث جميع البيانات
            _cacheService.RemoveByPattern("user_evaluations_");
            _cacheService.RemoveByPattern("all_user_stats_");
            _cacheService.RemoveByPattern("users_not_evaluated_");
            _cacheService.RemoveByPattern("evaluable_users_");
            _cacheService.RemoveByPattern($"user_stats_{userId}");

            // مسح كاش الإحصائيات العامة
            _cacheService.RemoveByPattern("all_users_statistics_");

            return true;
        }

        public async Task<IEnumerable<UserEvaluation>> GetByUserIdAsync(string userId, DateTime? startDate = null, DateTime? endDate = null)
        {
            if (string.IsNullOrEmpty(userId))
                return Array.Empty<UserEvaluation>();

            var cacheKey = $"user_evaluations_user_{userId}_{startDate?.ToString("yyyyMMdd") ?? "null"}_{endDate?.ToString("yyyyMMdd") ?? "null"}";
            
            return await _cacheService.GetOrCreateAsync<IEnumerable<UserEvaluation>>(cacheKey, async () =>
            {
                using var scope = _serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                if (context.UserEvaluations == null)
                    return Array.Empty<UserEvaluation>();

                var query = context.UserEvaluations
                    .Include(e => e.User)
                    .Include(e => e.EvaluatedByUser)
                    .Include(e => e.Network)
                    .Where(e => e.UserId == userId);

                if (startDate.HasValue)
                    query = query.Where(e => e.EvaluationDate.Date >= startDate.Value.Date);

                if (endDate.HasValue)
                    query = query.Where(e => e.EvaluationDate.Date <= endDate.Value.Date);

                return await query.OrderByDescending(e => e.EvaluationDate).ToListAsync();
            }, TimeSpan.FromMinutes(10));
        }

        public async Task<UserEvaluation?> GetByUserAndDateAsync(string userId, DateTime date)
        {
            if (string.IsNullOrEmpty(userId))
                return null;

            using var scope = _serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

            if (context.UserEvaluations == null)
                return null;

            return await context.UserEvaluations
                .Include(e => e.User)
                .Include(e => e.EvaluatedByUser)
                .Include(e => e.Network)
                .FirstOrDefaultAsync(e => e.UserId == userId && e.EvaluationDate.Date == date.Date);
        }

        public async Task<IEnumerable<UserEvaluation>> GetByDateRangeAsync(DateTime startDate, DateTime endDate, string? networkFilter = null)
        {
            var cacheKey = $"user_evaluations_range_{startDate:yyyyMMdd}_{endDate:yyyyMMdd}_{networkFilter ?? "null"}";
            
            return await _cacheService.GetOrCreateAsync<IEnumerable<UserEvaluation>>(cacheKey, async () =>
            {
                using var scope = _serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                if (context.UserEvaluations == null)
                    return Array.Empty<UserEvaluation>();

                var query = context.UserEvaluations
                    .Include(e => e.User)
                    .Include(e => e.EvaluatedByUser)
                    .Include(e => e.Network)
                    .Where(e => e.EvaluationDate.Date >= startDate.Date &&
                               e.EvaluationDate.Date <= endDate.Date);

                if (!string.IsNullOrEmpty(networkFilter))
                {
                    query = query.Where(e => e.NetworkId == networkFilter);
                }

                return await query.OrderByDescending(e => e.EvaluationDate)
                                 .ThenBy(e => e.User!.Name)
                                 .ToListAsync();
            }, TimeSpan.FromMinutes(5));
        }

        public async Task<UserEvaluationStatistics?> GetUserStatisticsAsync(string userId, DateTime? startDate = null, DateTime? endDate = null)
        {
            if (string.IsNullOrEmpty(userId))
                return null;

            var cacheKey = $"user_stats_{userId}_{startDate?.ToString("yyyyMMdd") ?? "null"}_{endDate?.ToString("yyyyMMdd") ?? "null"}";

            return await _cacheService.GetOrCreateAsync<UserEvaluationStatistics?>(cacheKey, async () =>
            {
                using var scope = _serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                if (context.UserEvaluations == null || context.Users == null)
                    return null;

                var user = await context.Users.Include(u => u.Network)
                    .FirstOrDefaultAsync(u => u.Id == userId);

                if (user == null)
                    return null;

                var query = context.UserEvaluations.Where(e => e.UserId == userId);

                if (startDate.HasValue)
                    query = query.Where(e => e.EvaluationDate.Date >= startDate.Value.Date);

                if (endDate.HasValue)
                    query = query.Where(e => e.EvaluationDate.Date <= endDate.Value.Date);

                var evaluations = await query.ToListAsync();

                if (!evaluations.Any())
                    return null;

                return new UserEvaluationStatistics
                {
                    UserId = userId,
                    UserName = user.Name,
                    NetworkId = user.NetworkId ?? string.Empty,
                    NetworkName = user.Network?.Name ?? "غير محدد",
                    TotalEvaluations = evaluations.Count,
                    AverageScore = (decimal)evaluations.Average(e => e.Score),
                    HighestScore = evaluations.Max(e => e.Score),
                    LowestScore = evaluations.Min(e => e.Score),
                    FirstEvaluationDate = evaluations.Min(e => e.EvaluationDate),
                    LastEvaluationDate = evaluations.Max(e => e.EvaluationDate)
                };
            }, TimeSpan.FromMinutes(15));
        }

        public async Task<IEnumerable<UserEvaluationStatistics>> GetAllUsersStatisticsAsync(string? networkFilter = null, DateTime? startDate = null, DateTime? endDate = null, string? currentUserRole = null, string? currentUserId = null)
        {
            var cacheKey = $"all_user_stats_{networkFilter ?? "null"}_{startDate?.ToString("yyyyMMdd") ?? "null"}_{endDate?.ToString("yyyyMMdd") ?? "null"}_{currentUserRole ?? "null"}_{currentUserId ?? "null"}";

            return await _cacheService.GetOrCreateAsync<IEnumerable<UserEvaluationStatistics>>(cacheKey, async () =>
            {
                using var scope = _serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                if (context.UserEvaluations == null || context.Users == null)
                    return Array.Empty<UserEvaluationStatistics>();

                var usersQuery = context.Users.Include(u => u.Network).AsQueryable();

                // تطبيق فلتر الأدوار حسب المستخدم الحالي
                if (currentUserRole == "Admin")
                {
                    // الأدمن يرى جميع المستخدمين عدا الأدمن
                    usersQuery = usersQuery.Where(u => u.Role != "Admin");
                }
                else if (currentUserRole == "Manager")
                {
                    // المانجر يرى نفسه + الفنيين في شبكته
                    usersQuery = usersQuery.Where(u =>
                        (u.Id == currentUserId) ||
                        (u.Role == "Technician" && u.NetworkId == networkFilter));
                }
                else
                {
                    // المستخدمين العاديين لا يرون إحصائيات
                    return Array.Empty<UserEvaluationStatistics>();
                }

                if (!string.IsNullOrEmpty(networkFilter))
                {
                    usersQuery = usersQuery.Where(u => u.NetworkId == networkFilter);
                }

                var users = await usersQuery.ToListAsync();
                var statistics = new List<UserEvaluationStatistics>();

                foreach (var user in users)
                {
                    var userStats = await GetUserStatisticsAsync(user.Id, startDate, endDate);
                    if (userStats != null)
                    {
                        statistics.Add(userStats);
                    }
                }

                return statistics.OrderByDescending(s => s.AverageScore).ToList();
            }, TimeSpan.FromMinutes(10));
        }

        public async Task<IEnumerable<User>> GetUsersNotEvaluatedTodayAsync(string? networkFilter = null)
        {
            var today = DateTime.Today;
            var cacheKey = $"users_not_evaluated_{today:yyyyMMdd}_{networkFilter ?? "null"}";

            return await _cacheService.GetOrCreateAsync<IEnumerable<User>>(cacheKey, async () =>
            {
                using var scope = _serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                if (context.Users == null || context.UserEvaluations == null)
                    return Array.Empty<User>();

                var usersQuery = context.Users.Include(u => u.Network)
                    .Where(u => u.Role == "Technician" && u.IsActive); // فقط الفنيين النشطين

                if (!string.IsNullOrEmpty(networkFilter))
                {
                    usersQuery = usersQuery.Where(u => u.NetworkId == networkFilter);
                }

                var evaluatedUserIds = await context.UserEvaluations
                    .Where(e => e.EvaluationDate.Date == today)
                    .Select(e => e.UserId)
                    .ToListAsync();

                return await usersQuery
                    .Where(u => !evaluatedUserIds.Contains(u.Id))
                    .OrderBy(u => u.Name)
                    .ToListAsync();
            }, TimeSpan.FromMinutes(30));
        }

        public async Task<IEnumerable<User>> GetEvaluableUsersAsync(string? networkFilter = null, string? currentUserRole = null)
        {
            var cacheKey = $"evaluable_users_{networkFilter ?? "null"}_{currentUserRole ?? "null"}";

            return await _cacheService.GetOrCreateAsync<IEnumerable<User>>(cacheKey, async () =>
            {
                using var scope = _serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                if (context.Users == null)
                    return Array.Empty<User>();

                var usersQuery = context.Users.Include(u => u.Network)
                    .Where(u => u.IsActive); // المستخدمين النشطين فقط

                // تطبيق فلتر الأدوار حسب المستخدم الحالي
                if (currentUserRole == "Admin")
                {
                    // الأدمن يرى جميع المستخدمين
                    usersQuery = usersQuery.Where(u => true);
                }
                else if (currentUserRole == "Manager")
                {
                    // المانجر يرى الفنيين فقط
                    usersQuery = usersQuery.Where(u => u.Role == "Technician");
                }
                else
                {
                    // المستخدمين العاديين لا يمكنهم تقييم أحد
                    return Array.Empty<User>();
                }

                if (!string.IsNullOrEmpty(networkFilter))
                {
                    usersQuery = usersQuery.Where(u => u.NetworkId == networkFilter);
                }

                return await usersQuery
                    .OrderBy(u => u.Name)
                    .ToListAsync();
            }, TimeSpan.FromHours(1)); // تخزين لمدة ساعة لأن قائمة المستخدمين لا تتغير كثيراً
        }

        public async Task<bool> IsUserEvaluatedOnDateAsync(string userId, DateTime date)
        {
            if (string.IsNullOrEmpty(userId))
                return false;

            using var scope = _serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

            if (context.UserEvaluations == null)
                return false;

            return await context.UserEvaluations
                .AnyAsync(e => e.UserId == userId && e.EvaluationDate.Date == date.Date);
        }

        public async Task<decimal> GetAverageScoreAsync(string userId, DateTime? startDate = null, DateTime? endDate = null)
        {
            var evaluations = await GetByUserIdAsync(userId, startDate, endDate);
            return evaluations.Any() ? (decimal)evaluations.Average(e => e.Score) : 0;
        }

        public async Task<int> GetEvaluationCountAsync(string userId, DateTime? startDate = null, DateTime? endDate = null)
        {
            var evaluations = await GetByUserIdAsync(userId, startDate, endDate);
            return evaluations.Count();
        }

        public async Task<(int highest, int lowest)> GetScoreRangeAsync(string userId, DateTime? startDate = null, DateTime? endDate = null)
        {
            var evaluations = await GetByUserIdAsync(userId, startDate, endDate);
            if (!evaluations.Any())
                return (0, 0);

            return (evaluations.Max(e => e.Score), evaluations.Min(e => e.Score));
        }

        public async Task<IEnumerable<UserEvaluation>> SearchAsync(string searchTerm, string? networkFilter = null, DateTime? startDate = null, DateTime? endDate = null)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return Array.Empty<UserEvaluation>();

            using var scope = _serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

            if (context.UserEvaluations == null)
                return Array.Empty<UserEvaluation>();

            var query = context.UserEvaluations
                .Include(e => e.User)
                .Include(e => e.EvaluatedByUser)
                .Include(e => e.Network)
                .AsQueryable();

            // تطبيق فلاتر التاريخ والشبكة
            if (!string.IsNullOrEmpty(networkFilter))
                query = query.Where(e => e.NetworkId == networkFilter);

            if (startDate.HasValue)
                query = query.Where(e => e.EvaluationDate.Date >= startDate.Value.Date);

            if (endDate.HasValue)
                query = query.Where(e => e.EvaluationDate.Date <= endDate.Value.Date);

            // البحث في النصوص
            var searchLower = searchTerm.ToLower();
            query = query.Where(e =>
                e.User!.Name.ToLower().Contains(searchLower) ||
                e.User!.Username.ToLower().Contains(searchLower) ||
                e.EvaluatedByUser!.Name.ToLower().Contains(searchLower) ||
                (e.Notes != null && e.Notes.ToLower().Contains(searchLower)));

            return await query.OrderByDescending(e => e.EvaluationDate)
                             .ThenBy(e => e.User!.Name)
                             .ToListAsync();
        }

        public async Task<IEnumerable<UserEvaluation>> GetFilteredAsync(
            string? networkFilter = null,
            string? userId = null,
            string? evaluatedBy = null,
            DateTime? startDate = null,
            DateTime? endDate = null,
            int? minScore = null,
            int? maxScore = null,
            string? searchText = null)
        {
            using var scope = _serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

            if (context.UserEvaluations == null)
                return Array.Empty<UserEvaluation>();

            var query = context.UserEvaluations
                .Include(e => e.User)
                .Include(e => e.EvaluatedByUser)
                .Include(e => e.Network)
                .AsQueryable();

            // تطبيق الفلاتر
            if (!string.IsNullOrEmpty(networkFilter))
                query = query.Where(e => e.NetworkId == networkFilter);

            if (!string.IsNullOrEmpty(userId))
                query = query.Where(e => e.UserId == userId);

            if (!string.IsNullOrEmpty(evaluatedBy))
                query = query.Where(e => e.EvaluatedBy == evaluatedBy);

            if (startDate.HasValue)
                query = query.Where(e => e.EvaluationDate.Date >= startDate.Value.Date);

            if (endDate.HasValue)
                query = query.Where(e => e.EvaluationDate.Date <= endDate.Value.Date);

            if (minScore.HasValue)
                query = query.Where(e => e.Score >= minScore.Value);

            if (maxScore.HasValue)
                query = query.Where(e => e.Score <= maxScore.Value);

            if (!string.IsNullOrWhiteSpace(searchText))
            {
                var searchLower = searchText.ToLower();
                query = query.Where(e =>
                    e.User!.Name.ToLower().Contains(searchLower) ||
                    e.User!.Username.ToLower().Contains(searchLower) ||
                    e.EvaluatedByUser!.Name.ToLower().Contains(searchLower) ||
                    (e.Notes != null && e.Notes.ToLower().Contains(searchLower)));
            }

            return await query.OrderByDescending(e => e.EvaluationDate)
                             .ThenBy(e => e.User!.Name)
                             .ToListAsync();
        }

        public async Task<int> GetCountAsync(
            string? networkFilter = null,
            string? userId = null,
            DateTime? startDate = null,
            DateTime? endDate = null)
        {
            using var scope = _serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

            if (context.UserEvaluations == null)
                return 0;

            var query = context.UserEvaluations.AsQueryable();

            // تطبيق الفلاتر
            if (!string.IsNullOrEmpty(networkFilter))
                query = query.Where(e => e.NetworkId == networkFilter);

            if (!string.IsNullOrEmpty(userId))
                query = query.Where(e => e.UserId == userId);

            if (startDate.HasValue)
                query = query.Where(e => e.EvaluationDate.Date >= startDate.Value.Date);

            if (endDate.HasValue)
                query = query.Where(e => e.EvaluationDate.Date <= endDate.Value.Date);

            return await query.CountAsync();
        }

        private System.Threading.Tasks.Task ClearRelatedCacheAsync(string? networkId, string? userId)
        {
            // مسح الكاش المتعلق بالتقييمات
            _cacheService.RemoveByPattern("user_evaluations_");
            _cacheService.RemoveByPattern("all_user_stats_");
            _cacheService.RemoveByPattern("users_not_evaluated_");
            _cacheService.RemoveByPattern("evaluable_users_");

            if (!string.IsNullOrEmpty(userId))
            {
                _cacheService.RemoveByPattern($"user_stats_{userId}");
            }

            return System.Threading.Tasks.Task.CompletedTask;
        }
    }
}
