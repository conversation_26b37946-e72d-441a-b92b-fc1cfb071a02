using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using NetworkManagement.Helpers;
using NetworkManagement.Models;
using NetworkManagement.Services;
using NetworkManagement.Views;

namespace NetworkManagement.ViewModels
{
    public partial class SiteDialogViewModel : ObservableObject
    {
        private readonly ISiteService _siteService;
        private readonly IAuthService _authService;
        private readonly INetworkService _networkService;
        private readonly INotificationService _notificationService;
        private readonly IInventoryService _inventoryService;
        private readonly ISiteInventoryService _siteInventoryService;

        [ObservableProperty]
        private string title = "إضافة موقع جديد";

        [ObservableProperty]
        private string name = string.Empty;

        [ObservableProperty]
        private string address = string.Empty;

        [ObservableProperty]
        private string phone = string.Empty;

        [ObservableProperty]
        private double? gpsLat;

        [ObservableProperty]
        private double? gpsLng;

        [ObservableProperty]
        private string powerSource = string.Empty;

        // نظام البطاريات
        [ObservableProperty]
        private string batteryType = string.Empty;

        [ObservableProperty]
        private string batterySize = string.Empty;

        // نظام القواعد
        [ObservableProperty]
        private string baseType = string.Empty;

        [ObservableProperty]
        private string baseCount = string.Empty;

        // نظام الصناديق
        [ObservableProperty]
        private string boxType = string.Empty;

        [ObservableProperty]
        private string boxCount = string.Empty;

        // نظام أسلاك الشبكة
        [ObservableProperty]
        private string networkCableType = string.Empty;

        [ObservableProperty]
        private string networkCableLength = string.Empty;

        [ObservableProperty]
        private string powerCableType = string.Empty;

        [ObservableProperty]
        private string powerCableLength = string.Empty;

        // قوائم الخيارات من المخزون
        [ObservableProperty]
        private List<string> batteryTypes = new();

        [ObservableProperty]
        private List<string> baseTypes = new();

        [ObservableProperty]
        private List<string> boxTypes = new();

        [ObservableProperty]
        private List<string> networkCableTypes = new();

        [ObservableProperty]
        private List<string> powerCableTypes = new();

        [ObservableProperty]
        private List<string> availableDeviceTypes = new();

        [ObservableProperty]
        private string deviceTypesText = string.Empty;

        [ObservableProperty]
        private ObservableCollection<AdditionalInventoryItemViewModel> additionalInventoryItems = new();

        [ObservableProperty]
        private List<Inventory> availableInventoryItems = new();

        [ObservableProperty]
        private string selectedNetworkId = string.Empty;

        [ObservableProperty]
        private List<Network> availableNetworks = new();

        [ObservableProperty]
        private bool isLoading = false;

        [ObservableProperty]
        private string errorMessage = string.Empty;

        private Site? _editingSite;
        public bool IsEditMode => _editingSite != null;

        public event EventHandler<Site>? SiteSaved;
        public event EventHandler? DialogClosed;

        public SiteDialogViewModel(ISiteService siteService, IAuthService authService, INetworkService networkService, INotificationService notificationService, ISiteInventoryService siteInventoryService, IInventoryService inventoryService)
        {
            _siteService = siteService;
            _authService = authService;
            _networkService = networkService;
            _notificationService = notificationService;
            _siteInventoryService = siteInventoryService;
            _inventoryService = inventoryService;
            _ = LoadNetworksAsync();
            _ = LoadInventoryOptionsAsync();
        }

        public void SetEditSite(Site site)
        {
            _editingSite = site;
            Title = "تحرير الموقع";

            Name = site.Name;
            Address = site.Address ?? string.Empty;
            Phone = site.Phone ?? string.Empty;
            GpsLat = site.GpsLat;
            GpsLng = site.GpsLng;
            PowerSource = site.PowerSource ?? string.Empty;

            // نظام البطاريات
            BatteryType = site.BatteryType ?? string.Empty;
            BatterySize = site.BatterySize ?? string.Empty;

            // نظام القواعد
            BaseType = site.BaseType ?? string.Empty;
            BaseCount = site.BaseCount?.ToString() ?? string.Empty;

            // نظام الصناديق
            BoxType = site.BoxType ?? string.Empty;
            BoxCount = site.BoxCount?.ToString() ?? string.Empty;

            // نظام أسلاك الشبكة
            NetworkCableType = site.NetworkCableType ?? string.Empty;
            NetworkCableLength = site.NetworkCableLength?.ToString() ?? string.Empty;
            PowerCableType = site.PowerCableType ?? string.Empty;
            PowerCableLength = site.PowerCableLength?.ToString() ?? string.Empty;

            // تحميل العناصر الإضافية
            AdditionalInventoryItems.Clear();
            if (!string.IsNullOrEmpty(site.AdditionalInventoryItems))
            {
                try
                {
                    var additionalItems = JsonSerializer.Deserialize<List<Models.AdditionalInventoryItem>>(site.AdditionalInventoryItems);
                    if (additionalItems != null)
                    {
                        foreach (var item in additionalItems)
                        {
                            AdditionalInventoryItems.Add(new AdditionalInventoryItemViewModel
                            {
                                InventoryId = item.InventoryId,
                                Name = item.Name,
                                Quantity = item.Quantity
                            });
                        }
                    }
                }
                catch (JsonException)
                {
                    // تجاهل أخطاء JSON
                }
            }

            SelectedNetworkId = site.NetworkId ?? string.Empty;
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task SaveAsync()
        {
            try
            {
                if (!ValidateInput())
                    return;

                IsLoading = true;
                ErrorMessage = string.Empty;

                var site = _editingSite ?? new Site();

                site.Name = Name.Trim();
                site.Address = string.IsNullOrWhiteSpace(Address) ? null : Address.Trim();
                site.Phone = string.IsNullOrWhiteSpace(Phone) ? null : Phone.Trim();
                site.GpsLat = GpsLat;
                site.GpsLng = GpsLng;
                site.PowerSource = string.IsNullOrWhiteSpace(PowerSource) ? null : PowerSource.Trim();

                // نظام البطاريات
                site.BatteryType = string.IsNullOrWhiteSpace(BatteryType) ? null : BatteryType.Trim();
                site.BatterySize = string.IsNullOrWhiteSpace(BatterySize) ? null : BatterySize.Trim();

                // نظام القواعد
                site.BaseType = string.IsNullOrWhiteSpace(BaseType) ? null : BaseType.Trim();
                site.BaseCount = ParseIntegerSafely(BaseCount);

                // نظام الصناديق
                site.BoxType = string.IsNullOrWhiteSpace(BoxType) ? null : BoxType.Trim();
                site.BoxCount = ParseIntegerSafely(BoxCount);

                // نظام أسلاك الشبكة
                site.NetworkCableType = string.IsNullOrWhiteSpace(NetworkCableType) ? null : NetworkCableType.Trim();
                site.NetworkCableLength = ParseIntegerSafely(NetworkCableLength);
                site.PowerCableType = string.IsNullOrWhiteSpace(PowerCableType) ? null : PowerCableType.Trim();
                site.PowerCableLength = ParseIntegerSafely(PowerCableLength);

                // العناصر الإضافية من المخزون
                if (AdditionalInventoryItems.Count > 0)
                {
                    var additionalItems = AdditionalInventoryItems.Select(item => new Models.AdditionalInventoryItem
                    {
                        InventoryId = item.InventoryId,
                        Name = item.Name,
                        Quantity = item.Quantity
                    }).ToList();
                    site.AdditionalInventoryItems = JsonSerializer.Serialize(additionalItems);

                    // تسجيل للتأكد من الكميات
                    System.Diagnostics.Debug.WriteLine($"حفظ العناصر الإضافية: {string.Join(", ", additionalItems.Select(i => $"{i.Name}: {i.Quantity}"))}");
                }
                else
                {
                    site.AdditionalInventoryItems = null;
                }
                // تعيين NetworkId - تعيين شبكة المستخدم تلقائ<|im_start|> للـ Network Manager
                if (!string.IsNullOrWhiteSpace(SelectedNetworkId))
                {
                    // استخدام الشبكة المختارة
                    site.NetworkId = SelectedNetworkId;
                }
                else
                {
                    // إذا كان المستخدم Network Manager، استخدم شبكته تلقائ<|im_start|>
                    var currentUser = _authService.CurrentUser;
                    if (_authService.IsAdmin)
                    {
                        // للأدمن: يمكن ترك الحقل فارغ
                        site.NetworkId = null;
                    }
                    else if (_authService.IsNetworkManager && !string.IsNullOrEmpty(currentUser?.NetworkId))
                    {
                        // للـ Network Manager: استخدم شبكته تلقائياً
                        site.NetworkId = currentUser.NetworkId;
                    }
                    else
                    {
                        // لباقي المستخدمين: يمكن ترك الحقل فارغ
                        site.NetworkId = null;
                    }
                }

                if (_editingSite == null)
                {
                    // Adding new site - خصم المخزون تلقائياً
                    site.CreatedAt = DateTime.Now;
                    await _siteService.CreateAsync(site);

                    // خصم المواد من المخزون (دعم القيم السالبة)
                    var networkId = _authService.CurrentUser?.NetworkId;
                    try
                    {
                        var inventoryResult = await _siteInventoryService.DeductInventoryForNewSiteAsync(site, networkId);
                        if (inventoryResult.Success && inventoryResult.DeductedItems.Count > 0)
                        {
                            var deductedItemsText = string.Join(", ", inventoryResult.DeductedItems);
                            System.Diagnostics.Debug.WriteLine($"تم خصم المواد من المخزون: {deductedItemsText}");
                        }

                        // العناصر الإضافية يتم خصمها تلقائياً بواسطة SiteInventoryService
                        // لا حاجة لخصم إضافي هنا
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"تحذير: فشل في تحديث المخزون: {ex.Message}");
                        // لا نمنع إضافة الموقع حتى لو فشل تحديث المخزون
                    }

                    // إشعار إضافة موقع
                    var userName = _authService.CurrentUser?.Name ?? "المستخدم";
                    var userId = _authService.CurrentUser?.Id;
                    await _notificationService.ShowOperationAsync(
                        "تم إضافة الموقع",
                        $"قام {userName} بإضافة موقع جديد: {site.Name}",
                        NotificationType.Success,
                        userName, userId, "إضافة", site.Name, site.NetworkId);
                }
                else
                {
                    // Updating existing site - تحديث المخزون (دعم القيم السالبة)
                    var networkId = _authService.CurrentUser?.NetworkId;
                    try
                    {
                        // العناصر الإضافية يتم إرجاعها تلقائياً بواسطة SiteInventoryService
                        // لا حاجة لإرجاع إضافي هنا

                        var inventoryResult = await _siteInventoryService.UpdateInventoryForSiteModificationAsync(_editingSite, site, networkId);
                        if (inventoryResult.Success && inventoryResult.DeductedItems.Count > 0)
                        {
                            var deductedItemsText = string.Join(", ", inventoryResult.DeductedItems);
                            System.Diagnostics.Debug.WriteLine($"تم تحديث المخزون: {deductedItemsText}");
                        }

                        // العناصر الإضافية يتم خصمها تلقائياً بواسطة SiteInventoryService
                        // لا حاجة لخصم إضافي هنا
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"تحذير: فشل في تحديث المخزون: {ex.Message}");
                        // لا نمنع تحديث الموقع حتى لو فشل تحديث المخزون
                    }

                    await _siteService.UpdateAsync(site);

                    // إشعار تحديث موقع
                    var userName = _authService.CurrentUser?.Name ?? "المستخدم";
                    var userId = _authService.CurrentUser?.Id;
                    await _notificationService.ShowOperationAsync(
                        "تم تحديث الموقع",
                        $"قام {userName} بتحديث الموقع: {site.Name}",
                        NotificationType.Success,
                        userName, userId, "تحديث", site.Name, site.NetworkId);
                }

                SiteSaved?.Invoke(this, site);
                DialogClosed?.Invoke(this, EventArgs.Empty);
            }
            catch (Exception ex)
            {
                ErrorMessage = $"خطأ في حفظ الموقع: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private void Cancel()
        {
            DialogClosed?.Invoke(this, EventArgs.Empty);
        }

        private bool ValidateInput()
        {
            ErrorMessage = string.Empty;

            if (string.IsNullOrWhiteSpace(Name))
            {
                ErrorMessage = "اسم الموقع مطلوب";
                return false;
            }

            // Validate Phone format if provided
            if (!string.IsNullOrWhiteSpace(Phone))
            {
                if (Phone.Trim().Length < 7)
                {
                    ErrorMessage = "رقم الهاتف قصير جداً";
                    return false;
                }
            }

            return true;
        }

        private async System.Threading.Tasks.Task LoadNetworksAsync()
        {
            try
            {
                // استخدام NetworkHelper لتحميل الشبكات مع تطبيق الصلاحيات
                var (networks, defaultNetworkId) = await NetworkHelper.LoadNetworksForSelectionAsync(
                    _authService,
                    _networkService);

                AvailableNetworks = networks;

                // تعيين الشبكة الافتراضية
                if (!string.IsNullOrEmpty(defaultNetworkId))
                {
                    SelectedNetworkId = defaultNetworkId;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading networks: {ex.Message}");
            }
        }

        [RelayCommand]
        private System.Threading.Tasks.Task SelectFromMapAsync()
        {
            try
            {
                var mapDialog = new Views.MapSelectionDialog(GpsLat, GpsLng);
                var result = mapDialog.ShowDialog();

                if (result == true)
                {
                    var selectedLocation = mapDialog.GetSelectedLocation();
                    if (selectedLocation.HasValue)
                    {
                        GpsLat = selectedLocation.Value.Latitude;
                        GpsLng = selectedLocation.Value.Longitude;
                    }
                }

                return System.Threading.Tasks.Task.CompletedTask;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error opening map dialog: {ex.Message}");
                ErrorMessage = "خطأ في فتح الخريطة";
                return System.Threading.Tasks.Task.CompletedTask;
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task AddInventoryItemAsync()
        {
            try
            {
                // تحميل جميع عناصر المخزون المتاحة
                await LoadAllInventoryItemsAsync();

                // إنشاء نافذة اختيار عنصر من المخزون
                var dialog = new InventorySelectionDialog(AvailableInventoryItems);
                var result = dialog.ShowDialog();

                if (result == true && dialog.SelectedInventoryItem != null)
                {
                    var selectedItem = dialog.SelectedInventoryItem;
                    var quantity = dialog.SelectedQuantity;

                    System.Diagnostics.Debug.WriteLine($"إضافة عنصر للموقع - {selectedItem.Name}: الكمية المطلوبة = {quantity}");

                    // التحقق من عدم وجود العنصر مسبقاً
                    var existingItem = AdditionalInventoryItems.FirstOrDefault(x => x.InventoryId == selectedItem.Id);
                    if (existingItem != null)
                    {
                        // تحديث الكمية إذا كان العنصر موجود
                        var oldQuantity = existingItem.Quantity;
                        existingItem.Quantity += quantity;
                        System.Diagnostics.Debug.WriteLine($"تحديث عنصر موجود - {selectedItem.Name}: {oldQuantity} -> {existingItem.Quantity}");
                    }
                    else
                    {
                        // إضافة عنصر جديد
                        var newItem = new AdditionalInventoryItemViewModel
                        {
                            InventoryId = selectedItem.Id,
                            Name = $"{selectedItem.Name} (متوفر: {selectedItem.Quantity} {selectedItem.Unit ?? "قطعة"})",
                            Quantity = quantity
                        };
                        AdditionalInventoryItems.Add(newItem);
                        System.Diagnostics.Debug.WriteLine($"إضافة عنصر جديد - {selectedItem.Name}: الكمية = {quantity}");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error adding inventory item: {ex.Message}");
            }

            await System.Threading.Tasks.Task.CompletedTask;
        }

        [RelayCommand]
        private void RemoveInventoryItem(AdditionalInventoryItemViewModel item)
        {
            System.Diagnostics.Debug.WriteLine($"حذف عنصر من الموقع - {item.Name}: الكمية = {item.Quantity}");
            AdditionalInventoryItems.Remove(item);
            System.Diagnostics.Debug.WriteLine($"عدد العناصر المتبقية: {AdditionalInventoryItems.Count}");
        }

        private async System.Threading.Tasks.Task LoadInventoryOptionsAsync()
        {
            try
            {
                var networkId = _authService.CurrentUser?.NetworkId;

                // تحميل أنواع البطاريات المتاحة في المخزون
                var batteries = await _siteInventoryService.GetAvailableBatteryTypesAsync(networkId);
                BatteryTypes = batteries;

                // تحميل أنواع القواعد المتاحة في المخزون
                var bases = await _siteInventoryService.GetAvailableBaseTypesAsync(networkId);
                BaseTypes = bases;

                // تحميل أنواع الصناديق المتاحة في المخزون
                var boxes = await _siteInventoryService.GetAvailableBoxTypesAsync(networkId);
                BoxTypes = boxes;

                // لا نحمل العناصر هنا، سيتم تحميلها عند الحاجة
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading inventory options: {ex.Message}");
                BatteryTypes = new List<string>();
                BaseTypes = new List<string>();
                BoxTypes = new List<string>();
                AvailableInventoryItems = new List<Inventory>();
            }
        }

        /// <summary>
        /// تحميل جميع عناصر المخزون المتاحة
        /// </summary>
        private async System.Threading.Tasks.Task LoadAllInventoryItemsAsync()
        {
            try
            {
                var networkId = _authService.CurrentUser?.NetworkId;
                var allInventory = await _inventoryService.GetAllAsync(networkId);
                AvailableInventoryItems = allInventory.ToList();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading all inventory items: {ex.Message}");
                AvailableInventoryItems = new List<Inventory>();
            }
        }

        private int? ParseIntegerSafely(string value)
        {
            if (string.IsNullOrWhiteSpace(value))
                return null;

            if (int.TryParse(value.Trim(), out var result))
                return result;

            return null;
        }

        /// <summary>
        /// خصم العناصر الإضافية من المخزون - لم تعد مستخدمة
        /// يتم الخصم تلقائياً بواسطة SiteInventoryService
        /// </summary>
        [Obsolete("يتم الخصم تلقائياً بواسطة SiteInventoryService")]
        private async System.Threading.Tasks.Task DeductAdditionalInventoryItems(string? additionalItemsJson, string? networkId)
        {
            // هذه الوظيفة لم تعد مستخدمة لتجنب الخصم المزدوج
            // SiteInventoryService يتولى خصم العناصر الإضافية
            await System.Threading.Tasks.Task.CompletedTask;
        }

        /// <summary>
        /// إرجاع العناصر الإضافية للمخزون - لم تعد مستخدمة
        /// يتم الإرجاع تلقائياً بواسطة SiteInventoryService
        /// </summary>
        [Obsolete("يتم الإرجاع تلقائياً بواسطة SiteInventoryService")]
        private async System.Threading.Tasks.Task ReturnAdditionalInventoryItems(string? additionalItemsJson, string? networkId)
        {
            // هذه الوظيفة لم تعد مستخدمة لتجنب التضارب
            // SiteInventoryService يتولى إرجاع العناصر الإضافية
            await System.Threading.Tasks.Task.CompletedTask;
        }
    }

    public partial class AdditionalInventoryItemViewModel : ObservableObject
    {
        [ObservableProperty]
        private string inventoryId = string.Empty;

        [ObservableProperty]
        private string name = string.Empty;

        [ObservableProperty]
        private int quantity = 1;

        /// <summary>
        /// عرض النص مع الكمية
        /// </summary>
        public string DisplayText => $"{Name} - الكمية: {Quantity}";

        partial void OnNameChanged(string value)
        {
            OnPropertyChanged(nameof(DisplayText));
        }

        partial void OnQuantityChanged(int value)
        {
            OnPropertyChanged(nameof(DisplayText));
        }
    }

    /// <summary>
    /// نافذة اختيار عنصر من المخزون
    /// </summary>
    public class InventorySelectionDialog : System.Windows.Window
    {
        public Inventory? SelectedInventoryItem { get; private set; }
        public int SelectedQuantity { get; private set; } = 1;

        private System.Windows.Controls.ListBox _inventoryListBox = null!;
        private System.Windows.Controls.TextBox _quantityTextBox = null!;

        public InventorySelectionDialog(List<Inventory> inventoryItems)
        {
            InitializeDialog(inventoryItems);
        }

        private void InitializeDialog(List<Inventory> inventoryItems)
        {
            Title = "اختيار عنصر من المخزون";
            Width = 600;
            Height = 500;
            WindowStartupLocation = System.Windows.WindowStartupLocation.CenterOwner;
            ResizeMode = System.Windows.ResizeMode.NoResize;
            FlowDirection = System.Windows.FlowDirection.RightToLeft;

            // تطبيق الثيم الأسود
            Background = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(33, 33, 33));
            Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.White);

            var grid = new System.Windows.Controls.Grid();
            grid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition { Height = System.Windows.GridLength.Auto });
            grid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition { Height = new System.Windows.GridLength(1, System.Windows.GridUnitType.Star) });
            grid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition { Height = System.Windows.GridLength.Auto });
            grid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition { Height = System.Windows.GridLength.Auto });

            // عنوان النافذة
            var titleLabel = new System.Windows.Controls.Label
            {
                Content = "اختيار عنصر من المخزون",
                FontSize = 16,
                FontWeight = System.Windows.FontWeights.Bold,
                Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.White),
                HorizontalAlignment = System.Windows.HorizontalAlignment.Center,
                Margin = new System.Windows.Thickness(10)
            };
            System.Windows.Controls.Grid.SetRow(titleLabel, 0);
            grid.Children.Add(titleLabel);

            // قائمة العناصر مع تحسين التصميم
            _inventoryListBox = new System.Windows.Controls.ListBox
            {
                ItemsSource = inventoryItems,
                Margin = new System.Windows.Thickness(15),
                Background = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(45, 45, 45)),
                Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.White),
                BorderBrush = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(96, 96, 96)),
                BorderThickness = new System.Windows.Thickness(1),
                FontSize = 13
            };

            // تخصيص عرض العناصر
            var itemTemplate = new System.Windows.DataTemplate();
            var stackPanelFactory = new System.Windows.FrameworkElementFactory(typeof(System.Windows.Controls.StackPanel));
            stackPanelFactory.SetValue(System.Windows.Controls.StackPanel.OrientationProperty, System.Windows.Controls.Orientation.Vertical);
            stackPanelFactory.SetValue(System.Windows.Controls.StackPanel.MarginProperty, new System.Windows.Thickness(5));

            var nameTextFactory = new System.Windows.FrameworkElementFactory(typeof(System.Windows.Controls.TextBlock));
            nameTextFactory.SetBinding(System.Windows.Controls.TextBlock.TextProperty, new System.Windows.Data.Binding("Name"));
            nameTextFactory.SetValue(System.Windows.Controls.TextBlock.FontWeightProperty, System.Windows.FontWeights.Bold);
            nameTextFactory.SetValue(System.Windows.Controls.TextBlock.ForegroundProperty, new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.White));

            var detailsTextFactory = new System.Windows.FrameworkElementFactory(typeof(System.Windows.Controls.TextBlock));
            var detailsBinding = new System.Windows.Data.MultiBinding();
            detailsBinding.StringFormat = "الفئة: {0} | الكمية المتاحة: {1} {2}";
            detailsBinding.Bindings.Add(new System.Windows.Data.Binding("Category"));
            detailsBinding.Bindings.Add(new System.Windows.Data.Binding("Quantity"));
            detailsBinding.Bindings.Add(new System.Windows.Data.Binding("Unit"));
            detailsTextFactory.SetBinding(System.Windows.Controls.TextBlock.TextProperty, detailsBinding);
            detailsTextFactory.SetValue(System.Windows.Controls.TextBlock.FontSizeProperty, 11.0);
            detailsTextFactory.SetValue(System.Windows.Controls.TextBlock.ForegroundProperty, new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(200, 200, 200)));

            stackPanelFactory.AppendChild(nameTextFactory);
            stackPanelFactory.AppendChild(detailsTextFactory);
            itemTemplate.VisualTree = stackPanelFactory;
            _inventoryListBox.ItemTemplate = itemTemplate;

            System.Windows.Controls.Grid.SetRow(_inventoryListBox, 1);
            grid.Children.Add(_inventoryListBox);

            // حقل الكمية مع تحسين التصميم
            var quantityPanel = new System.Windows.Controls.StackPanel
            {
                Orientation = System.Windows.Controls.Orientation.Horizontal,
                HorizontalAlignment = System.Windows.HorizontalAlignment.Center,
                Margin = new System.Windows.Thickness(15, 10, 15, 15)
            };

            var quantityLabel = new System.Windows.Controls.Label
            {
                Content = "الكمية المطلوبة:",
                Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.White),
                VerticalAlignment = System.Windows.VerticalAlignment.Center,
                FontSize = 13,
                FontWeight = System.Windows.FontWeights.Medium
            };
            quantityPanel.Children.Add(quantityLabel);

            _quantityTextBox = new System.Windows.Controls.TextBox
            {
                Text = "1",
                Width = 120,
                Height = 30,
                Margin = new System.Windows.Thickness(10, 0, 0, 0),
                Background = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(45, 45, 45)),
                Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.White),
                BorderBrush = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(96, 96, 96)),
                BorderThickness = new System.Windows.Thickness(1),
                FontSize = 13,
                TextAlignment = System.Windows.TextAlignment.Center,
                VerticalContentAlignment = System.Windows.VerticalAlignment.Center
            };
            quantityPanel.Children.Add(_quantityTextBox);

            System.Windows.Controls.Grid.SetRow(quantityPanel, 2);
            grid.Children.Add(quantityPanel);

            // أزرار التحكم مع تحسين التصميم
            var buttonPanel = new System.Windows.Controls.StackPanel
            {
                Orientation = System.Windows.Controls.Orientation.Horizontal,
                HorizontalAlignment = System.Windows.HorizontalAlignment.Center,
                Margin = new System.Windows.Thickness(15, 10, 15, 20)
            };

            var okButton = new System.Windows.Controls.Button
            {
                Content = "موافق",
                Width = 100,
                Height = 35,
                Margin = new System.Windows.Thickness(10, 0, 10, 0),
                Background = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(33, 150, 243)),
                Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.White),
                BorderBrush = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(33, 150, 243)),
                BorderThickness = new System.Windows.Thickness(1),
                FontSize = 13,
                FontWeight = System.Windows.FontWeights.Medium,
                Cursor = System.Windows.Input.Cursors.Hand
            };
            okButton.Click += OkButton_Click;
            buttonPanel.Children.Add(okButton);

            var cancelButton = new System.Windows.Controls.Button
            {
                Content = "إلغاء",
                Width = 100,
                Height = 35,
                Margin = new System.Windows.Thickness(10, 0, 10, 0),
                Background = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(96, 96, 96)),
                Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.White),
                BorderBrush = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(96, 96, 96)),
                BorderThickness = new System.Windows.Thickness(1),
                FontSize = 13,
                FontWeight = System.Windows.FontWeights.Medium,
                Cursor = System.Windows.Input.Cursors.Hand
            };
            cancelButton.Click += CancelButton_Click;
            buttonPanel.Children.Add(cancelButton);

            System.Windows.Controls.Grid.SetRow(buttonPanel, 3);
            grid.Children.Add(buttonPanel);

            Content = grid;
        }

        private void OkButton_Click(object sender, System.Windows.RoutedEventArgs e)
        {
            SelectedInventoryItem = _inventoryListBox.SelectedItem as Inventory;

            if (SelectedInventoryItem == null)
            {
                System.Windows.MessageBox.Show("يرجى اختيار عنصر من المخزون", "تنبيه", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                return;
            }

            if (!int.TryParse(_quantityTextBox.Text, out var quantity) || quantity <= 0)
            {
                System.Windows.MessageBox.Show("يرجى إدخال كمية صحيحة (أكبر من صفر)", "تنبيه", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                return;
            }

            if (quantity > SelectedInventoryItem.Quantity)
            {
                var message = $"الكمية المطلوبة ({quantity}) أكبر من الكمية المتاحة ({SelectedInventoryItem.Quantity})\n\nهل تريد المتابعة؟";
                var result = System.Windows.MessageBox.Show(message, "تحذير", System.Windows.MessageBoxButton.YesNo, System.Windows.MessageBoxImage.Question);
                if (result != System.Windows.MessageBoxResult.Yes)
                    return;
            }

            SelectedQuantity = quantity;
            DialogResult = true;
        }

        private void CancelButton_Click(object sender, System.Windows.RoutedEventArgs e)
        {
            DialogResult = false;
        }
    }
}
