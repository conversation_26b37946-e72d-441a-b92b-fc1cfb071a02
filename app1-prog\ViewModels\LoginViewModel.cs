using System;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.EntityFrameworkCore;
using NetworkManagement.Data;
using NetworkManagement.Models;
using NetworkManagement.Services;
using NetworkManagement.Views;

namespace NetworkManagement.ViewModels
{
    public partial class LoginViewModel : ObservableObject
    {
        private readonly IAuthService _authService;
        private readonly ISettingsService _settingsService;

        [ObservableProperty]
        private string username = string.Empty;

        [ObservableProperty]
        private string password = string.Empty;

        [ObservableProperty]
        private bool rememberMe = true;

        [ObservableProperty]
        private bool isLoading = false;

        [ObservableProperty]
        private string errorMessage = string.Empty;

        [ObservableProperty]
        private bool hasError = false;

        public event EventHandler<string>? PasswordLoadRequested;

        public LoginViewModel(IAuthService authService, ISettingsService settingsService)
        {
            _authService = authService;
            _settingsService = settingsService;
            _ = LoadRememberedCredentialsAsync();
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task LoginAsync()
        {
            ClearError();

            if (string.IsNullOrWhiteSpace(Username))
            {
                SetError("يرجى إدخال اسم المستخدم");
                return;
            }

            if (string.IsNullOrWhiteSpace(Password))
            {
                SetError("يرجى إدخال كلمة المرور");
                return;
            }

            IsLoading = true;

            try
            {
                // استخدام AuthService للتحقق من المستخدم
                var user = await _authService.LoginAsync(Username, Password);

                if (user != null)
                {
                    // نجح تسجيل الدخول
                    if (RememberMe)
                    {
                        await _settingsService.SaveRememberedCredentialsAsync(Username, Password);
                    }
                    else
                    {
                        await _settingsService.ClearRememberedCredentialsAsync();
                    }

                    var mainWindow = new MainWindow();
                    mainWindow.Show();

                    Application.Current.Windows.OfType<LoginWindow>().FirstOrDefault()?.Close();
                }
                else
                {
                    SetError("اسم المستخدم أو كلمة المرور غير صحيحة");
                }
            }
            catch (Exception ex)
            {
                SetError($"خطأ في تسجيل الدخول: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }



        [RelayCommand]
        private void ClearError()
        {
            ErrorMessage = string.Empty;
            HasError = false;
        }

        [RelayCommand]
        private void ForgotPassword()
        {
            MessageBox.Show(
                "لاستعادة كلمة المرور، يرجى التواصل مع مدير النظام.\n\n" +
                "البريد الإلكتروني: <EMAIL>\n" +
                "الهاتف: +966-XX-XXX-XXXX",
                "استعادة كلمة المرور",
                MessageBoxButton.OK,
                MessageBoxImage.Information);
        }

        private void SetError(string message)
        {
            ErrorMessage = message;
            HasError = true;
        }

        private async System.Threading.Tasks.Task LoadRememberedCredentialsAsync()
        {
            try
            {
                var hasCredentials = await _settingsService.HasRememberedCredentialsAsync();
                if (hasCredentials)
                {
                    var (username, password) = await _settingsService.GetRememberedCredentialsAsync();
                    if (!string.IsNullOrEmpty(username) && !string.IsNullOrEmpty(password))
                    {
                        Username = username;
                        Password = password;
                        RememberMe = true;

                        PasswordLoadRequested?.Invoke(this, password);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading remembered credentials: {ex.Message}");
            }
        }
    }
}
