using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using NetworkManagement.Models;
using NetworkManagement.Services;
using NetworkManagement.Helpers;

namespace NetworkManagement.ViewModels
{
    public partial class TaskDialogViewModel : ObservableObject
    {
        private readonly ITaskService _taskService;
        private readonly IAuthService _authService;
        private readonly INetworkService _networkService;
        private readonly INotificationService _notificationService;

        [ObservableProperty]
        private string title = "إضافة مهمة جديدة";

        [ObservableProperty]
        private string description = string.Empty;

        [ObservableProperty]
        private string notes = string.Empty;

        [ObservableProperty]
        private string selectedStatus = TaskConstants.StatusDisplay.Pending;

        [ObservableProperty]
        private string selectedPriority = TaskConstants.PriorityDisplay.Medium;

        [ObservableProperty]
        private DateTime requestDate = DateTime.Now;

        [ObservableProperty]
        private DateTime? completedAt;

        [ObservableProperty]
        private string selectedNetworkId = string.Empty;

        [ObservableProperty]
        private ObservableCollection<Network> networks = new();

        [ObservableProperty]
        private bool isEditMode = false;

        [ObservableProperty]
        private bool isSaving = false;

        // خاصية للتحقق من كون المهمة مكتملة لإظهار تاريخ الإكمال
        public bool IsCompletedTask => SelectedStatus == "مكتمل" || SelectedStatus == "مكتملة";

        // تحديث خاصية IsCompletedTask عند تغيير الحالة
        partial void OnSelectedStatusChanged(string value)
        {
            OnPropertyChanged(nameof(IsCompletedTask));

            // إذا تم تغيير الحالة إلى مكتمل وليس هناك تاريخ إكمال، ضع التاريخ الحالي
            if (IsCompletedTask && !CompletedAt.HasValue)
            {
                CompletedAt = DateTime.Now;
            }
            // إذا تم تغيير الحالة إلى غير مكتمل، امسح تاريخ الإكمال
            else if (!IsCompletedTask)
            {
                CompletedAt = null;
            }
        }

        public ObservableCollection<string> StatusOptions { get; } = new(TaskConstants.StatusOptions);

        public ObservableCollection<string> PriorityOptions { get; } = new(TaskConstants.PriorityOptions);

        private Models.Task? _editingTask;
        public bool DialogResult { get; private set; }

        public TaskDialogViewModel(ITaskService taskService, IAuthService authService, INetworkService networkService, INotificationService notificationService)
        {
            _taskService = taskService;
            _authService = authService;
            _networkService = networkService;
            _notificationService = notificationService;
        }

        public async System.Threading.Tasks.Task InitializeAsync(Models.Task? task = null)
        {
            try
            {
                // Load networks
                var allNetworks = await _networkService.GetAllAsync();
                Networks.Clear();
                foreach (var network in allNetworks)
                {
                    Networks.Add(network);
                }

                // Set default network (اختياري)
                var currentUser = _authService.CurrentUser;
                if (currentUser?.Role?.Equals("Admin", StringComparison.OrdinalIgnoreCase) != true && !string.IsNullOrEmpty(currentUser?.NetworkId))
                {
                    // للمدراء: تعيين شبكتهم كافتراضي
                    SelectedNetworkId = currentUser.NetworkId;
                }
                // للأدمن: ترك الحقل فارغ ليختار بنفسه

                // If editing existing task
                if (task != null)
                {
                    _editingTask = task;
                    IsEditMode = true;
                    Title = "تعديل المهمة";

                    Description = task.Description;
                    Notes = task.Notes ?? string.Empty;
                    SelectedStatus = GetStatusDisplay(task.Status);
                    SelectedPriority = GetPriorityDisplay(task.Priority ?? "medium");
                    RequestDate = task.RequestDate;
                    CompletedAt = task.CompletedAt;
                    SelectedNetworkId = task.NetworkId ?? string.Empty;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error initializing task dialog: {ex.Message}");
                MessageBox.Show(
                    $"خطأ في تحميل البيانات:\n{ex.Message}",
                    "خطأ",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task SaveAsync()
        {
            if (!await ValidateInputAsync())
                return;

            IsSaving = true;
            try
            {
                var currentUser = _authService.CurrentUser;

                if (IsEditMode && _editingTask != null)
                {
                    // التحقق من صلاحية تعديل المهمة
                    if (!_authService.CanEditData(_editingTask.NetworkId))
                    {
                        MessageBox.Show(
                            "ليس لديك صلاحية لتعديل هذه المهمة",
                            "غير مسموح",
                            MessageBoxButton.OK,
                            MessageBoxImage.Warning);
                        return;
                    }

                    // Update existing task
                    _editingTask.Description = Description;
                    _editingTask.Notes = Notes;
                    _editingTask.Status = TaskHelper.GetStatusKey(SelectedStatus);
                    _editingTask.Priority = TaskHelper.GetPriorityKey(SelectedPriority);
                    _editingTask.RequestDate = RequestDate;
                    _editingTask.CompletedAt = CompletedAt;
                    _editingTask.NetworkId = string.IsNullOrEmpty(SelectedNetworkId) ? null : SelectedNetworkId;

                    await _taskService.UpdateAsync(_editingTask);

                    // إشعار تحديث المهمة
                    var userName = _authService.CurrentUser?.Name ?? "المستخدم";
                    var userId = _authService.CurrentUser?.Id;
                    await _notificationService.ShowOperationAsync(
                        "تم تحديث المهمة",
                        $"قام {userName} بتحديث المهمة: {_editingTask.Description}",
                        NotificationType.Success,
                        userName, userId, "تحديث", _editingTask.Description, _editingTask.NetworkId);
                }
                else
                {
                    // التحقق من صلاحية إضافة مهمة جديدة
                    var targetNetworkId = GetNetworkIdForNewTask();
                    if (!_authService.CanAddData(targetNetworkId))
                    {
                        MessageBox.Show(
                            "ليس لديك صلاحية لإضافة مهام في هذه الشبكة",
                            "غير مسموح",
                            MessageBoxButton.OK,
                            MessageBoxImage.Warning);
                        return;
                    }

                    // إنشاء مهمة جديدة بنفس طريقة التعديل
                    var newTask = new Models.Task();

                    // تعيين القيم بنفس طريقة التعديل
                    newTask.Id = Guid.NewGuid().ToString();
                    newTask.Description = Description;
                    newTask.Notes = Notes;
                    newTask.Status = TaskHelper.GetStatusKey(SelectedStatus);
                    newTask.Priority = TaskHelper.GetPriorityKey(SelectedPriority);
                    newTask.RequestDate = RequestDate;
                    newTask.CompletedAt = CompletedAt;
                    newTask.NetworkId = string.IsNullOrEmpty(targetNetworkId) ? null : targetNetworkId;
                    newTask.UserId = currentUser?.Id ?? "admin";

                    await _taskService.CreateAsync(newTask);

                    // إشعار إضافة المهمة
                    var userName = _authService.CurrentUser?.Name ?? "المستخدم";
                    var userId = _authService.CurrentUser?.Id;
                    await _notificationService.ShowOperationAsync(
                        "تم إضافة المهمة",
                        $"قام {userName} بإضافة مهمة جديدة: {newTask.Description}",
                        NotificationType.Success,
                        userName, userId, "إضافة", newTask.Description, newTask.NetworkId);
                }

                DialogResult = true;
                RequestClose?.Invoke();
            }
            catch (Exception ex)
            {
                var errorMessage = $"خطأ في حفظ المهمة:\n{ex.Message}";
                if (ex.InnerException != null)
                {
                    errorMessage += $"\n\nتفاصيل إضافية:\n{ex.InnerException.Message}";
                }

                System.Diagnostics.Debug.WriteLine($"Task save error: {ex}");

                MessageBox.Show(
                    errorMessage,
                    "خطأ",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
            finally
            {
                IsSaving = false;
            }
        }

        [RelayCommand]
        private void Cancel()
        {
            DialogResult = false;
            RequestClose?.Invoke();
        }

        private async System.Threading.Tasks.Task<bool> ValidateInputAsync()
        {
            if (string.IsNullOrWhiteSpace(Description))
            {
                MessageBox.Show("وصف المهمة مطلوب", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            // التحقق من وجود الشبكة فقط إذا كانت محددة
            if (!string.IsNullOrEmpty(SelectedNetworkId))
            {
                try
                {
                    var network = await _networkService.GetByIdAsync(SelectedNetworkId);
                    if (network == null)
                    {
                        MessageBox.Show("الشبكة المحددة غير موجودة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                        return false;
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في التحقق من الشبكة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    return false;
                }
            }

            return true;
        }



        private static string GetStatusDisplay(string statusKey)
        {
            return statusKey switch
            {
                "pending" => "معلق",
                "in-progress" => "قيد التنفيذ",
                "completed" => "مكتمل",
                "cancelled" => "ملغي",
                _ => "معلق"
            };
        }

        private static string GetPriorityDisplay(string priorityKey)
        {
            return priorityKey switch
            {
                "low" => "منخفضة",
                "medium" => "متوسطة",
                "high" => "عالية",
                "urgent" => "عاجلة",
                _ => "متوسطة"
            };
        }

        private string? GetNetworkIdForNewTask()
        {
            if (!string.IsNullOrEmpty(SelectedNetworkId))
            {
                return SelectedNetworkId;
            }

            // إذا كان المستخدم Network Manager، استخدم شبكته تلقائ<|im_start|>
            var currentUser = _authService.CurrentUser;
            if (_authService.IsNetworkManager && !string.IsNullOrEmpty(currentUser?.NetworkId))
            {
                return currentUser.NetworkId;
            }

            return null;
        }

        public event Action? RequestClose;
    }
}
