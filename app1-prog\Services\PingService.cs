using System;
using System.Collections.Generic;
using System.Net.NetworkInformation;
using NetworkManagement.Models;

namespace NetworkManagement.Services
{
    public interface IPingService
    {
        System.Threading.Tasks.Task<bool> PingDeviceAsync(string ipAddress, int timeout = 3000);
        System.Threading.Tasks.Task<Dictionary<string, bool>> PingMultipleDevicesAsync(IEnumerable<Device> devices, int timeout = 3000);
        System.Threading.Tasks.Task<string> GetDeviceStatusAsync(string ipAddress, int timeout = 3000);
    }

    public class PingService : IPingService
    {
        private readonly ISettingsService _settingsService;

        public PingService(ISettingsService settingsService)
        {
            _settingsService = settingsService;
        }

        public async System.Threading.Tasks.Task<bool> PingDeviceAsync(string ipAddress, int timeout = 3000)
        {
            if (string.IsNullOrWhiteSpace(ipAddress))
                return false;

            try
            {
                // استخدام المهلة الزمنية من الإعدادات إذا لم يتم تمرير قيمة
                if (timeout == 3000)
                {
                    var settings = await _settingsService.GetSettingsAsync();
                    timeout = settings.PingTimeout * 1000; // تحويل من ثواني إلى ميلي ثانية
                }

                using var ping = new Ping();
                var reply = await ping.SendPingAsync(ipAddress, timeout);
                return reply.Status == IPStatus.Success;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Ping error for {ipAddress}: {ex.Message}");
                return false;
            }
        }

        public async System.Threading.Tasks.Task<Dictionary<string, bool>> PingMultipleDevicesAsync(IEnumerable<Device> devices, int timeout = 3000)
        {
            var results = new Dictionary<string, bool>();
            var tasks = new List<System.Threading.Tasks.Task>();

            foreach (var device in devices)
            {
                // فقط الأجهزة التي حالتها "يعمل" يتم عمل ping لها
                if (device.CanPing && !string.IsNullOrWhiteSpace(device.Ip))
                {
                    tasks.Add(System.Threading.Tasks.Task.Run(async () =>
                    {
                        var isOnline = await PingDeviceAsync(device.Ip, timeout);
                        lock (results)
                        {
                            results[device.Id] = isOnline;
                        }
                    }));
                }
                else
                {
                    // الأجهزة التي لا يمكن عمل ping لها تحافظ على حالتها الحالية
                    results[device.Id] = false;
                }
            }

            await System.Threading.Tasks.Task.WhenAll(tasks);
            return results;
        }

        public async System.Threading.Tasks.Task<string> GetDeviceStatusAsync(string ipAddress, int timeout = 3000)
        {
            if (string.IsNullOrWhiteSpace(ipAddress))
                return "غير محدد";

            var isOnline = await PingDeviceAsync(ipAddress, timeout);
            return isOnline ? "متصل" : "غير متصل";
        }
    }
}
