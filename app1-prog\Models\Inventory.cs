using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NetworkManagement.Models
{
    public class Inventory
    {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [MaxLength(50)]
        public string Category { get; set; } = string.Empty;

        [Required]
        public int Quantity { get; set; } = 0;

        [Required]
        [MaxLength(20)]
        public string Unit { get; set; } = string.Empty;

        public DateTime? LastUpdated { get; set; } = DateTime.Now;

        [MaxLength(500)]
        public string? Description { get; set; }

        public decimal? UnitPrice { get; set; }

        [MaxLength(100)]
        public string? Location { get; set; }

        public int? MinimumStock { get; set; }

        public int? MaximumStock { get; set; }

        [MaxLength(50)]
        public string? Supplier { get; set; }

        // Network relationship
        public string? NetworkId { get; set; }
        public virtual Network? Network { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        // Display properties
        [NotMapped]
        public string QuantityDisplay => $"{Quantity} {Unit ?? "قطعة"}";

        [NotMapped]
        public string TotalValueDisplay => UnitPrice.HasValue ?
            $"{(Quantity * UnitPrice.Value):N0} ر.ي" : "غير محدد";

        [NotMapped]
        public string UnitPriceDisplay => UnitPrice.HasValue ?
            $"{UnitPrice.Value:N0} ر.ي" : "غير محدد";

        [NotMapped]
        public string StockStatusDisplay => MinimumStock.HasValue && Quantity <= MinimumStock.Value ?
            "منخفض" : "جيد";

        [NotMapped]
        public string LastUpdatedDisplay => LastUpdated?.ToString("yyyy/MM/dd") ?? "غير محدد";

        [NotMapped]
        public string NetworkName => Network?.Name ?? "غير محدد";

        [NotMapped]
        public string CategoryDisplay => Category ?? "غير محدد";

        [NotMapped]
        public string LocationDisplay => Location ?? "غير محدد";

        [NotMapped]
        public string SupplierDisplay => Supplier ?? "غير محدد";

        [NotMapped]
        public string DescriptionDisplay => Description ?? "غير محدد";

        // Validation properties
        [NotMapped]
        public bool IsLowStock => MinimumStock.HasValue && Quantity <= MinimumStock.Value;

        [NotMapped]
        public bool IsValidStock => Quantity >= 0 &&
            (!MinimumStock.HasValue || !MaximumStock.HasValue || MinimumStock <= MaximumStock);

        [NotMapped]
        public string StockStatusColor => IsLowStock ? "#F44336" : "#4CAF50";
    }
}
