<UserControl x:Class="NetworkManagement.Views.UserEvaluationView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft">

    <UserControl.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto"
                  HorizontalScrollBarVisibility="Disabled">
        <Grid Margin="8">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

        <!-- Page Header -->
        <TextBlock Grid.Row="0" Text="إدارة تقييم المستخدمين" 
                   Style="{StaticResource PageHeaderStyle}" 
                   Margin="0,0,0,20"/>

        <!-- Controls Panel -->
        <materialDesign:Card Grid.Row="1" Style="{StaticResource CardStyle}" Margin="0,0,0,5">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Evaluation Form -->
                <WrapPanel Grid.Row="0" Orientation="Horizontal" Margin="8" HorizontalAlignment="Center">
                    <ComboBox ItemsSource="{Binding UsersToEvaluate}"
                              SelectedItem="{Binding SelectedUser}"
                              DisplayMemberPath="Name"
                              materialDesign:HintAssist.Hint="اختر المستخدم"
                              Width="220" Margin="4"
                              FontSize="13"
                              IsEnabled="{Binding CanEvaluateUsers}"/>

                    <DatePicker SelectedDate="{Binding SelectedDate}"
                                materialDesign:HintAssist.Hint="تاريخ التقييم"
                                Width="160" Margin="4"
                                FontSize="13"
                                IsEnabled="{Binding CanEvaluateUsers}"/>

                    <ComboBox SelectedValue="{Binding SelectedScore}"
                              materialDesign:HintAssist.Hint="النقاط"
                              Width="90" Margin="4"
                              FontSize="13"
                              IsEnabled="{Binding CanEvaluateUsers}">
                        <ComboBox.Items>
                            <sys:Int32 xmlns:sys="clr-namespace:System;assembly=mscorlib">1</sys:Int32>
                            <sys:Int32 xmlns:sys="clr-namespace:System;assembly=mscorlib">2</sys:Int32>
                            <sys:Int32 xmlns:sys="clr-namespace:System;assembly=mscorlib">3</sys:Int32>
                            <sys:Int32 xmlns:sys="clr-namespace:System;assembly=mscorlib">4</sys:Int32>
                            <sys:Int32 xmlns:sys="clr-namespace:System;assembly=mscorlib">5</sys:Int32>
                            <sys:Int32 xmlns:sys="clr-namespace:System;assembly=mscorlib">6</sys:Int32>
                            <sys:Int32 xmlns:sys="clr-namespace:System;assembly=mscorlib">7</sys:Int32>
                            <sys:Int32 xmlns:sys="clr-namespace:System;assembly=mscorlib">8</sys:Int32>
                            <sys:Int32 xmlns:sys="clr-namespace:System;assembly=mscorlib">9</sys:Int32>
                            <sys:Int32 xmlns:sys="clr-namespace:System;assembly=mscorlib">10</sys:Int32>
                        </ComboBox.Items>
                    </ComboBox>

                    <TextBox Text="{Binding EvaluationNotes}"
                             materialDesign:HintAssist.Hint="ملاحظات (اختياري)"
                             Width="220" Margin="4"
                             FontSize="13"
                             IsEnabled="{Binding CanEvaluateUsers}"/>

                    <Button Command="{Binding EvaluateUserCommand}"
                            Style="{StaticResource MaterialDesignRaisedButton}"
                            Margin="4" Height="36" MinWidth="80"
                            FontSize="13"
                            IsEnabled="{Binding CanEvaluateUsers}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Star" Width="18" Height="18" Margin="2"/>
                            <TextBlock Text="تقييم" FontSize="13"/>
                        </StackPanel>
                    </Button>

                    <Button Command="{Binding ClearEvaluationCommand}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Margin="4" Height="36" MinWidth="100"
                            FontSize="13"
                            IsEnabled="{Binding CanEvaluateUsers}"
                            Background="#FFEBEE"
                            BorderBrush="#F44336">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="DeleteForever" Width="18" Height="18" Margin="2" Foreground="#F44336"/>
                            <TextBlock Text="حذف التقييم" FontSize="13" Foreground="#F44336"/>
                        </StackPanel>
                    </Button>

                    <Button Command="{Binding ResetFormOnlyCommand}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Margin="4" Height="36" MinWidth="100"
                            FontSize="13"
                            IsEnabled="{Binding CanEvaluateUsers}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Refresh" Width="18" Height="18" Margin="2"/>
                            <TextBlock Text="تصفير النموذج" FontSize="13"/>
                        </StackPanel>
                    </Button>
                </WrapPanel>

                <!-- Filter and Action Controls -->
                <WrapPanel Grid.Row="1" Orientation="Horizontal" Margin="8" HorizontalAlignment="Center">
                    <DatePicker SelectedDate="{Binding StartDate}"
                                materialDesign:HintAssist.Hint="من تاريخ"
                                Width="130" Margin="3"
                                FontSize="13"/>

                    <DatePicker SelectedDate="{Binding EndDate}"
                                materialDesign:HintAssist.Hint="إلى تاريخ"
                                Width="130" Margin="3"
                                FontSize="13"/>

                    <TextBox Text="{Binding SearchText}"
                             materialDesign:HintAssist.Hint="بحث..."
                             Width="160" Margin="3"
                             FontSize="13"/>

                    <Button Command="{Binding LoadEvaluationsCommand}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Margin="3" Height="36" MinWidth="70"
                            FontSize="13">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Refresh" Width="16" Height="16" Margin="2"/>
                            <TextBlock Text="تحديث" FontSize="13"/>
                        </StackPanel>
                    </Button>

                    <Button Command="{Binding ToggleStatisticsCommand}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Margin="3" Height="36" MinWidth="120"
                            FontSize="13">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="ChartLine" Width="16" Height="16" Margin="2"/>
                            <TextBlock FontSize="13">
                                <TextBlock.Text>
                                    <MultiBinding StringFormat="{}{0}">
                                        <Binding Path="ShowStatistics"/>
                                    </MultiBinding>
                                </TextBlock.Text>
                                <TextBlock.Style>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="Text" Value="عرض الإحصائيات"/>
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding ShowStatistics}" Value="True">
                                                <Setter Property="Text" Value="إخفاء الإحصائيات"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </TextBlock.Style>
                            </TextBlock>
                        </StackPanel>
                    </Button>

                    <Button Command="{Binding ExportEvaluationsCommand}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Margin="3" Height="36" MinWidth="70"
                            FontSize="13">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Export" Width="16" Height="16" Margin="2"/>
                            <TextBlock Text="تصدير" FontSize="13"/>
                        </StackPanel>
                    </Button>
                </WrapPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Content Area -->
        <Grid Grid.Row="2" Margin="0,3,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" MinWidth="350"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="320" x:Name="StatisticsColumn" MinWidth="280"/>
            </Grid.ColumnDefinitions>

            <!-- Evaluations List -->
            <materialDesign:Card Grid.Column="0" Style="{StaticResource CardStyle}" Margin="0,0,3,0">
                <Grid>
                    <!-- Loading Indicator -->
                    <ProgressBar IsIndeterminate="True"
                                Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                                VerticalAlignment="Top" Height="4" Panel.ZIndex="1"/>

                    <!-- Data Grid -->
                    <DataGrid ItemsSource="{Binding Evaluations}"
                             SelectedItem="{Binding SelectedEvaluation}"
                             AutoGenerateColumns="False"
                             AlternatingRowBackground="Transparent"
                             GridLinesVisibility="Horizontal"
                             HeadersVisibility="Column"
                             CanUserAddRows="False"
                             CanUserDeleteRows="False"
                             CanUserSortColumns="True"
                             CanUserReorderColumns="True"
                             FontSize="12"
                             RowHeight="32"
                             CanUserResizeColumns="True"
                             IsReadOnly="True"
                             SelectionMode="Single"
                             SelectionUnit="FullRow"
                             EnableRowVirtualization="True"
                             EnableColumnVirtualization="True"
                             VirtualizingPanel.VirtualizationMode="Recycling"
                             VirtualizingPanel.IsVirtualizing="True"
                             ScrollViewer.CanContentScroll="True"
                             ColumnHeaderHeight="36">

                        <DataGrid.Columns>
                            <DataGridTextColumn Header="المستخدم" Binding="{Binding UserName}" Width="160" MinWidth="120"/>
                            <DataGridTextColumn Header="التاريخ" Binding="{Binding EvaluationDateDisplay}" Width="110" MinWidth="90"/>
                            <DataGridTextColumn Header="النقاط" Binding="{Binding ScoreDisplay}" Width="85" MinWidth="70"/>
                            <DataGridTextColumn Header="النسبة" Binding="{Binding ScorePercentageDisplay}" Width="85" MinWidth="70"/>
                            <DataGridTextColumn Header="التقدير" Binding="{Binding ScoreDescription}" Width="110" MinWidth="90"/>
                            <DataGridTextColumn Header="المقيم" Binding="{Binding EvaluatedByName}" Width="130" MinWidth="100"/>
                            <DataGridTextColumn Header="الملاحظات" Binding="{Binding NotesDisplay}" Width="*" MinWidth="150"/>
                        </DataGrid.Columns>

                        <DataGrid.ContextMenu>
                            <ContextMenu>
                                <MenuItem Header="تعديل التقييم" 
                                         Command="{Binding EditEvaluationCommand}"
                                         Icon="{materialDesign:PackIcon Kind=Edit}"/>
                                <MenuItem Header="حذف التقييم" 
                                         Command="{Binding DeleteEvaluationCommand}"
                                         Icon="{materialDesign:PackIcon Kind=Delete}"/>
                            </ContextMenu>
                        </DataGrid.ContextMenu>
                    </DataGrid>
                </Grid>
            </materialDesign:Card>

            <!-- Splitter -->
            <GridSplitter Grid.Column="1" Width="5" 
                         HorizontalAlignment="Center" 
                         VerticalAlignment="Stretch"
                         Background="Transparent"
                         Visibility="{Binding ShowStatistics, Converter={StaticResource BooleanToVisibilityConverter}}"/>

            <!-- Statistics Panel -->
            <materialDesign:Card Grid.Column="2" Style="{StaticResource CardStyle}"
                               Margin="3,0,0,0"
                               Visibility="{Binding ShowStatistics, Converter={StaticResource BooleanToVisibilityConverter}}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Header -->
                    <TextBlock Grid.Row="0" Text="إحصائيات التقييم"
                              Style="{StaticResource SubHeaderTextStyle}"
                              FontSize="14"
                              FontWeight="Bold"
                              HorizontalAlignment="Center"
                              Margin="8,8,8,5"/>

                    <!-- Content -->
                    <StackPanel Grid.Row="1" Margin="8,0,8,8">
                        <ItemsControl ItemsSource="{Binding UserStatistics}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <materialDesign:Card Style="{StaticResource CardStyle}"
                                                       Margin="0,0,0,5"
                                                       Padding="0">
                                        <StackPanel Margin="8">
                                            <TextBlock Text="{Binding UserName}"
                                                      FontWeight="SemiBold"
                                                      FontSize="13"
                                                      HorizontalAlignment="Center"
                                                      Margin="0,0,0,3"/>

                                            <TextBlock Text="{Binding AveragePercentageDisplay}"
                                                      FontSize="18"
                                                      FontWeight="Bold"
                                                      HorizontalAlignment="Center"
                                                      Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                      Margin="0,0,0,2"/>

                                            <TextBlock Text="{Binding AverageScoreDisplay}"
                                                      FontSize="12"
                                                      FontWeight="Medium"
                                                      HorizontalAlignment="Center"
                                                      Opacity="0.8"
                                                      Margin="0,0,0,2"/>

                                            <TextBlock Text="{Binding ScoreRangeDisplay, StringFormat='النطاق: {0}'}"
                                                      FontSize="11"
                                                      HorizontalAlignment="Center"
                                                      Opacity="0.7"
                                                      Margin="0,0,0,1"/>

                                            <TextBlock Text="{Binding TotalEvaluations, StringFormat='عدد التقييمات: {0}'}"
                                                      FontSize="11"
                                                      HorizontalAlignment="Center"
                                                      FontWeight="Medium"
                                                      Opacity="0.7"/>
                                        </StackPanel>
                                    </materialDesign:Card>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </StackPanel>
                </Grid>
            </materialDesign:Card>
        </Grid>
    </Grid>
    </ScrollViewer>
</UserControl>
