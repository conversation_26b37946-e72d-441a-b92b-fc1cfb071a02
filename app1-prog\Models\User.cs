using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace NetworkManagement.Models
{
    public class User
    {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        [Required]
        [MaxLength(50)]
        public string Username { get; set; } = string.Empty;

        [Required]
        [MaxLength(255)]
        public string Password { get; set; } = string.Empty;

        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [MaxLength(20)]
        public string Role { get; set; } = string.Empty; // admin, manager, technician

        // Network relationship
        public string? NetworkId { get; set; }
        public virtual Network? Network { get; set; }

        [MaxLength(100)]
        public string? Email { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual ICollection<Models.Task> Tasks { get; set; } = new List<Models.Task>();

        // Display properties
        public string NetworkName => Network?.Name ?? "جميع الشبكات";
        public string RoleDisplay => Role?.ToLower() switch
        {
            "admin" => "مدير عام",
            "manager" => "مدير شبكة",
            "user" => "مستخدم",
            "technician" => "فني",
            _ => Role ?? ""
        };
    }
}
