using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using NetworkManagement.ViewModels;

namespace NetworkManagement.Views
{
    public partial class LoginWindow : Window
    {
        public LoginWindow()
        {
            InitializeComponent();

            try
            {
                var viewModel = App.GetService<LoginViewModel>();
                DataContext = viewModel;
                viewModel.PasswordLoadRequested += OnPasswordLoadRequested;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"فشل في إنشاء LoginViewModel: {ex.Message}");
                throw;
            }
        }

        private void OnPasswordLoadRequested(object? sender, string password)
        {
            Dispatcher.Invoke(() =>
            {
                PasswordBox.Password = password;
            });
        }

        private void PasswordBox_PasswordChanged(object sender, RoutedEventArgs e)
        {
            if (DataContext is LoginViewModel viewModel)
            {
                viewModel.Password = ((PasswordBox)sender).Password;
            }
        }

        private void PasswordBox_GotFocus(object sender, RoutedEventArgs e)
        {
            // تحديد كلمة المرور تلقائياً عند الانتقال إليها
            if (sender is PasswordBox passwordBox && !string.IsNullOrEmpty(passwordBox.Password))
            {
                passwordBox.SelectAll();
            }
        }

        private void OnKeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                if (DataContext is LoginViewModel viewModel)
                {
                    // التحقق من أن الزر غير معطل (ليس في حالة تحميل)
                    if (viewModel.LoginCommand.CanExecute(null))
                    {
                        viewModel.LoginCommand.Execute(null);
                    }
                }
                e.Handled = true; // منع معالجة الحدث مرة أخرى
            }
        }
    }
}
