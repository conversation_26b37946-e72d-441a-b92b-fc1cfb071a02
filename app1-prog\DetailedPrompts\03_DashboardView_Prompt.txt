# برومت مفصل للوحة التحكم - DashboardView

## وصف الصفحة العام
لوحة التحكم هي الصفحة الرئيسية التي تعطي نظرة شاملة على حالة النظام بالكامل. تحتوي على إحصائيات في الوقت الحقيقي، رسوم بيانية تفاعلية، وملخصات سريعة لجميع جوانب النظام.

## المتطلبات التقنية
- **النوع**: UserControl
- **التخطيط**: Grid مع تقسيمات متجاوبة
- **التحديث**: كل 30 ثانية تلقائياً
- **الرسوم البيانية**: LiveCharts أو مشابه
- **الثيم**: دعم الثيم الفاتح والداكن
- **اللغة**: عربية كاملة مع تخطيط RTL

## التصميم المرئي

### الألوان:
- **خلفية البطاقات**: أبيض (#FFFFFF) للثيم الفاتح، رمادي داكن (#424242) للثيم الداكن
- **لون النجاح**: أخضر (#4CAF50)
- **لون التحذير**: برتقالي (#FF9800)
- **لون الخطر**: أحمر (#F44336)
- **لون المعلومات**: أزرق (#2196F3)
- **لون الحدود**: رمادي فاتح (#E0E0E0)

### الخطوط:
- **عناوين البطاقات**: Segoe UI، 16px، وزن Bold
- **الأرقام الكبيرة**: Segoe UI، 32px، وزن Bold
- **النص العادي**: Tahoma، 13px، وزن Normal
- **النص الصغير**: Tahoma، 11px، وزن Normal

## هيكل الصفحة

### 1. شريط الإحصائيات السريعة (Quick Stats Bar):
#### أ. بطاقة الأجهزة:
- **العنوان**: "الأجهزة"
- **الأيقونة**: Computer icon (أزرق)
- **الرقم الرئيسي**: إجمالي عدد الأجهزة (خط كبير)
- **التفاصيل**:
  - النشطة: عدد الأجهزة النشطة (أخضر)
  - غير النشطة: عدد الأجهزة غير النشطة (أحمر)
  - قيد الصيانة: عدد الأجهزة قيد الصيانة (برتقالي)
- **النسبة المئوية**: نسبة الأجهزة النشطة
- **مؤشر الاتجاه**: سهم صاعد/هابط مع النسبة

#### ب. بطاقة المواقع:
- **العنوان**: "المواقع"
- **الأيقونة**: LocationOn icon (أخضر)
- **الرقم الرئيسي**: إجمالي عدد المواقع
- **التفاصيل**:
  - مفعلة: عدد المواقع المفعلة
  - معطلة: عدد المواقع المعطلة
  - قيد الإنشاء: عدد المواقع قيد الإنشاء
- **آخر موقع مضاف**: اسم وتاريخ آخر موقع

#### ج. بطاقة المخزون:
- **العنوان**: "المخزون"
- **الأيقونة**: Inventory icon (برتقالي)
- **الرقم الرئيسي**: إجمالي عدد المواد
- **التفاصيل**:
  - متوفرة: عدد المواد المتوفرة
  - منخفضة: عدد المواد منخفضة المخزون (تحذير)
  - نافدة: عدد المواد النافدة (خطر)
- **القيمة الإجمالية**: قيمة المخزون بالريال اليمني

#### د. بطاقة المهام:
- **العنوان**: "المهام"
- **الأيقونة**: Task icon (بنفسجي)
- **الرقم الرئيسي**: إجمالي عدد المهام
- **التفاصيل**:
  - مكتملة: عدد المهام المكتملة
  - قيد التنفيذ: عدد المهام قيد التنفيذ
  - متأخرة: عدد المهام المتأخرة (أحمر)
- **نسبة الإنجاز**: نسبة المهام المكتملة

#### هـ. بطاقة التقييمات (للمديرين):
- **العنوان**: "تقييمات الموظفين"
- **الأيقونة**: Star icon (ذهبي)
- **الرقم الرئيسي**: متوسط التقييمات العام
- **التفاصيل**:
  - أعلى تقييم: أعلى درجة هذا الشهر
  - أقل تقييم: أقل درجة هذا الشهر
  - عدد التقييمات: إجمالي التقييمات
- **الاتجاه**: تحسن أم تراجع عن الشهر الماضي

### 2. الرسوم البيانية (Charts Section):
#### أ. رسم بياني لحالة الأجهزة (Device Status Chart):
- **النوع**: Pie Chart (دائري)
- **البيانات**: 
  - نشطة (أخضر)
  - غير نشطة (أحمر)
  - قيد الصيانة (برتقالي)
  - معطلة (رمادي)
- **التفاعل**: عرض التفاصيل عند التمرير
- **التحديث**: كل دقيقة

#### ب. رسم بياني للمهام الشهرية (Monthly Tasks Chart):
- **النوع**: Column Chart (أعمدة)
- **البيانات**: عدد المهام المكتملة لآخر 6 أشهر
- **الألوان**: تدرج أزرق
- **المحاور**: 
  - X: الأشهر (بالعربية)
  - Y: عدد المهام
- **التفاعل**: عرض التفاصيل عند النقر

#### ج. رسم بياني لحركة المخزون (Inventory Movement Chart):
- **النوع**: Line Chart (خطي)
- **البيانات**: حركة المخزون لآخر 30 يوم
- **الخطوط**:
  - الإضافات (أخضر)
  - الخصومات (أحمر)
  - الرصيد (أزرق)
- **التفاعل**: تكبير وتصغير الفترة الزمنية

#### د. رسم بياني لأداء الموظفين (Employee Performance Chart):
- **النوع**: Bar Chart (أشرطة أفقية)
- **البيانات**: متوسط تقييمات الموظفين
- **الترتيب**: من الأعلى للأقل
- **الألوان**: تدرج من الأخضر للأحمر
- **الخصوصية**: يظهر للمديرين فقط

### 3. قائمة الأنشطة الأخيرة (Recent Activities):
#### أ. المواصفات:
- **العرض**: 400 بكسل
- **الارتفاع**: 300 بكسل
- **التمرير**: عمودي
- **عدد العناصر**: آخر 20 نشاط

#### ب. أنواع الأنشطة:
1. **إضافة جهاز جديد**:
   - الأيقونة: Add icon (أخضر)
   - النص: "تم إضافة جهاز: [اسم الجهاز]"
   - المستخدم: اسم المستخدم الذي أضاف
   - الوقت: منذ كم من الوقت

2. **تحديث موقع**:
   - الأيقونة: Edit icon (أزرق)
   - النص: "تم تحديث موقع: [اسم الموقع]"
   - التغييرات: ملخص التغييرات
   - المستخدم والوقت

3. **حركة مخزون**:
   - الأيقونة: Inventory icon (برتقالي)
   - النص: "تم خصم/إضافة: [اسم المادة]"
   - الكمية: الكمية المتأثرة
   - السبب: سبب الحركة

4. **إكمال مهمة**:
   - الأيقونة: CheckCircle icon (أخضر)
   - النص: "تم إكمال مهمة: [عنوان المهمة]"
   - المسؤول: من أكمل المهمة
   - مدة الإنجاز

5. **تقييم موظف**:
   - الأيقونة: Star icon (ذهبي)
   - النص: "تم تقييم: [اسم الموظف]"
   - الدرجة: الدرجة المعطاة
   - المقيم: من قام بالتقييم

### 4. التنبيهات والإشعارات المهمة (Important Alerts):
#### أ. تنبيهات المخزون:
- **نفاد المخزون**: قائمة بالمواد النافدة
- **مخزون منخفض**: قائمة بالمواد منخفضة المخزون
- **مواد منتهية الصلاحية**: إذا كان هناك تتبع للصلاحية

#### ب. تنبيهات الأجهزة:
- **أجهزة معطلة**: قائمة بالأجهزة التي لا تستجيب للـ ping
- **أجهزة تحتاج صيانة**: حسب تواريخ الصيانة المجدولة
- **أجهزة جديدة**: أجهزة مضافة خلال آخر 24 ساعة

#### ج. تنبيهات المهام:
- **مهام متأخرة**: المهام التي تجاوزت موعد الإنجاز
- **مهام مستعجلة**: المهام عالية الأولوية
- **مهام قريبة الانتهاء**: المهام التي تنتهي خلال 3 أيام

### 5. الإحصائيات المتقدمة (Advanced Statistics):
#### أ. إحصائيات الشبكة:
- **عدد عناوين IP المستخدمة/المتاحة**
- **توزيع الأجهزة حسب النوع**
- **متوسط وقت الاستجابة للأجهزة**
- **نسبة الجهوزية (Uptime) للشبكة**

#### ب. إحصائيات مالية:
- **إجمالي قيمة المشتريات هذا الشهر**
- **متوسط تكلفة المشتريات الشهرية**
- **أكثر الموردين تعاملاً**
- **توزيع المصروفات حسب الفئة**

#### ج. إحصائيات الأداء:
- **متوسط وقت إنجاز المهام**
- **نسبة المهام المكتملة في الوقت المحدد**
- **أكثر المستخدمين نشاطاً**
- **توزيع الأنشطة حسب الوقت**

## الوظائف المطلوبة

### 1. تحديث البيانات:
```csharp
- تحديث تلقائي كل 30 ثانية
- تحديث فوري عند تغيير البيانات
- مؤشر آخر تحديث
- زر تحديث يدوي
```

### 2. التفاعل مع الرسوم البيانية:
```csharp
- عرض التفاصيل عند التمرير
- إمكانية التكبير والتصغير
- تصدير الرسوم البيانية كصور
- طباعة الرسوم البيانية
```

### 3. فلترة البيانات:
```csharp
- فلترة حسب الفترة الزمنية
- فلترة حسب نوع النشاط
- فلترة حسب المستخدم
- حفظ الفلاتر المفضلة
```

### 4. التنقل السريع:
```csharp
- النقر على البطاقات للانتقال للصفحة المقابلة
- النقر على الأنشطة للانتقال للعنصر المرتبط
- اختصارات لوحة المفاتيح للتنقل
```

### 5. التخصيص:
```csharp
- إعادة ترتيب البطاقات
- إخفاء/إظهار أقسام معينة
- تخصيص الألوان والثيمات
- حفظ التخصيصات للمستخدم
```

## التفاعلات والأحداث

### 1. أحداث التحديث:
- تحديث تلقائي للبيانات
- إشعار عند فشل التحديث
- مؤشر حالة التحديث
- إمكانية إيقاف التحديث التلقائي

### 2. أحداث التفاعل:
- النقر على البطاقات
- التمرير على الرسوم البيانية
- النقر على عناصر قائمة الأنشطة
- سحب وإفلات لإعادة الترتيب

### 3. أحداث التصدير:
- تصدير الإحصائيات كـ PDF
- تصدير البيانات كـ Excel
- طباعة لوحة التحكم
- مشاركة الإحصائيات

## الأمان والصلاحيات

### 1. فلترة البيانات:
```csharp
- عرض بيانات الشبكة الخاصة بالمستخدم فقط
- إخفاء البيانات الحساسة حسب الدور
- فلترة الأنشطة حسب الصلاحيات
```

### 2. التحكم في الوصول:
```csharp
- إخفاء الإحصائيات المالية عن غير المخولين
- تقييد الوصول لبيانات المستخدمين الآخرين
- منع تصدير البيانات الحساسة
```

## الأداء والتحسين

### 1. تحسين التحميل:
- تحميل كسول للرسوم البيانية
- تخزين مؤقت للإحصائيات
- ضغط البيانات المنقولة
- تحميل تدريجي للأنشطة

### 2. تحسين الذاكرة:
- تنظيف البيانات القديمة
- إعادة استخدام الكائنات
- تحسين استعلامات قاعدة البيانات
- مراقبة استهلاك الذاكرة

هذا البرومت المفصل يغطي جميع جوانب لوحة التحكم لضمان إنشاء واجهة شاملة وتفاعلية.
