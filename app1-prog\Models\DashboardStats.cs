using System;

namespace NetworkManagement.Models
{
    /// <summary>
    /// إحصائيات لوحة التحكم للتخزين المؤقت
    /// </summary>
    public class DashboardStats
    {
        public int TotalDevices { get; set; }
        public int ActiveDevices { get; set; }
        public int TotalSites { get; set; }
        public int PendingTasks { get; set; }
        public decimal MonthlySpending { get; set; }
        public int LowStockItems { get; set; }
        public DateTime CachedAt { get; set; } = DateTime.Now;
    }
}
