<UserControl x:Class="NetworkManagement.Views.DevicesView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
             Background="{DynamicResource MaterialDesignPaper}">

    <Grid Margin="5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header and Actions - تصميم مضغوط -->
        <materialDesign:Card Grid.Row="0" Margin="2" Padding="10">
            <StackPanel>
                <!-- Title and Search Row -->
                <Grid Margin="5">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- Title Section -->
                    <StackPanel Grid.Column="0" VerticalAlignment="Center" Margin="5">
                        <TextBlock Text="إدارة الأجهزة"
                                  FontSize="16" FontWeight="Medium" Margin="2"/>
                        <TextBlock Text="إضافة وتعديل وحذف أجهزة الشبكة • فحص حالة الاتصال"
                                  FontSize="11" Opacity="0.68"/>
                    </StackPanel>

                    <!-- Search Box -->
                    <TextBox Grid.Column="1"
                            materialDesign:HintAssist.Hint="البحث في الأجهزة..."
                            materialDesign:HintAssist.IsFloating="True"
                            Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                            VerticalAlignment="Center" Margin="10,0,0,0"/>
                </Grid>

                <!-- Action Buttons - تصميم مضغوط -->
                <WrapPanel HorizontalAlignment="Right" Margin="5">
                    <!-- الأزرار الأساسية -->
                    <Button Command="{Binding AddDeviceCommand}"
                           Style="{StaticResource MaterialDesignRaisedButton}"
                           Margin="3" Height="32"
                           Visibility="{Binding CanAddDevices, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Plus" Width="14" Height="14" Margin="2"/>
                            <TextBlock Text="إضافة جهاز" FontSize="12"/>
                        </StackPanel>
                    </Button>

                    <!-- أزرار التحديد -->
                    <Button Command="{Binding SelectAllDevicesCommand}"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Margin="3" Height="32">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="CheckboxMarkedCircle" Width="14" Height="14" Margin="2"/>
                            <TextBlock Text="تحديد الكل" FontSize="12"/>
                        </StackPanel>
                    </Button>

                    <!-- أزرار الفحص -->
                    <Button Command="{Binding PingAllDevicesCommand}"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Margin="3" Height="32"
                           IsEnabled="{Binding IsPinging, Converter={StaticResource InverseBooleanConverter}}"
                           ToolTip="فحص جميع الأجهزة التي تحتوي على عنوان IP">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Wifi" Width="14" Height="14" Margin="2"/>
                            <TextBlock Text="فحص الكل" FontSize="12"/>
                        </StackPanel>
                    </Button>

                    <Button Command="{Binding PingSelectedDevicesCommand}"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Margin="3" Height="32"
                           IsEnabled="{Binding IsPinging, Converter={StaticResource InverseBooleanConverter}}"
                           ToolTip="فحص الأجهزة المحددة فقط">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="WifiCheck" Width="14" Height="14" Margin="2"/>
                            <TextBlock Text="فحص المحدد" FontSize="12"/>
                        </StackPanel>
                    </Button>

                    <!-- أزرار التصدير والاستيراد -->
                    <Button Command="{Binding ExportDevicesCommand}"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Margin="3" Height="32">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Export" Width="14" Height="14" Margin="2"/>
                            <TextBlock Text="تصدير الكل" FontSize="12"/>
                        </StackPanel>
                    </Button>

                    <Button Command="{Binding ExportSelectedDevicesCommand}"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Margin="3" Height="32">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="ExportVariant" Width="14" Height="14" Margin="2"/>
                            <TextBlock Text="تصدير المحدد" FontSize="12"/>
                        </StackPanel>
                    </Button>

                    <Button Command="{Binding ImportDevicesCommand}"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Margin="3" Height="32"
                           Visibility="{Binding CanAddDevices, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Import" Width="14" Height="14" Margin="2"/>
                            <TextBlock Text="استيراد" FontSize="12"/>
                        </StackPanel>
                    </Button>

                    <!-- زر الحذف -->
                    <Button Command="{Binding DeleteSelectedDevicesCommand}"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Margin="3" Height="32"
                           Foreground="{StaticResource StatusDisconnectedBrush}"
                           Visibility="{Binding CanDeleteDevices, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Delete" Width="14" Height="14" Margin="2"/>
                            <TextBlock Text="حذف المحدد" FontSize="12"/>
                        </StackPanel>
                    </Button>

                    <!-- Selected Count Display -->
                    <Border Background="{DynamicResource PrimaryHueMidBrush}"
                           CornerRadius="10" Padding="8,4" Margin="3"
                           Visibility="{Binding SelectedDevicesCount, Converter={StaticResource ZeroToVisibilityConverter}, ConverterParameter=Inverse}">
                        <TextBlock Text="{Binding SelectedDevicesCount, StringFormat='{}{0} محدد'}"
                                  FontSize="11" Foreground="White" FontWeight="Medium"/>
                    </Border>
                </WrapPanel>
            </StackPanel>
        </materialDesign:Card>

        <!-- Filters - تصميم مضغوط -->
        <materialDesign:Card Grid.Row="1" Margin="2" Padding="8">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="فلترة:" FontWeight="Bold" FontSize="12"
                          VerticalAlignment="Center" Margin="5"/>

                <ComboBox materialDesign:HintAssist.Hint="الحالة"
                         SelectedValue="{Binding StatusFilter}"
                         SelectedValuePath="Tag"
                         Width="100" Margin="3" Height="28">
                    <ComboBoxItem Content="الكل" Tag=""/>
                    <ComboBoxItem Content="متصل" Tag="متصل"/>
                    <ComboBoxItem Content="غير متصل" Tag="غير متصل"/>
                    <ComboBoxItem Content="نشط" Tag="active"/>
                    <ComboBoxItem Content="غير نشط" Tag="inactive"/>
                    <ComboBoxItem Content="صيانة" Tag="maintenance"/>
                    <ComboBoxItem Content="معطل" Tag="disabled"/>
                </ComboBox>

                <ComboBox materialDesign:HintAssist.Hint="النوع"
                         ItemsSource="{Binding AvailableDeviceTypes}"
                         SelectedItem="{Binding TypeFilter}"
                         Width="100" Margin="3" Height="28"/>

                <ComboBox materialDesign:HintAssist.Hint="نطاق IP"
                         ItemsSource="{Binding AvailableIpRanges}"
                         SelectedItem="{Binding SelectedIpRange}"
                         Width="120" Margin="3" Height="28">
                    <i:Interaction.Triggers>
                        <i:EventTrigger EventName="SelectionChanged">
                            <i:InvokeCommandAction Command="{Binding FilterByIpRangeCommand}"/>
                        </i:EventTrigger>
                    </i:Interaction.Triggers>
                </ComboBox>

                <Button Command="{Binding ClearFiltersCommand}"
                       Style="{StaticResource MaterialDesignOutlinedButton}"
                       Content="مسح الفلاتر" FontSize="11"
                       Margin="3" Height="28"/>

                    <Button Command="{Binding OpenColumnSettingsCommand}"
                           Style="{StaticResource MaterialDesignIconButton}"
                           ToolTip="إعدادات الأعمدة" Width="28" Height="28" Margin="3">
                        <materialDesign:PackIcon Kind="ViewColumn" Width="14" Height="14"/>
                    </Button>
            </StackPanel>
        </materialDesign:Card>

        <!-- Devices List - استغلال أقصى مساحة -->
        <Grid Grid.Row="2" Margin="2">
            <!-- Loading Indicator -->
            <ProgressBar IsIndeterminate="True"
                        Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                        VerticalAlignment="Top" Height="3"/>

            <!-- Ping Status -->
            <Border Background="{DynamicResource PrimaryHueMidBrush}"
                   Visibility="{Binding IsPinging, Converter={StaticResource BooleanToVisibilityConverter}}"
                   VerticalAlignment="Top" Height="25" Margin="5">
                <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="10,0">
                    <ProgressBar Width="16" Height="16"
                               Style="{StaticResource MaterialDesignCircularProgressBar}"
                               IsIndeterminate="True"
                               Foreground="White"/>
                    <TextBlock Text="{Binding PingStatus}"
                             Foreground="White" FontSize="12"
                             VerticalAlignment="Center"
                             Margin="8,0,0,0"/>
                </StackPanel>
            </Border>

            <!-- Data Grid - استغلال أقصى مساحة -->
            <DataGrid x:Name="DevicesDataGrid"
                     ItemsSource="{Binding Devices}"
                     SelectedItem="{Binding SelectedDevice}"
                     AutoGenerateColumns="False"
                     LoadingRow="DevicesDataGrid_LoadingRow"
                     AlternatingRowBackground="Transparent"
                     GridLinesVisibility="Horizontal"
                     HeadersVisibility="Column"
                     CanUserAddRows="False"
                     CanUserDeleteRows="False"
                     CanUserSortColumns="True"
                     CanUserReorderColumns="True"
                     CanUserResizeColumns="True"
                     IsReadOnly="True"
                     SelectionMode="Extended"
                     SelectionUnit="FullRow"
                     EnableRowVirtualization="True"
                     EnableColumnVirtualization="True"
                     VirtualizingPanel.VirtualizationMode="Recycling"
                     VirtualizingPanel.IsVirtualizing="True"
                     ScrollViewer.CanContentScroll="True"
                     Margin="0"
                     RowHeight="50"
                     ColumnHeaderHeight="45">

                <!-- إزالة Focus وSelection Borders -->
                <DataGrid.CellStyle>
                    <Style TargetType="DataGridCell">
                        <Setter Property="BorderThickness" Value="0"/>
                        <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="DataGridCell">
                                    <Border Background="{TemplateBinding Background}"
                                           BorderThickness="0"
                                           Padding="8,0">
                                        <ContentPresenter VerticalAlignment="Center"/>
                                    </Border>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Style>
                </DataGrid.CellStyle>

                <DataGrid.RowStyle>
                    <Style TargetType="DataGridRow">
                        <Setter Property="Background" Value="Transparent"/>
                        <Setter Property="BorderThickness" Value="0"/>
                        <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
                        <Setter Property="Margin" Value="0,2"/>
                    </Style>
                </DataGrid.RowStyle>

                <DataGrid.Columns>
                    <!-- Row Number Column -->
                    <DataGridTemplateColumn Header="#" Width="40" CanUserSort="False" CanUserReorder="False">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding RelativeSource={RelativeSource AncestorType=DataGridRow}, Path=Header}"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center"
                                           FontWeight="Bold" FontSize="14"
                                           Foreground="{DynamicResource MaterialDesignBody}"/>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>

                    <!-- Selection Column -->
                    <DataGridTemplateColumn Header="تحديد" Width="50">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <CheckBox IsChecked="{Binding IsSelected, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}"
                                         HorizontalAlignment="Center"
                                         VerticalAlignment="Center"/>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>

                    <!-- 1. عنوان IP -->
                    <DataGridTemplateColumn Header="عنوان IP" Width="140" SortMemberPath="Ip">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Button Content="{Binding Ip, FallbackValue='غير محدد'}"
                                       Command="{Binding Path=DataContext.OpenIpInBrowserCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                       CommandParameter="{Binding Ip}"
                                       Style="{StaticResource MaterialDesignFlatButton}"
                                       Foreground="{StaticResource StatisticsBlueBrush}"
                                       HorizontalAlignment="Left" FontSize="14"
                                       Padding="0" Height="30"
                                       ToolTip="انقر لفتح في المتصفح">
                                    <Button.IsEnabled>
                                        <Binding Path="Ip" Converter="{StaticResource StringToVisibilityConverter}" ConverterParameter="NotEmpty"/>
                                    </Button.IsEnabled>
                                </Button>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>

                    <!-- 2. الحالة -->
                    <DataGridTemplateColumn Header="الحالة" Width="100" SortMemberPath="Status">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <Ellipse Style="{StaticResource StatusIndicatorStyle}" Width="8" Height="8"/>
                                    <TextBlock Text="{Binding StatusDisplay}" Style="{StaticResource StatusTextStyle}" FontSize="14"/>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>

                    <!-- 3. النوع -->
                    <DataGridTextColumn Header="النوع" Binding="{Binding Type, FallbackValue='غير محدد'}" Width="110">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="FontSize" Value="14"/>
                                <Setter Property="VerticalAlignment" Value="Center"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- 4. المسؤول -->
                    <DataGridTextColumn Header="المسؤول" Binding="{Binding Responsible, FallbackValue='غير محدد'}" Width="130">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="FontSize" Value="14"/>
                                <Setter Property="VerticalAlignment" Value="Center"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- 5. رقم الهاتف -->
                    <DataGridTextColumn Header="رقم الهاتف" Binding="{Binding Phone, FallbackValue='غير محدد'}" Width="120">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="FontSize" Value="14"/>
                                <Setter Property="VerticalAlignment" Value="Center"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- 6. الشبكة -->
                    <DataGridTemplateColumn Header="الشبكة" Width="110" SortMemberPath="Network.Name">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding Network.Name, FallbackValue='غير محدد'}"
                                           ToolTip="{Binding Network.Name, FallbackValue='لم يتم تحديد شبكة لهذا الجهاز'}"
                                           VerticalAlignment="Center" FontSize="14"/>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>

                    <!-- 7. الموقع -->
                    <DataGridTextColumn Header="الموقع" Binding="{Binding Location, FallbackValue='غير محدد'}" Width="140">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="FontSize" Value="14"/>
                                <Setter Property="VerticalAlignment" Value="Center"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- 8. آخر فحص -->
                    <DataGridTemplateColumn Header="آخر فحص" Width="150" SortMemberPath="LastCheck">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding LastCheck, StringFormat=dd/MM/yyyy HH:mm, FallbackValue='لم يتم الفحص'}"
                                           VerticalAlignment="Center" FontSize="14"
                                           ToolTip="{Binding LastCheck, StringFormat=dd/MM/yyyy HH:mm, FallbackValue='لم يتم فحص هذا الجهاز بعد'}"/>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>

                    <!-- Actions Column -->
                    <DataGridTemplateColumn Header="الإجراءات" Width="120">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                    <Button Command="{Binding Path=DataContext.EditDeviceCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                           CommandParameter="{Binding}"
                                           Style="{StaticResource MaterialDesignIconButton}"
                                           ToolTip="تعديل الجهاز" Width="32" Height="32"
                                           Visibility="{Binding Converter={StaticResource CanEditDataConverter}}"
                                           Margin="2"
                                           Opacity="1"
                                           IsEnabled="True">
                                        <materialDesign:PackIcon Kind="Edit" Width="16" Height="16"
                                                               Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                                    </Button>
                                    <Button Command="{Binding Path=DataContext.DeleteDeviceCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                           CommandParameter="{Binding}"
                                           Style="{StaticResource MaterialDesignIconButton}"
                                           ToolTip="حذف الجهاز" Width="32" Height="32"
                                           Visibility="{Binding Converter={StaticResource CanDeleteDataConverter}}"
                                           Margin="2"
                                           Opacity="1"
                                           IsEnabled="True">
                                        <materialDesign:PackIcon Kind="Delete" Width="16" Height="16" Foreground="Red"/>
                                    </Button>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>
        </Grid>
    </Grid>
</UserControl>