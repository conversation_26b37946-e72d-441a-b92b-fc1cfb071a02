using System;
using System.Windows.Controls;
using NetworkManagement.ViewModels;

namespace NetworkManagement.Views
{
    public partial class UsersView : UserControl
    {
        public UsersView()
        {
            InitializeComponent();
            Loaded += UsersView_Loaded;
        }

        private async void UsersView_Loaded(object sender, System.Windows.RoutedEventArgs e)
        {
            try
            {
                if (DataContext is UsersViewModel viewModel)
                {
                    await viewModel.InitializeAsync();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"UsersView_Loaded: خطأ - {ex.Message}");
                System.Windows.MessageBox.Show(
                    $"خطأ في تحميل صفحة المستخدمين:\n{ex.Message}",
                    "خطأ",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Error);
            }
        }
    }
}
