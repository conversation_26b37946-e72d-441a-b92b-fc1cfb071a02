using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using NetworkManagement.Models;
using NetworkManagement.Services;

namespace NetworkManagement.Helpers
{
    /// <summary>
    /// فئة مساعدة لإدارة الشبكات وإزالة التكرار في الأكواد
    /// </summary>
    public static class NetworkHelper
    {
        /// <summary>
        /// تحميل الشبكات مع تطبيق فلترة الصلاحيات
        /// </summary>
        /// <param name="authService">خدمة المصادقة</param>
        /// <param name="networkService">خدمة الشبكات</param>
        /// <param name="cancellationToken">رمز الإلغاء</param>
        /// <returns>قائمة الشبكات المفلترة حسب الصلاحيات</returns>
        public static async Task<IEnumerable<Network>> LoadFilteredNetworksAsync(
            IAuthService authService,
            INetworkService networkService,
            CancellationToken cancellationToken = default)
        {
            var allNetworks = await networkService.GetAllAsync().ConfigureAwait(false);
            
            // التحقق من الإلغاء
            cancellationToken.ThrowIfCancellationRequested();

            // تطبيق فلترة الصلاحيات
            if (authService.CanViewAllNetworks)
            {
                return allNetworks;
            }

            // إذا كان المستخدم مدير شبكة، عرض شبكته فقط
            if (!string.IsNullOrEmpty(authService.CurrentUserNetworkId))
            {
                return allNetworks.Where(n => n.Id == authService.CurrentUserNetworkId);
            }

            // إذا لم يكن له شبكة محددة، لا يعرض أي شبكات
            return Enumerable.Empty<Network>();
        }

        /// <summary>
        /// تحميل الشبكات للاستخدام في القوائم المنسدلة مع تعيين الشبكة الافتراضية
        /// </summary>
        /// <param name="authService">خدمة المصادقة</param>
        /// <param name="networkService">خدمة الشبكات</param>
        /// <param name="cancellationToken">رمز الإلغاء</param>
        /// <returns>قائمة الشبكات والشبكة الافتراضية المحددة</returns>
        public static async Task<(List<Network> Networks, string? DefaultNetworkId)> LoadNetworksForSelectionAsync(
            IAuthService authService,
            INetworkService networkService,
            CancellationToken cancellationToken = default)
        {
            var networks = await LoadFilteredNetworksAsync(authService, networkService, cancellationToken);
            var networkList = networks.ToList();

            // تحديد الشبكة الافتراضية
            string? defaultNetworkId = null;
            var currentUser = authService.CurrentUser;

            if (currentUser != null && !authService.IsSuperAdmin && !string.IsNullOrEmpty(currentUser.NetworkId))
            {
                // للمدراء والفنيين: تعيين شبكتهم كافتراضي
                defaultNetworkId = currentUser.NetworkId;
            }

            return (networkList, defaultNetworkId);
        }

        /// <summary>
        /// التحقق من صلاحية إدارة شبكة معينة
        /// </summary>
        /// <param name="authService">خدمة المصادقة</param>
        /// <param name="networkId">معرف الشبكة</param>
        /// <returns>true إذا كان يمكن إدارة الشبكة</returns>
        public static bool CanManageNetwork(IAuthService authService, string? networkId)
        {
            if (authService.IsSuperAdmin) return true;
            
            if (authService.IsNetworkManager)
            {
                return networkId == authService.CurrentUserNetworkId;
            }

            return false;
        }

        /// <summary>
        /// التحقق من صلاحية عرض شبكة معينة
        /// </summary>
        /// <param name="authService">خدمة المصادقة</param>
        /// <param name="networkId">معرف الشبكة</param>
        /// <returns>true إذا كان يمكن عرض الشبكة</returns>
        public static bool CanViewNetwork(IAuthService authService, string? networkId)
        {
            if (authService.CanViewAllNetworks) return true;
            
            return networkId == authService.CurrentUserNetworkId;
        }

        /// <summary>
        /// الحصول على قائمة الشبكات المتاحة للمستخدم الحالي
        /// </summary>
        /// <param name="authService">خدمة المصادقة</param>
        /// <param name="allNetworks">جميع الشبكات</param>
        /// <returns>الشبكات المتاحة للمستخدم</returns>
        public static IEnumerable<Network> FilterNetworksByPermissions(
            IAuthService authService,
            IEnumerable<Network> allNetworks)
        {
            if (authService.CanViewAllNetworks)
            {
                return allNetworks;
            }

            if (!string.IsNullOrEmpty(authService.CurrentUserNetworkId))
            {
                return allNetworks.Where(n => n.Id == authService.CurrentUserNetworkId);
            }

            return Enumerable.Empty<Network>();
        }

        /// <summary>
        /// عرض رسالة عدم وجود صلاحية لإدارة الشبكات
        /// </summary>
        /// <param name="action">نوع العملية</param>
        public static void ShowNetworkPermissionDeniedMessage(string action)
        {
            System.Windows.MessageBox.Show(
                $"ليس لديك صلاحية {action} الشبكات",
                "غير مسموح",
                System.Windows.MessageBoxButton.OK,
                System.Windows.MessageBoxImage.Warning);
        }

        /// <summary>
        /// التحقق من وجود بيانات مرتبطة بالشبكة
        /// </summary>
        /// <param name="network">الشبكة المراد فحصها</param>
        /// <returns>true إذا كانت هناك بيانات مرتبطة</returns>
        public static bool HasRelatedData(Network network)
        {
            return network.Users?.Any() == true ||
                   network.Devices?.Any() == true ||
                   network.Sites?.Any() == true;
        }

        /// <summary>
        /// إنشاء شبكة جديدة بالقيم الافتراضية
        /// </summary>
        /// <param name="name">اسم الشبكة (اختياري)</param>
        /// <param name="description">وصف الشبكة (اختياري)</param>
        /// <returns>شبكة جديدة</returns>
        public static Network CreateNewNetwork(string? name = null, string? description = null)
        {
            return new Network
            {
                Id = Guid.NewGuid().ToString(),
                Name = name ?? "شبكة جديدة",
                Description = description ?? "وصف الشبكة الجديدة",
                Color = "#2196F3",
                IsActive = true,
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now
            };
        }
    }
}
