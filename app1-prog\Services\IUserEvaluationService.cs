using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using NetworkManagement.Models;

namespace NetworkManagement.Services
{
    public interface IUserEvaluationService
    {
        /// <summary>
        /// الحصول على جميع التقييمات مع فلترة اختيارية
        /// </summary>
        Task<IEnumerable<UserEvaluation>> GetAllAsync(string? networkFilter = null);

        /// <summary>
        /// الحصول على تقييم بالمعرف
        /// </summary>
        Task<UserEvaluation?> GetByIdAsync(string id);

        /// <summary>
        /// إنشاء تقييم جديد
        /// </summary>
        Task<UserEvaluation> CreateAsync(UserEvaluation evaluation);

        /// <summary>
        /// تحديث تقييم موجود
        /// </summary>
        Task<UserEvaluation> UpdateAsync(UserEvaluation evaluation);

        /// <summary>
        /// حذف تقييم
        /// </summary>
        Task<bool> DeleteAsync(string id);

        /// <summary>
        /// الحصول على تقييمات مستخدم محدد
        /// </summary>
        Task<IEnumerable<UserEvaluation>> GetByUserIdAsync(string userId, DateTime? startDate = null, DateTime? endDate = null);

        /// <summary>
        /// الحصول على تقييم مستخدم في تاريخ محدد
        /// </summary>
        Task<UserEvaluation?> GetByUserAndDateAsync(string userId, DateTime date);

        /// <summary>
        /// الحصول على تقييمات فترة محددة
        /// </summary>
        Task<IEnumerable<UserEvaluation>> GetByDateRangeAsync(DateTime startDate, DateTime endDate, string? networkFilter = null);

        /// <summary>
        /// الحصول على إحصائيات تقييم مستخدم
        /// </summary>
        Task<UserEvaluationStatistics?> GetUserStatisticsAsync(string userId, DateTime? startDate = null, DateTime? endDate = null);

        /// <summary>
        /// الحصول على إحصائيات جميع المستخدمين حسب صلاحيات المستخدم الحالي
        /// </summary>
        Task<IEnumerable<UserEvaluationStatistics>> GetAllUsersStatisticsAsync(string? networkFilter = null, DateTime? startDate = null, DateTime? endDate = null, string? currentUserRole = null, string? currentUserId = null);

        /// <summary>
        /// الحصول على المستخدمين الذين لم يتم تقييمهم اليوم
        /// </summary>
        Task<IEnumerable<User>> GetUsersNotEvaluatedTodayAsync(string? networkFilter = null);

        /// <summary>
        /// الحصول على جميع المستخدمين القابلين للتقييم حسب صلاحيات المستخدم الحالي
        /// </summary>
        Task<IEnumerable<User>> GetEvaluableUsersAsync(string? networkFilter = null, string? currentUserRole = null);

        /// <summary>
        /// فحص إذا كان المستخدم تم تقييمه في تاريخ محدد
        /// </summary>
        Task<bool> IsUserEvaluatedOnDateAsync(string userId, DateTime date);

        /// <summary>
        /// الحصول على متوسط النقاط لمستخدم في فترة محددة
        /// </summary>
        Task<decimal> GetAverageScoreAsync(string userId, DateTime? startDate = null, DateTime? endDate = null);

        /// <summary>
        /// الحصول على عدد التقييمات لمستخدم
        /// </summary>
        Task<int> GetEvaluationCountAsync(string userId, DateTime? startDate = null, DateTime? endDate = null);

        /// <summary>
        /// الحصول على أعلى وأقل نقاط لمستخدم
        /// </summary>
        Task<(int highest, int lowest)> GetScoreRangeAsync(string userId, DateTime? startDate = null, DateTime? endDate = null);

        /// <summary>
        /// البحث في التقييمات
        /// </summary>
        Task<IEnumerable<UserEvaluation>> SearchAsync(string searchTerm, string? networkFilter = null, DateTime? startDate = null, DateTime? endDate = null);

        /// <summary>
        /// الحصول على تقييمات مع فلترة شاملة
        /// </summary>
        Task<IEnumerable<UserEvaluation>> GetFilteredAsync(
            string? networkFilter = null,
            string? userId = null,
            string? evaluatedBy = null,
            DateTime? startDate = null,
            DateTime? endDate = null,
            int? minScore = null,
            int? maxScore = null,
            string? searchText = null);

        /// <summary>
        /// الحصول على عدد التقييمات مع فلترة
        /// </summary>
        Task<int> GetCountAsync(
            string? networkFilter = null,
            string? userId = null,
            DateTime? startDate = null,
            DateTime? endDate = null);
    }
}
