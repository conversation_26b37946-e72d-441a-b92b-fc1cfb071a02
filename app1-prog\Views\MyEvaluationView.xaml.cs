using System;
using System.Windows.Controls;
using NetworkManagement.ViewModels;
using NetworkManagement.Views;

namespace NetworkManagement.Views
{
    public partial class MyEvaluationView : UserControl
    {
        public MyEvaluationView()
        {
            InitializeComponent();
            Loaded += MyEvaluationView_Loaded;
        }

        private async void MyEvaluationView_Loaded(object sender, System.Windows.RoutedEventArgs e)
        {
            try
            {
                if (DataContext is MyEvaluationViewModel viewModel)
                {
                    await viewModel.InitializeAsync();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"MyEvaluationView_Loaded: خطأ - {ex.Message}");
                System.Windows.MessageBox.Show(
                    $"خطأ في تحميل صفحة تقييماتي:\n{ex.Message}",
                    "خطأ",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Error);
            }
        }
    }
}
