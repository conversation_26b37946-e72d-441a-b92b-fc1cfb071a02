using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using NetworkManagement.Data;
using Microsoft.EntityFrameworkCore;
using System.Linq;

namespace NetworkManagement.Services
{
    /// <summary>
    /// خدمة المزامنة التلقائية بين الأجهزة المتعددة
    /// </summary>
    public class SyncService : ISyncService, IDisposable
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IMemoryCache _cache;
        private Timer? _syncTimer;
        private readonly SemaphoreSlim _syncSemaphore = new(1, 1);
        private bool _disposed = false;

        // إعدادات المزامنة
        private readonly TimeSpan _syncInterval = TimeSpan.FromSeconds(15); // كل 15 ثانية
        private readonly string _lastSyncCacheKey = "LastSyncTimes";

        public DateTime LastSyncTime { get; private set; } = DateTime.Now;
        public bool IsSyncing { get; private set; } = false;

        public event EventHandler<SyncEventArgs>? DataUpdated;
        public event EventHandler<bool>? SyncStatusChanged;

        public SyncService(IServiceProvider serviceProvider, IMemoryCache cache)
        {
            _serviceProvider = serviceProvider;
            _cache = cache;
        }

        public async Task StartSyncAsync()
        {
            if (_syncTimer != null) return;

            // تحميل آخر أوقات المزامنة من الذاكرة المؤقتة
            LoadLastSyncTimes();

            // بدء المؤقت للمزامنة الدورية
            _syncTimer = new Timer(async _ => await PerformSyncAsync(), null, TimeSpan.Zero, _syncInterval);

            await Task.CompletedTask;
        }

        public async Task StopSyncAsync()
        {
            _syncTimer?.Dispose();
            _syncTimer = null;
            await Task.CompletedTask;
        }

        public async Task ForceSyncAsync()
        {
            await PerformSyncAsync();
        }

        public async Task<bool> HasUpdatesAsync()
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                using var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                var lastSyncTimes = GetLastSyncTimes();
                
                // فحص كل جدول للتحديثات
                var tables = new[] { "Devices", "Sites", "Users", "Tasks", "Purchases", "Inventory", "Networks" };
                
                foreach (var table in tables)
                {
                    var lastSync = lastSyncTimes.GetValueOrDefault(table, DateTime.MinValue);
                    var hasUpdates = await CheckTableForUpdatesAsync(context, table, lastSync);
                    if (hasUpdates) return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في فحص التحديثات: {ex.Message}");
                return false;
            }
        }

        private async Task PerformSyncAsync()
        {
            if (!await _syncSemaphore.WaitAsync(1000)) return; // تجنب التداخل

            try
            {
                IsSyncing = true;
                SyncStatusChanged?.Invoke(this, true);

                using var scope = _serviceProvider.CreateScope();
                using var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                var lastSyncTimes = GetLastSyncTimes();
                var updatedTables = new List<string>();

                // فحص كل جدول للتحديثات
                var tables = new[] { "Devices", "Sites", "Users", "Tasks", "Purchases", "Inventory", "Networks" };
                
                foreach (var table in tables)
                {
                    var lastSync = lastSyncTimes.GetValueOrDefault(table, DateTime.MinValue);
                    var updatedRows = await GetUpdatedRowsCountAsync(context, table, lastSync);
                    
                    if (updatedRows > 0)
                    {
                        updatedTables.Add(table);
                        lastSyncTimes[table] = DateTime.Now;
                        
                        // إرسال إشعار بالتحديث
                        DataUpdated?.Invoke(this, new SyncEventArgs
                        {
                            TableName = table,
                            Action = "UPDATE",
                            AffectedRows = updatedRows,
                            Timestamp = DateTime.Now
                        });
                    }
                }

                // حفظ أوقات المزامنة الجديدة
                if (updatedTables.Any())
                {
                    SaveLastSyncTimes(lastSyncTimes);
                }

                LastSyncTime = DateTime.Now;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في المزامنة: {ex.Message}");
            }
            finally
            {
                IsSyncing = false;
                SyncStatusChanged?.Invoke(this, false);
                _syncSemaphore.Release();
            }
        }

        private async Task<bool> CheckTableForUpdatesAsync(NetworkDbContext context, string tableName, DateTime lastSync)
        {
            try
            {
                // استعلامات محسنة مع استخدام الفهارس
                var sql = tableName switch
                {
                    "Devices" => "SELECT COUNT(*) FROM Devices WHERE UpdatedAt > @lastSync LIMIT 1",
                    "Sites" => "SELECT COUNT(*) FROM Sites WHERE UpdatedAt > @lastSync LIMIT 1",
                    "Users" => "SELECT COUNT(*) FROM Users WHERE UpdatedAt > @lastSync LIMIT 1",
                    "Tasks" => "SELECT COUNT(*) FROM Tasks WHERE (RequestDate > @lastSync OR CompletedAt > @lastSync) LIMIT 1",
                    "Purchases" => "SELECT COUNT(*) FROM Purchases WHERE UpdatedAt > @lastSync LIMIT 1",
                    "Inventory" => "SELECT COUNT(*) FROM Inventory WHERE UpdatedAt > @lastSync LIMIT 1",
                    "Networks" => "SELECT COUNT(*) FROM Networks WHERE UpdatedAt > @lastSync LIMIT 1",
                    _ => null
                };

                if (sql == null) return false;

                // استخدام طريقة أبسط للفحص
                var count = tableName switch
                {
                    "Devices" => await context.Devices!.Where(d => d.UpdatedAt > lastSync).Take(1).CountAsync(),
                    "Sites" => await context.Sites!.Where(s => s.UpdatedAt > lastSync).Take(1).CountAsync(),
                    "Users" => await context.Users!.Where(u => u.UpdatedAt > lastSync).Take(1).CountAsync(),
                    "Tasks" => await context.Tasks!.Where(t => t.RequestDate > lastSync || (t.CompletedAt.HasValue && t.CompletedAt > lastSync)).Take(1).CountAsync(),
                    "Purchases" => await context.Purchases!.Where(p => p.UpdatedAt > lastSync).Take(1).CountAsync(),
                    "Inventory" => await context.Inventory!.Where(i => i.UpdatedAt > lastSync).Take(1).CountAsync(),
                    "Networks" => await context.Networks!.Where(n => n.UpdatedAt > lastSync).Take(1).CountAsync(),
                    _ => 0
                };

                return count > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في فحص تحديثات الجدول {tableName}: {ex.Message}");
                return false;
            }
        }

        private async Task<int> GetUpdatedRowsCountAsync(NetworkDbContext context, string tableName, DateTime lastSync)
        {
            try
            {
                return await CheckTableForUpdatesAsync(context, tableName, lastSync) ? 1 : 0;
            }
            catch
            {
                return 0;
            }
        }

        private Dictionary<string, DateTime> GetLastSyncTimes()
        {
            return _cache.GetOrCreate(_lastSyncCacheKey, _ => new Dictionary<string, DateTime>()) ?? new Dictionary<string, DateTime>();
        }

        private void SaveLastSyncTimes(Dictionary<string, DateTime> syncTimes)
        {
            _cache.Set(_lastSyncCacheKey, syncTimes, TimeSpan.FromHours(24));
        }

        private void LoadLastSyncTimes()
        {
            var syncTimes = GetLastSyncTimes();
            if (syncTimes.Any())
            {
                LastSyncTime = syncTimes.Values.Max();
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _syncTimer?.Dispose();
                _syncSemaphore?.Dispose();
                _disposed = true;
                GC.SuppressFinalize(this);
            }
        }
    }
}
