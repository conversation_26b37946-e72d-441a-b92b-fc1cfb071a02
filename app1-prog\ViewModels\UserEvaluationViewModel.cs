using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using NetworkManagement.Helpers;
using NetworkManagement.Models;
using NetworkManagement.Services;

namespace NetworkManagement.ViewModels
{
    public partial class UserEvaluationViewModel : ObservableObject
    {
        private readonly IUserEvaluationService _evaluationService;
        private readonly IUserService _userService;
        private readonly IAuthService _authService;
        private readonly INotificationService _notificationService;
        private readonly IReportExportService _exportService;

        [ObservableProperty]
        private ObservableCollection<UserEvaluation> evaluations = new();

        [ObservableProperty]
        private ObservableCollection<User> usersToEvaluate = new();

        [ObservableProperty]
        private ObservableCollection<UserEvaluationStatistics> userStatistics = new();

        [ObservableProperty]
        private UserEvaluation? selectedEvaluation;

        [ObservableProperty]
        private User? selectedUser;

        [ObservableProperty]
        private int selectedScore = 5;

        [ObservableProperty]
        private string evaluationNotes = string.Empty;

        [ObservableProperty]
        private DateTime selectedDate = DateTime.Today;

        [ObservableProperty]
        private DateTime startDate = DateTime.Today.AddDays(-30);

        [ObservableProperty]
        private DateTime endDate = DateTime.Today;

        [ObservableProperty]
        private bool isLoading = false;

        [ObservableProperty]
        private string searchText = string.Empty;

        [ObservableProperty]
        private bool showStatistics = false;

        [ObservableProperty]
        private bool canEvaluateUsers = false;

        public UserEvaluationViewModel(
            IUserEvaluationService evaluationService,
            IUserService userService,
            IAuthService authService,
            INotificationService notificationService,
            IReportExportService exportService)
        {
            _evaluationService = evaluationService;
            _userService = userService;
            _authService = authService;
            _notificationService = notificationService;
            _exportService = exportService;

            // تحديد الصلاحيات
            CanEvaluateUsers = _authService.IsSuperAdmin || _authService.IsNetworkManager;
        }

        public async System.Threading.Tasks.Task InitializeAsync()
        {
            try
            {
                IsLoading = true;

                await LoadEvaluationsAsync();
                await LoadUsersToEvaluateAsync();
                await LoadUserStatisticsAsync();
            }
            catch (Exception ex)
            {
                await ErrorHandler.HandleExceptionAsync(ex, "تهيئة صفحة التقييمات", "UserEvaluationViewModel");
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task LoadEvaluationsAsync()
        {
            try
            {
                IsLoading = true;

                var networkFilter = PermissionHelper.GetNetworkFilter(_authService);
                var allEvaluations = await _evaluationService.GetByDateRangeAsync(StartDate, EndDate, networkFilter);
                var filteredEvaluations = PermissionHelper.ApplyPermissionFilter(allEvaluations, _authService, e => e.NetworkId);

                // تطبيق البحث
                if (!string.IsNullOrWhiteSpace(SearchText))
                {
                    var searchLower = SearchText.ToLower();
                    filteredEvaluations = filteredEvaluations.Where(e =>
                        e.UserName.ToLower().Contains(searchLower) ||
                        e.EvaluatedByName.ToLower().Contains(searchLower) ||
                        e.NotesDisplay.ToLower().Contains(searchLower));
                }

                Evaluations.Clear();
                foreach (var evaluation in filteredEvaluations.OrderByDescending(e => e.EvaluationDate))
                {
                    Evaluations.Add(evaluation);
                }
            }
            catch (Exception ex)
            {
                await ErrorHandler.HandleExceptionAsync(ex, "تحميل التقييمات", "UserEvaluationViewModel");
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task LoadUsersToEvaluateAsync()
        {
            try
            {
                var networkFilter = PermissionHelper.GetNetworkFilter(_authService);
                var currentUserRole = _authService.CurrentUser?.Role;
                var users = await _evaluationService.GetEvaluableUsersAsync(networkFilter, currentUserRole);
                var filteredUsers = PermissionHelper.ApplyPermissionFilter(users, _authService, u => u.NetworkId);

                UsersToEvaluate.Clear();
                foreach (var user in filteredUsers.OrderBy(u => u.Name))
                {
                    UsersToEvaluate.Add(user);
                }
            }
            catch (Exception ex)
            {
                await ErrorHandler.HandleExceptionAsync(ex, "تحميل المستخدمين للتقييم", "UserEvaluationViewModel");
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task LoadUserStatisticsAsync()
        {
            try
            {
                var networkFilter = PermissionHelper.GetNetworkFilter(_authService);
                var currentUserRole = _authService.CurrentUser?.Role;
                var currentUserId = _authService.CurrentUser?.Id;
                var statistics = await _evaluationService.GetAllUsersStatisticsAsync(networkFilter, StartDate, EndDate, currentUserRole, currentUserId);
                var filteredStatistics = PermissionHelper.ApplyPermissionFilter(statistics, _authService, s => s.NetworkId);

                UserStatistics.Clear();
                foreach (var stat in filteredStatistics.OrderByDescending(s => s.AverageScore))
                {
                    UserStatistics.Add(stat);
                }
            }
            catch (Exception ex)
            {
                await ErrorHandler.HandleExceptionAsync(ex, "تحميل إحصائيات التقييم", "UserEvaluationViewModel");
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task EvaluateUserAsync()
        {
            if (!CanEvaluateUsers)
            {
                PermissionHelper.ShowPermissionDeniedMessage("تقييم", "المستخدمين");
                return;
            }

            if (SelectedUser == null)
            {
                MessageBox.Show("يرجى اختيار مستخدم للتقييم", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            // فحص صلاحية تقييم هذا المستخدم
            if (!_authService.CanEditData(SelectedUser.NetworkId))
            {
                PermissionHelper.ShowPermissionDeniedMessage("تقييم", "هذا المستخدم");
                return;
            }

            try
            {
                var evaluation = new UserEvaluation
                {
                    UserId = SelectedUser.Id,
                    EvaluatedBy = _authService.CurrentUser?.Id ?? string.Empty,
                    EvaluationDate = SelectedDate,
                    Score = SelectedScore,
                    Notes = EvaluationNotes?.Trim(),
                    NetworkId = SelectedUser.NetworkId
                };

                await _evaluationService.CreateAsync(evaluation);

                await _notificationService.ShowSuccessAsync("تقييم المستخدم", 
                    $"تم تقييم {SelectedUser.Name} بنجاح بدرجة {SelectedScore}/10");

                // إعادة تحميل البيانات
                await LoadEvaluationsAsync();
                await LoadUsersToEvaluateAsync();
                await LoadUserStatisticsAsync();

                // إعادة تعيين النموذج
                ResetEvaluationForm();
            }
            catch (Exception ex)
            {
                await ErrorHandler.HandleExceptionAsync(ex, "تقييم المستخدم", "UserEvaluationViewModel");
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task EditEvaluationAsync()
        {
            if (!CanEvaluateUsers || SelectedEvaluation == null)
            {
                PermissionHelper.ShowPermissionDeniedMessage("تعديل", "التقييمات");
                return;
            }

            if (!_authService.CanEditData(SelectedEvaluation.NetworkId))
            {
                PermissionHelper.ShowPermissionDeniedMessage("تعديل", "هذا التقييم");
                return;
            }

            try
            {
                // فتح نافذة التعديل (يمكن إنشاؤها لاحقاً)
                var result = MessageBox.Show(
                    $"هل تريد تعديل تقييم {SelectedEvaluation.UserName}؟\nالدرجة الحالية: {SelectedEvaluation.Score}/10",
                    "تعديل التقييم",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // هنا يمكن فتح نافذة تعديل مخصصة
                    // للآن سنستخدم نفس النموذج
                    SelectedUser = await _userService.GetByIdAsync(SelectedEvaluation.UserId);
                    SelectedScore = SelectedEvaluation.Score;
                    EvaluationNotes = SelectedEvaluation.Notes ?? string.Empty;
                    SelectedDate = SelectedEvaluation.EvaluationDate;
                }
            }
            catch (Exception ex)
            {
                await ErrorHandler.HandleExceptionAsync(ex, "تعديل التقييم", "UserEvaluationViewModel");
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task DeleteEvaluationAsync()
        {
            if (!CanEvaluateUsers || SelectedEvaluation == null)
            {
                PermissionHelper.ShowPermissionDeniedMessage("حذف", "التقييمات");
                return;
            }

            if (!_authService.CanDeleteData(SelectedEvaluation.NetworkId))
            {
                PermissionHelper.ShowPermissionDeniedMessage("حذف", "هذا التقييم");
                return;
            }

            var result = MessageBox.Show(
                $"هل أنت متأكد من حذف تقييم {SelectedEvaluation.UserName}؟\nالدرجة: {SelectedEvaluation.Score}/10\nالتاريخ: {SelectedEvaluation.EvaluationDateDisplay}",
                "تأكيد الحذف",
                MessageBoxButton.YesNo,
                MessageBoxImage.Warning);

            if (result != MessageBoxResult.Yes)
                return;

            try
            {
                await _evaluationService.DeleteAsync(SelectedEvaluation.Id);

                await _notificationService.ShowSuccessAsync("حذف التقييم", 
                    $"تم حذف تقييم {SelectedEvaluation.UserName} بنجاح");

                await LoadEvaluationsAsync();
                await LoadUsersToEvaluateAsync();
                await LoadUserStatisticsAsync();

                SelectedEvaluation = null;
            }
            catch (Exception ex)
            {
                await ErrorHandler.HandleExceptionAsync(ex, "حذف التقييم", "UserEvaluationViewModel");
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task ExportEvaluationsAsync()
        {
            try
            {
                var evaluationsToExport = Evaluations.ToList();
                if (!evaluationsToExport.Any())
                {
                    MessageBox.Show("لا توجد تقييمات للتصدير", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                var filePath = await _exportService.GetSaveFilePathAsync(
                    $"تقييمات_المستخدمين_{DateTime.Now:yyyyMMdd}.xlsx",
                    "Excel Files (*.xlsx)|*.xlsx|CSV Files (*.csv)|*.csv");

                if (string.IsNullOrEmpty(filePath))
                    return;

                if (filePath.EndsWith(".xlsx"))
                {
                    await _exportService.ExportUserEvaluationsReportToExcelAsync(evaluationsToExport, filePath);
                }
                else
                {
                    await _exportService.ExportUserEvaluationsReportToCsvAsync(evaluationsToExport, filePath);
                }
                
                await _notificationService.ShowSuccessAsync("تصدير التقييمات", "تم تصدير التقييمات بنجاح");
            }
            catch (Exception ex)
            {
                await ErrorHandler.HandleExceptionAsync(ex, "تصدير التقييمات", "UserEvaluationViewModel");
            }
        }

        [RelayCommand]
        private void ToggleStatistics()
        {
            ShowStatistics = !ShowStatistics;
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task RefreshDataAsync()
        {
            await InitializeAsync();
        }

        [RelayCommand]
        private void ResetFormOnly()
        {
            ResetEvaluationForm();
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task ClearEvaluationAsync()
        {
            if (!CanEvaluateUsers)
            {
                PermissionHelper.ShowPermissionDeniedMessage("حذف", "التقييمات");
                return;
            }

            if (SelectedUser == null)
            {
                MessageBox.Show("يرجى اختيار مستخدم أولاً", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            // فحص صلاحية حذف تقييم هذا المستخدم
            if (!_authService.CanDeleteData(SelectedUser.NetworkId))
            {
                PermissionHelper.ShowPermissionDeniedMessage("حذف تقييم", "هذا المستخدم");
                return;
            }

            try
            {
                // البحث عن التقييم الموجود للمستخدم في التاريخ المحدد
                var existingEvaluation = await _evaluationService.GetByUserAndDateAsync(SelectedUser.Id, SelectedDate);

                if (existingEvaluation == null)
                {
                    MessageBox.Show($"لا يوجد تقييم للمستخدم {SelectedUser.Name} في تاريخ {SelectedDate:dd/MM/yyyy}",
                        "لا يوجد تقييم", MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                // تأكيد الحذف
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف تقييم {SelectedUser.Name}؟\n" +
                    $"التاريخ: {existingEvaluation.EvaluationDateDisplay}\n" +
                    $"النقاط: {existingEvaluation.ScoreDisplay}\n" +
                    $"الملاحظات: {existingEvaluation.NotesDisplay}",
                    "تأكيد حذف التقييم",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result != MessageBoxResult.Yes)
                    return;

                // حذف التقييم
                await _evaluationService.DeleteAsync(existingEvaluation.Id);

                await _notificationService.ShowSuccessAsync("حذف التقييم",
                    $"تم حذف تقييم {SelectedUser.Name} بنجاح");

                // إعادة تحميل البيانات
                await LoadEvaluationsAsync();
                await LoadUsersToEvaluateAsync();
                await LoadUserStatisticsAsync();

                // تصفير النموذج
                ResetEvaluationForm();
            }
            catch (Exception ex)
            {
                await ErrorHandler.HandleExceptionAsync(ex, "حذف التقييم", "UserEvaluationViewModel");
            }
        }

        private void ResetEvaluationForm()
        {
            SelectedUser = null;
            SelectedScore = 5;
            EvaluationNotes = string.Empty;
            SelectedDate = DateTime.Today;
        }

        partial void OnStartDateChanged(DateTime value)
        {
            _ = System.Threading.Tasks.Task.Run(async () => await LoadEvaluationsAsync());
            _ = System.Threading.Tasks.Task.Run(async () => await LoadUserStatisticsAsync());
        }

        partial void OnEndDateChanged(DateTime value)
        {
            _ = System.Threading.Tasks.Task.Run(async () => await LoadEvaluationsAsync());
            _ = System.Threading.Tasks.Task.Run(async () => await LoadUserStatisticsAsync());
        }

        partial void OnSearchTextChanged(string value)
        {
            _ = System.Threading.Tasks.Task.Run(async () => await LoadEvaluationsAsync());
        }
    }
}
