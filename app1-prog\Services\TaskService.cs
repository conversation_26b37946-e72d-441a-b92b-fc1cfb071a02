using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using NetworkManagement.Data;
using NetworkManagement.Models;
using Task = System.Threading.Tasks.Task;

namespace NetworkManagement.Services
{
    public class TaskService : ITaskService
    {
        private readonly IServiceProvider _serviceProvider;

        public TaskService(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        public async Task<IEnumerable<Models.Task>> GetAllAsync(string? userId = null)
        {
            using var scope = _serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

            if (context.Tasks == null)
                throw new InvalidOperationException("DbSet<Task> is null");

            // تضمين الشبكة كما في الأجهزة
            var query = context.Tasks.Include(t => t.User).Include(t => t.Network).AsQueryable();

            if (!string.IsNullOrEmpty(userId))
            {
                query = query.Where(t => t.UserId == userId);
            }

            return await query.OrderByDescending(t => t.RequestDate).ToListAsync();
        }

        /// <summary>
        /// الحصول على المهام مع فلترة محسنة على مستوى قاعدة البيانات
        /// </summary>
        public async Task<IEnumerable<Models.Task>> GetFilteredAsync(
            IEnumerable<string>? networkIds = null,
            string? status = null,
            string? priority = null,
            string? searchText = null,
            string? userId = null,
            int pageSize = 0,
            int pageNumber = 1)
        {
            using var scope = _serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

            if (context.Tasks == null)
                throw new InvalidOperationException("DbSet<Task> is null");

            var query = context.Tasks
                .Include(t => t.User)
                .Include(t => t.Network)
                .AsQueryable();

            // فلترة حسب الشبكات المسموحة
            if (networkIds != null && networkIds.Any())
            {
                var networkIdsList = networkIds.ToList();
                query = query.Where(t => t.NetworkId == null || networkIdsList.Contains(t.NetworkId));
            }

            // فلترة حسب الحالة
            if (!string.IsNullOrEmpty(status))
            {
                query = query.Where(t => t.Status == status);
            }

            // فلترة حسب الأولوية
            if (!string.IsNullOrEmpty(priority))
            {
                query = query.Where(t => t.Priority == priority);
            }

            // فلترة حسب المستخدم
            if (!string.IsNullOrEmpty(userId))
            {
                query = query.Where(t => t.UserId == userId);
            }

            // فلترة حسب النص
            if (!string.IsNullOrWhiteSpace(searchText))
            {
                var search = searchText.Trim().ToLower();
                query = query.Where(t =>
                    t.Description.ToLower().Contains(search) ||
                    (t.Notes != null && t.Notes.ToLower().Contains(search)) ||
                    (t.User != null && t.User.Name.ToLower().Contains(search)));
            }

            // ترتيب النتائج
            query = query.OrderByDescending(t => t.RequestDate);

            // تطبيق التصفح (Pagination)
            if (pageSize > 0)
            {
                query = query.Skip((pageNumber - 1) * pageSize).Take(pageSize);
            }

            return await query.ToListAsync();
        }

        /// <summary>
        /// الحصول على عدد المهام مع فلترة
        /// </summary>
        public async Task<int> GetCountAsync(
            IEnumerable<string>? networkIds = null,
            string? status = null,
            string? priority = null,
            string? searchText = null,
            string? userId = null)
        {
            using var scope = _serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

            if (context.Tasks == null)
                return 0;

            var query = context.Tasks.AsQueryable();

            // تطبيق نفس الفلاتر
            if (networkIds != null && networkIds.Any())
            {
                var networkIdsList = networkIds.ToList();
                query = query.Where(t => t.NetworkId == null || networkIdsList.Contains(t.NetworkId));
            }

            if (!string.IsNullOrEmpty(status))
            {
                query = query.Where(t => t.Status == status);
            }

            if (!string.IsNullOrEmpty(priority))
            {
                query = query.Where(t => t.Priority == priority);
            }

            if (!string.IsNullOrEmpty(userId))
            {
                query = query.Where(t => t.UserId == userId);
            }

            if (!string.IsNullOrWhiteSpace(searchText))
            {
                var search = searchText.Trim().ToLower();
                query = query.Where(t =>
                    t.Description.ToLower().Contains(search) ||
                    (t.Notes != null && t.Notes.ToLower().Contains(search)) ||
                    (t.User != null && t.User.Name.ToLower().Contains(search)));
            }

            return await query.CountAsync();
        }

        /// <summary>
        /// الحصول على إحصائيات المهام
        /// </summary>
        public async Task<TaskStatistics> GetStatisticsAsync(IEnumerable<string>? networkIds = null)
        {
            using var scope = _serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

            if (context.Tasks == null)
                return new TaskStatistics();

            var query = context.Tasks.AsQueryable();

            if (networkIds != null && networkIds.Any())
            {
                var networkIdsList = networkIds.ToList();
                query = query.Where(t => t.NetworkId == null || networkIdsList.Contains(t.NetworkId));
            }

            var stats = await query
                .GroupBy(t => 1) // Group all records
                .Select(g => new TaskStatistics
                {
                    TotalTasks = g.Count(),
                    PendingTasks = g.Count(t => t.Status == "pending"),
                    InProgressTasks = g.Count(t => t.Status == "in-progress"),
                    CompletedTasks = g.Count(t => t.Status == "completed"),
                    CancelledTasks = g.Count(t => t.Status == "cancelled"),
                    OverdueTasks = g.Count(t => t.RequestDate < DateTime.Now.AddDays(-7) && t.Status != "completed"), // المهام المتأخرة أكثر من أسبوع
                    LowPriorityTasks = g.Count(t => t.Priority == "low"),
                    MediumPriorityTasks = g.Count(t => t.Priority == "medium"),
                    HighPriorityTasks = g.Count(t => t.Priority == "high"),
                    UrgentPriorityTasks = g.Count(t => t.Priority == "urgent")
                })
                .FirstOrDefaultAsync();

            return stats ?? new TaskStatistics();
        }

        public async Task<Models.Task?> GetByIdAsync(string id)
        {
            using var scope = _serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

            if (context.Tasks == null)
                return null;

            return await context.Tasks
                .Include(t => t.User)
                .Include(t => t.Network)
                .FirstOrDefaultAsync(t => t.Id == id);
        }

        public async Task<Models.Task> CreateAsync(Models.Task task)
        {
            using var scope = _serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

            if (context.Tasks == null)
                throw new InvalidOperationException("DbSet<Task> is null");

            // بساطة - نفس طريقة UpdateAsync
            context.Tasks.Add(task);
            await context.SaveChangesAsync();
            return task;
        }

        public async Task<Models.Task> UpdateAsync(Models.Task task)
        {
            using var scope = _serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

            if (context.Tasks == null)
                throw new InvalidOperationException("DbSet<Task> is null");

            // البحث عن المهمة الموجودة في الـ context الحالي
            var existingTask = await context.Tasks.FindAsync(task.Id);
            if (existingTask == null)
                throw new InvalidOperationException($"Task with ID {task.Id} not found");

            // تحديث الخصائص فقط بدلاً من استبدال الكيان كاملاً
            existingTask.Description = task.Description;
            existingTask.Notes = task.Notes;
            existingTask.Status = task.Status;
            existingTask.Priority = task.Priority;
            existingTask.RequestDate = task.RequestDate;
            existingTask.CompletedAt = task.CompletedAt;
            existingTask.NetworkId = task.NetworkId;

            await context.SaveChangesAsync();
            return existingTask;
        }

        public async Task<bool> DeleteAsync(string id)
        {
            using var scope = _serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

            if (context.Tasks == null)
                throw new InvalidOperationException("DbSet<Task> is null");

            var task = await context.Tasks.FindAsync(id);
            if (task == null) return false;

            context.Tasks.Remove(task);
            await context.SaveChangesAsync();
            return true;
        }

        public async Task<IEnumerable<Models.Task>> GetByUserIdAsync(string userId)
        {
            using var scope = _serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

            if (context.Tasks == null)
                throw new InvalidOperationException("DbSet<Task> is null");

            return await context.Tasks
                .Include(t => t.User)
                .Include(t => t.Network)
                .Where(t => t.UserId == userId)
                .OrderByDescending(t => t.RequestDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Models.Task>> GetByStatusAsync(string status)
        {
            using var scope = _serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

            if (context.Tasks == null)
                throw new InvalidOperationException("DbSet<Task> is null");

            return await context.Tasks
                .Include(t => t.User)
                .Include(t => t.Network)
                .Where(t => t.Status == status)
                .OrderByDescending(t => t.RequestDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Models.Task>> GetByNetworkIdAsync(string networkId)
        {
            using var scope = _serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

            if (context.Tasks == null)
                throw new InvalidOperationException("DbSet<Task> is null");

            return await context.Tasks
                .Include(t => t.User)
                .Include(t => t.Network)
                .Where(t => t.NetworkId == networkId)
                .OrderByDescending(t => t.RequestDate)
                .ToListAsync();
        }
    }
}

