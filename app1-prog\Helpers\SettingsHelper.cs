using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using NetworkManagement.Data;
using NetworkManagement.Models;
using NetworkManagement.Services;
using Microsoft.EntityFrameworkCore;

namespace NetworkManagement.Helpers
{
    /// <summary>
    /// فئة مساعدة لإدارة الإعدادات وإزالة التكرار في الأكواد
    /// </summary>
    public static class SettingsHelper
    {
        /// <summary>
        /// تحميل الإحصائيات مع تطبيق فلترة الصلاحيات
        /// </summary>
        /// <param name="serviceProvider">مزود الخدمات</param>
        /// <param name="authService">خدمة المصادقة</param>
        /// <param name="cancellationToken">رمز الإلغاء</param>
        /// <returns>إحصائيات النظام المفلترة</returns>
        public static async Task<SystemStatistics> LoadFilteredStatisticsAsync(
            IServiceProvider serviceProvider,
            IAuthService authService,
            CancellationToken cancellationToken = default)
        {
            using var scope = serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

            var statistics = new SystemStatistics();

            try
            {
                // الحصول على فلتر الشبكة
                var networkFilter = PermissionHelper.GetNetworkFilter(authService);

                // تحميل الإحصائيات حسب الصلاحيات
                if (context.Devices != null)
                {
                    var devicesQuery = context.Devices.AsQueryable();
                    if (!string.IsNullOrEmpty(networkFilter))
                    {
                        devicesQuery = devicesQuery.Where(d => d.NetworkId == networkFilter);
                    }
                    statistics.TotalDevices = await devicesQuery.CountAsync(cancellationToken);
                }

                if (context.Sites != null)
                {
                    var sitesQuery = context.Sites.AsQueryable();
                    if (!string.IsNullOrEmpty(networkFilter))
                    {
                        sitesQuery = sitesQuery.Where(s => s.NetworkId == networkFilter);
                    }
                    statistics.TotalSites = await sitesQuery.CountAsync(cancellationToken);
                }

                if (context.Users != null)
                {
                    var usersQuery = context.Users.AsQueryable();
                    if (!string.IsNullOrEmpty(networkFilter))
                    {
                        usersQuery = usersQuery.Where(u => u.NetworkId == networkFilter);
                    }
                    statistics.TotalUsers = await usersQuery.CountAsync(cancellationToken);
                }

                if (context.Purchases != null)
                {
                    var purchasesQuery = context.Purchases.AsQueryable();
                    if (!string.IsNullOrEmpty(networkFilter))
                    {
                        purchasesQuery = purchasesQuery.Where(p => p.NetworkId == networkFilter);
                    }
                    statistics.TotalPurchases = await purchasesQuery.CountAsync(cancellationToken);
                }

                if (context.Inventory != null)
                {
                    var inventoryQuery = context.Inventory.AsQueryable();
                    if (!string.IsNullOrEmpty(networkFilter))
                    {
                        inventoryQuery = inventoryQuery.Where(i => i.NetworkId == networkFilter);
                    }
                    statistics.TotalInventoryItems = await inventoryQuery.CountAsync(cancellationToken);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading statistics: {ex.Message}");
                // إرجاع إحصائيات فارغة في حالة الخطأ
            }

            return statistics;
        }

        /// <summary>
        /// اختبار اتصال قاعدة البيانات مع معالجة الأخطاء
        /// </summary>
        /// <param name="settingsService">خدمة الإعدادات</param>
        /// <param name="cancellationToken">رمز الإلغاء</param>
        /// <returns>حالة الاتصال ورسالة الحالة</returns>
        public static async Task<(bool IsConnected, string StatusMessage)> TestDatabaseConnectionAsync(
            ISettingsService settingsService,
            CancellationToken cancellationToken = default)
        {
            try
            {
                var canConnect = await settingsService.TestDatabaseConnectionAsync();
                return canConnect 
                    ? (true, "متصل") 
                    : (false, "غير متصل");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Database connection test error: {ex.Message}");
                return (false, "خطأ في الاتصال");
            }
        }

        /// <summary>
        /// التحقق من صلاحية الوصول للإعدادات
        /// </summary>
        /// <param name="authService">خدمة المصادقة</param>
        /// <returns>true إذا كان يمكن الوصول للإعدادات</returns>
        public static bool CanAccessSettings(IAuthService authService)
        {
            return authService.CanViewSettings;
        }

        /// <summary>
        /// التحقق من صلاحية إجراء النسخ الاحتياطي
        /// </summary>
        /// <param name="authService">خدمة المصادقة</param>
        /// <returns>true إذا كان يمكن إجراء النسخ الاحتياطي</returns>
        public static bool CanCreateBackup(IAuthService authService)
        {
            // فقط Super Admin يمكنه إجراء النسخ الاحتياطي
            return authService.IsSuperAdmin;
        }

        /// <summary>
        /// التحقق من صلاحية استعادة النسخ الاحتياطية
        /// </summary>
        /// <param name="authService">خدمة المصادقة</param>
        /// <returns>true إذا كان يمكن استعادة النسخ الاحتياطية</returns>
        public static bool CanRestoreBackup(IAuthService authService)
        {
            // فقط Super Admin يمكنه استعادة النسخ الاحتياطية
            return authService.IsSuperAdmin;
        }

        /// <summary>
        /// التحقق من صلاحية تحديث بنية قاعدة البيانات
        /// </summary>
        /// <param name="authService">خدمة المصادقة</param>
        /// <returns>true إذا كان يمكن تحديث بنية قاعدة البيانات</returns>
        public static bool CanUpdateDatabaseStructure(IAuthService authService)
        {
            // فقط Super Admin يمكنه تحديث بنية قاعدة البيانات
            return authService.IsSuperAdmin;
        }

        /// <summary>
        /// التحقق من صلاحية تعديل إعدادات قاعدة البيانات
        /// </summary>
        /// <param name="authService">خدمة المصادقة</param>
        /// <returns>true إذا كان يمكن تعديل إعدادات قاعدة البيانات</returns>
        public static bool CanEditDatabaseSettings(IAuthService authService)
        {
            // فقط Super Admin يمكنه تعديل إعدادات قاعدة البيانات
            return authService.IsSuperAdmin;
        }

        /// <summary>
        /// عرض رسالة عدم وجود صلاحية للإعدادات
        /// </summary>
        /// <param name="action">نوع العملية</param>
        public static void ShowSettingsPermissionDeniedMessage(string action)
        {
            System.Windows.MessageBox.Show(
                $"ليس لديك صلاحية {action}",
                "غير مسموح",
                System.Windows.MessageBoxButton.OK,
                System.Windows.MessageBoxImage.Warning);
        }

        /// <summary>
        /// تحميل معلومات قاعدة البيانات مع معالجة الأخطاء
        /// </summary>
        /// <param name="databaseService">خدمة قاعدة البيانات</param>
        /// <param name="cancellationToken">رمز الإلغاء</param>
        /// <returns>معلومات قاعدة البيانات</returns>
        public static async Task<(long SizeInBytes, string SizeDisplay)> GetDatabaseInfoAsync(
            IDatabaseService databaseService,
            CancellationToken cancellationToken = default)
        {
            try
            {
                var dbInfo = await databaseService.GetDatabaseInfoAsync();
                return (dbInfo.SizeInBytes, dbInfo.SizeDisplay);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting database info: {ex.Message}");
                return (0, "غير متاح");
            }
        }

        /// <summary>
        /// تحميل الإعدادات مع القيم الافتراضية
        /// </summary>
        /// <param name="settingsService">خدمة الإعدادات</param>
        /// <param name="cancellationToken">رمز الإلغاء</param>
        /// <returns>إعدادات التطبيق</returns>
        public static async Task<Models.AppSettings> LoadSettingsWithDefaultsAsync(
            ISettingsService settingsService,
            CancellationToken cancellationToken = default)
        {
            try
            {
                var serviceSettings = await settingsService.GetSettingsAsync();

                // تحويل من Services.AppSettings إلى Models.AppSettings
                return new Models.AppSettings
                {
                    RememberLogin = serviceSettings.RememberLogin,
                    DefaultNetwork = serviceSettings.DefaultNetwork ?? "",
                    PingTimeout = serviceSettings.PingTimeout,
                    ShowNotifications = serviceSettings.ShowNotifications,
                    Theme = serviceSettings.Theme ?? "Light",
                    Language = serviceSettings.Language ?? "العربية",
                    BackupLocation = serviceSettings.BackupLocation ?? System.IO.Path.Combine(
                        Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),
                        "NetworkManagement_Backups"),
                    AutoBackupEnabled = serviceSettings.AutoBackupEnabled,
                    AutoBackupDays = serviceSettings.AutoBackupDays,
                    LastBackupDate = serviceSettings.LastBackupDate,
                    MySQLServer = serviceSettings.MySQLServer ?? "localhost",
                    MySQLDatabase = serviceSettings.MySQLDatabase ?? "NetworkManagementDB",
                    MySQLUser = serviceSettings.MySQLUser ?? "root",
                    MySQLPassword = serviceSettings.MySQLPassword ?? "",
                    AutoPingEnabled = serviceSettings.AutoPingEnabled,
                    AutoPingInterval = serviceSettings.AutoPingInterval
                };
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Settings loading error: {ex.Message}");

                // إرجاع القيم الافتراضية في حالة الخطأ
                return Models.AppSettings.CreateDefault();
            }
        }
    }

    /// <summary>
    /// فئة لتخزين إحصائيات النظام
    /// </summary>
    public class SystemStatistics
    {
        public int TotalDevices { get; set; }
        public int TotalSites { get; set; }
        public int TotalUsers { get; set; }
        public int TotalPurchases { get; set; }
        public int TotalInventoryItems { get; set; }
    }
}
