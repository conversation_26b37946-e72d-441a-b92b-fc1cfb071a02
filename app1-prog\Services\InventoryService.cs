using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using NetworkManagement.Data;
using NetworkManagement.Models;

namespace NetworkManagement.Services
{
    public class InventoryService : IInventoryService
    {
        private readonly IServiceProvider _serviceProvider;

        public InventoryService(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        public async Task<IEnumerable<Inventory>> GetAllAsync(string? networkFilter = null, CancellationToken cancellationToken = default)
        {
            var scope = _serviceProvider.CreateScope();
            try
            {
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                var query = context.Inventory?.Include(i => i.Network).AsQueryable() ??
                    throw new InvalidOperationException("DbSet<Inventory> is null");

                if (!string.IsNullOrEmpty(networkFilter))
                {
                    query = query.Where(i => i.NetworkId == networkFilter);
                }

                return await query.OrderBy(i => i.Name).ToListAsync(cancellationToken).ConfigureAwait(false);
            }
            finally
            {
                scope.Dispose();
            }
        }

        public async Task<Inventory?> GetByIdAsync(string id, CancellationToken cancellationToken = default)
        {
            var scope = _serviceProvider.CreateScope();
            try
            {
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                if (context.Inventory == null)
                    throw new InvalidOperationException("DbSet<Inventory> is null");

                return await context.Inventory
                    .Include(i => i.Network)
                    .FirstOrDefaultAsync(i => i.Id == id, cancellationToken)
                    .ConfigureAwait(false);
            }
            finally
            {
                scope.Dispose();
            }
        }

        public async Task<Inventory> CreateAsync(Inventory inventory, CancellationToken cancellationToken = default)
        {
            var scope = _serviceProvider.CreateScope();
            try
            {
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                if (context.Inventory == null)
                    throw new InvalidOperationException("DbSet<Inventory> is null");

                inventory.Id = Guid.NewGuid().ToString();
                inventory.CreatedAt = DateTime.Now;
                inventory.LastUpdated = DateTime.Now;
                context.Inventory.Add(inventory);
                await context.SaveChangesAsync(cancellationToken).ConfigureAwait(false);
                return inventory;
            }
            finally
            {
                scope.Dispose();
            }
        }

        public async Task<Inventory> UpdateAsync(Inventory inventory, CancellationToken cancellationToken = default)
        {
            var scope = _serviceProvider.CreateScope();
            try
            {
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                if (context.Inventory == null)
                    throw new InvalidOperationException("DbSet<Inventory> is null");

                inventory.UpdatedAt = DateTime.Now;
                inventory.LastUpdated = DateTime.Now;
                context.Inventory.Update(inventory);
                await context.SaveChangesAsync(cancellationToken).ConfigureAwait(false);
                return inventory;
            }
            finally
            {
                scope.Dispose();
            }
        }

        public async Task<bool> DeleteAsync(string id, CancellationToken cancellationToken = default)
        {
            var scope = _serviceProvider.CreateScope();
            try
            {
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                if (context.Inventory == null)
                    throw new InvalidOperationException("DbSet<Inventory> is null");

                var inventory = await context.Inventory.FindAsync(new object[] { id }, cancellationToken).ConfigureAwait(false);
                if (inventory == null) return false;

                context.Inventory.Remove(inventory);
                await context.SaveChangesAsync(cancellationToken).ConfigureAwait(false);
                return true;
            }
            finally
            {
                scope.Dispose();
            }
        }

        public async Task<IEnumerable<Inventory>> SearchAsync(string searchTerm, string? networkFilter = null, CancellationToken cancellationToken = default)
        {
            var scope = _serviceProvider.CreateScope();
            try
            {
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                var query = context.Inventory?.Include(i => i.Network).AsQueryable() ??
                    throw new InvalidOperationException("DbSet<Inventory> is null");

                if (!string.IsNullOrEmpty(networkFilter))
                {
                    query = query.Where(i => i.NetworkId == networkFilter);
                }

                if (!string.IsNullOrEmpty(searchTerm))
                {
                    query = query.Where(i =>
                        i.Name.Contains(searchTerm) ||
                        i.Category.Contains(searchTerm) ||
                        (i.Description ?? "").Contains(searchTerm));
                }

                return await query.OrderBy(i => i.Name).ToListAsync(cancellationToken).ConfigureAwait(false);
            }
            finally
            {
                scope.Dispose();
            }
        }

        public async Task<IEnumerable<Inventory>> GetLowStockItemsAsync(string? networkFilter = null, CancellationToken cancellationToken = default)
        {
            var scope = _serviceProvider.CreateScope();
            try
            {
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                var query = context.Inventory?.Include(i => i.Network).AsQueryable() ??
                    throw new InvalidOperationException("DbSet<Inventory> is null");

                if (!string.IsNullOrEmpty(networkFilter))
                {
                    query = query.Where(i => i.NetworkId == networkFilter);
                }

                return await query
                    .Where(i => i.MinimumStock.HasValue && i.Quantity <= i.MinimumStock.Value)
                    .OrderBy(i => i.Quantity)
                    .ToListAsync(cancellationToken).ConfigureAwait(false);
            }
            finally
            {
                scope.Dispose();
            }
        }

        public async Task<InventoryStatistics> GetStatisticsAsync(string? networkFilter = null, CancellationToken cancellationToken = default)
        {
            var scope = _serviceProvider.CreateScope();
            try
            {
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                var query = context.Inventory?.AsQueryable() ??
                    throw new InvalidOperationException("DbSet<Inventory> is null");

                if (!string.IsNullOrEmpty(networkFilter))
                {
                    query = query.Where(i => i.NetworkId == networkFilter);
                }

                var items = await query.ToListAsync(cancellationToken).ConfigureAwait(false);

                return new InventoryStatistics
                {
                    TotalItems = items.Count,
                    LowStockCount = items.Count(i => i.MinimumStock.HasValue && i.Quantity <= i.MinimumStock.Value),
                    TotalQuantity = items.Sum(i => i.Quantity),
                    TotalValue = items.Where(i => i.UnitPrice.HasValue).Sum(i => i.Quantity * (i.UnitPrice ?? 0))
                };
            }
            finally
            {
                scope.Dispose();
            }
        }
    }
}

