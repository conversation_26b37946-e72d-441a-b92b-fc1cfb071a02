<Application x:Class="NetworkManagement.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:converters="clr-namespace:NetworkManagement.Converters">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design -->
                <materialDesign:BundledTheme BaseTheme="Dark" PrimaryColor="Blue" SecondaryColor="Lime" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />

                <!-- Custom Styles -->
                <ResourceDictionary>
                    <!-- Converters -->
                    <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
                    <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter"/>
                    <converters:InverseBooleanConverter x:Key="InverseBooleanConverter"/>
                    <converters:StringToVisibilityConverter x:Key="StringToVisibilityConverter"/>
                    <converters:ZeroToVisibilityConverter x:Key="ZeroToVisibilityConverter"/>
                    <converters:CountToVisibilityConverter x:Key="CountToVisibilityConverter"/>
                    <converters:BooleanToStringConverter x:Key="BooleanToStringConverter"/>
                    <converters:BooleanToColorConverter x:Key="BooleanToColorConverter"/>
                    <converters:BoolToStatusColorConverter x:Key="BoolToStatusColorConverter"/>
                    <converters:BoolToStatusTextConverter x:Key="BoolToStatusTextConverter"/>

                    <!-- Permission Converters -->
                    <converters:CanEditDataConverter x:Key="CanEditDataConverter"/>
                    <converters:CanDeleteDataConverter x:Key="CanDeleteDataConverter"/>
                    <converters:CanViewDataConverter x:Key="CanViewDataConverter"/>
                    <converters:PermissionConverter x:Key="PermissionConverter"/>
                    <converters:CanEditUserConverter x:Key="CanEditUserConverter"/>
                    <converters:CanDeleteUserConverter x:Key="CanDeleteUserConverter"/>
                    <converters:CanEditTaskConverter x:Key="CanEditTaskConverter"/>
                    <converters:CanDeleteTaskConverter x:Key="CanDeleteTaskConverter"/>
                    <converters:BoolToToggleTextConverter x:Key="BoolToToggleTextConverter"/>
                    <converters:NotNullToBooleanConverter x:Key="NotNullToBooleanConverter"/>
                    <converters:AndConverter x:Key="AndConverter"/>
                    <converters:BoolToSortDirectionConverter x:Key="BoolToSortDirectionConverter"/>
                    <converters:BoolToSortIconConverter x:Key="BoolToSortIconConverter"/>

                    <!-- نظام الألوان الموحد -->
                    <SolidColorBrush x:Key="StatusConnectedBrush" Color="#4CAF50"/>
                    <SolidColorBrush x:Key="StatusDisconnectedBrush" Color="#F44336"/>
                    <SolidColorBrush x:Key="StatusMaintenanceBrush" Color="#FF9800"/>
                    <SolidColorBrush x:Key="StatusDisabledBrush" Color="#9E9E9E"/>
                    <SolidColorBrush x:Key="StatusActiveBrush" Color="#4CAF50"/>
                    <SolidColorBrush x:Key="StatusInactiveBrush" Color="#F44336"/>

                    <SolidColorBrush x:Key="StatisticsBlueBrush" Color="#2196F3"/>
                    <SolidColorBrush x:Key="StatisticsGreenBrush" Color="#4CAF50"/>
                    <SolidColorBrush x:Key="StatisticsOrangeBrush" Color="#FF9800"/>
                    <SolidColorBrush x:Key="StatisticsRedBrush" Color="#F44336"/>
                    <SolidColorBrush x:Key="StatisticsPurpleBrush" Color="#9C27B0"/>

                    <!-- نظام المسافات الموحد -->
                    <Thickness x:Key="SpacingXS">5</Thickness>
                    <Thickness x:Key="SpacingS">10</Thickness>
                    <Thickness x:Key="SpacingM">15</Thickness>
                    <Thickness x:Key="SpacingL">20</Thickness>
                    <Thickness x:Key="SpacingXL">30</Thickness>

                    <Thickness x:Key="CardMargin">10</Thickness>
                    <Thickness x:Key="CardPadding">20</Thickness>
                    <Thickness x:Key="ButtonMargin">5</Thickness>
                    <Thickness x:Key="ElementMargin">0,0,0,20</Thickness>

                    <!-- نظام Typography الموحد -->
                    <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
                        <Setter Property="FontSize" Value="24"/>
                        <Setter Property="FontWeight" Value="Bold"/>
                        <Setter Property="Foreground" Value="{StaticResource StatisticsBlueBrush}"/>
                        <Setter Property="Margin" Value="{StaticResource ElementMargin}"/>
                    </Style>

                    <Style x:Key="SubHeaderTextStyle" TargetType="TextBlock">
                        <Setter Property="FontSize" Value="18"/>
                        <Setter Property="FontWeight" Value="Medium"/>
                        <Setter Property="Margin" Value="{StaticResource SpacingM}"/>
                    </Style>

                    <Style x:Key="BodyTextStyle" TargetType="TextBlock">
                        <Setter Property="FontSize" Value="14"/>
                        <Setter Property="FontWeight" Value="Normal"/>
                    </Style>

                    <Style x:Key="CaptionTextStyle" TargetType="TextBlock">
                        <Setter Property="FontSize" Value="12"/>
                        <Setter Property="FontWeight" Value="Normal"/>
                    </Style>

                    <Style x:Key="StatisticNumberStyle" TargetType="TextBlock">
                        <Setter Property="FontSize" Value="32"/>
                        <Setter Property="FontWeight" Value="Bold"/>
                    </Style>

                    <!-- أنماط الحالة الموحدة -->
                    <Style x:Key="StatusIndicatorStyle" TargetType="Ellipse">
                        <Setter Property="Width" Value="8"/>
                        <Setter Property="Height" Value="8"/>
                        <Setter Property="Margin" Value="0,0,5,0"/>
                        <Setter Property="VerticalAlignment" Value="Center"/>
                        <Setter Property="Fill" Value="{StaticResource StatusDisabledBrush}"/>
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding Status}" Value="متصل">
                                <Setter Property="Fill" Value="{StaticResource StatusConnectedBrush}"/>
                            </DataTrigger>
                            <DataTrigger Binding="{Binding Status}" Value="غير متصل">
                                <Setter Property="Fill" Value="{StaticResource StatusDisconnectedBrush}"/>
                            </DataTrigger>
                            <DataTrigger Binding="{Binding Status}" Value="active">
                                <Setter Property="Fill" Value="{StaticResource StatusActiveBrush}"/>
                            </DataTrigger>
                            <DataTrigger Binding="{Binding Status}" Value="inactive">
                                <Setter Property="Fill" Value="{StaticResource StatusInactiveBrush}"/>
                            </DataTrigger>
                            <DataTrigger Binding="{Binding Status}" Value="maintenance">
                                <Setter Property="Fill" Value="{StaticResource StatusMaintenanceBrush}"/>
                            </DataTrigger>
                            <DataTrigger Binding="{Binding Status}" Value="disabled">
                                <Setter Property="Fill" Value="{StaticResource StatusDisabledBrush}"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>

                    <Style x:Key="StatusTextStyle" TargetType="TextBlock">
                        <Setter Property="VerticalAlignment" Value="Center"/>
                        <Setter Property="FontWeight" Value="Bold"/>
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding Status}" Value="متصل">
                                <Setter Property="Foreground" Value="{StaticResource StatusConnectedBrush}"/>
                            </DataTrigger>
                            <DataTrigger Binding="{Binding Status}" Value="غير متصل">
                                <Setter Property="Foreground" Value="{StaticResource StatusDisconnectedBrush}"/>
                            </DataTrigger>
                            <DataTrigger Binding="{Binding Status}" Value="active">
                                <Setter Property="Foreground" Value="{StaticResource StatusActiveBrush}"/>
                            </DataTrigger>
                            <DataTrigger Binding="{Binding Status}" Value="inactive">
                                <Setter Property="Foreground" Value="{StaticResource StatusInactiveBrush}"/>
                            </DataTrigger>
                            <DataTrigger Binding="{Binding Status}" Value="maintenance">
                                <Setter Property="Foreground" Value="{StaticResource StatusMaintenanceBrush}"/>
                            </DataTrigger>
                            <DataTrigger Binding="{Binding Status}" Value="disabled">
                                <Setter Property="Foreground" Value="{StaticResource StatusDisabledBrush}"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>

                    <!-- الأنماط المحدثة -->
                    <Style x:Key="PageHeaderStyle" TargetType="TextBlock" BasedOn="{StaticResource HeaderTextStyle}"/>

                    <Style x:Key="CardStyle" TargetType="materialDesign:Card">
                        <Setter Property="Margin" Value="{StaticResource CardMargin}"/>
                        <Setter Property="Padding" Value="{StaticResource CardPadding}"/>
                        <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth2"/>
                    </Style>

                    <!-- نمط Card محسن للصفحات الرئيسية -->
                    <Style x:Key="MinimalCardStyle" TargetType="materialDesign:Card">
                        <Setter Property="Margin" Value="{StaticResource SpacingXS}"/>
                        <Setter Property="Padding" Value="{StaticResource SpacingM}"/>
                        <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth1"/>
                    </Style>

                    <!-- نمط للمحتوى المتدفق -->
                    <Style x:Key="FlowContentStyle" TargetType="Border">
                        <Setter Property="Background" Value="{DynamicResource MaterialDesignPaper}"/>
                        <Setter Property="Padding" Value="{StaticResource CardPadding}"/>
                        <Setter Property="Margin" Value="{StaticResource SpacingXS}"/>
                        <Setter Property="CornerRadius" Value="4"/>
                    </Style>

                    <Style x:Key="MenuButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
                        <Setter Property="Height" Value="50"/>
                        <Setter Property="Margin" Value="{StaticResource ButtonMargin}"/>
                        <Setter Property="FontSize" Value="14"/>
                        <Setter Property="FontWeight" Value="Medium"/>
                    </Style>

                    <!-- أنماط الجداول الموحدة -->
                    <Style x:Key="StandardDataGridStyle" TargetType="DataGrid">
                        <Setter Property="AlternatingRowBackground" Value="{DynamicResource MaterialDesignDivider}"/>
                        <Setter Property="GridLinesVisibility" Value="Horizontal"/>
                        <Setter Property="HeadersVisibility" Value="Column"/>
                        <Setter Property="CanUserAddRows" Value="False"/>
                        <Setter Property="CanUserDeleteRows" Value="False"/>
                        <Setter Property="CanUserSortColumns" Value="True"/>
                        <Setter Property="CanUserReorderColumns" Value="True"/>
                        <Setter Property="CanUserResizeColumns" Value="True"/>
                        <Setter Property="IsReadOnly" Value="True"/>
                        <Setter Property="SelectionMode" Value="Extended"/>
                        <Setter Property="SelectionUnit" Value="FullRow"/>
                        <Setter Property="EnableRowVirtualization" Value="True"/>
                        <Setter Property="EnableColumnVirtualization" Value="True"/>
                        <Setter Property="VirtualizingPanel.VirtualizationMode" Value="Recycling"/>
                        <Setter Property="VirtualizingPanel.IsVirtualizing" Value="True"/>
                        <Setter Property="ScrollViewer.CanContentScroll" Value="True"/>
                        <Setter Property="Margin" Value="{StaticResource SpacingS}"/>
                    </Style>

                    <!-- أنماط أزرار الإجراءات -->
                    <Style x:Key="ActionButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
                        <Setter Property="Margin" Value="{StaticResource SpacingS}"/>
                        <Setter Property="MinWidth" Value="80"/>
                        <Setter Property="Height" Value="36"/>
                    </Style>

                    <Style x:Key="SecondaryActionButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignOutlinedButton}">
                        <Setter Property="Margin" Value="{StaticResource SpacingS}"/>
                        <Setter Property="MinWidth" Value="80"/>
                        <Setter Property="Height" Value="36"/>
                    </Style>

                    <!-- أنماط النماذج -->
                    <Style x:Key="FormTextBoxStyle" TargetType="TextBox">
                        <Setter Property="Margin" Value="{StaticResource ElementMargin}"/>
                        <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
                    </Style>

                    <Style x:Key="FormComboBoxStyle" TargetType="ComboBox">
                        <Setter Property="Margin" Value="{StaticResource ElementMargin}"/>
                        <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
                    </Style>
                </ResourceDictionary>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Application.Resources>
</Application>
