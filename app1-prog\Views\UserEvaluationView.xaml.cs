using System;
using System.Windows.Controls;
using NetworkManagement.ViewModels;
using NetworkManagement.Views;

namespace NetworkManagement.Views
{
    public partial class UserEvaluationView : UserControl
    {
        public UserEvaluationView()
        {
            InitializeComponent();
            Loaded += UserEvaluationView_Loaded;
        }

        private async void UserEvaluationView_Loaded(object sender, System.Windows.RoutedEventArgs e)
        {
            try
            {
                if (DataContext is UserEvaluationViewModel viewModel)
                {
                    await viewModel.InitializeAsync();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"UserEvaluationView_Loaded: خطأ - {ex.Message}");
                System.Windows.MessageBox.Show(
                    $"خطأ في تحميل صفحة التقييمات:\n{ex.Message}",
                    "خطأ",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Error);
            }
        }
    }
}
