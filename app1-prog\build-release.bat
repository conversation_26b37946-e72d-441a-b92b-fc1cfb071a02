@echo off
echo ========================================
echo    Building Shabaka Pro - Enhanced Version
echo ========================================
echo.

echo [1/5] Cleaning old folders...
if exist "dist" (
    echo Removing old dist folder...
    rmdir /s /q "dist"
)
if exist "bin\Release" (
    echo Removing old bin\Release folder...
    rmdir /s /q "bin\Release"
)
if exist "obj\Release" (
    echo Removing old obj\Release folder...
    rmdir /s /q "obj\Release"
)
echo Old folders cleaned.

echo.
echo [2/5] Restoring packages...
dotnet restore NetworkManagement.csproj --no-cache --verbosity minimal
if %errorlevel% neq 0 (
    echo Error restoring packages!
    pause
    exit /b 1
)

echo.
echo [3/5] Building application in optimized release mode...
dotnet publish NetworkManagement.csproj ^
    -c Release ^
    -r win-x64 ^
    --self-contained true ^
    -o dist ^
    --verbosity minimal ^
    -p:PublishSingleFile=true ^
    -p:IncludeNativeLibrariesForSelfExtract=true ^
    -p:PublishTrimmed=false ^
    -p:PublishReadyToRun=true ^
    -p:TieredCompilation=true ^
    -p:TieredPGO=true

if %errorlevel% neq 0 (
    echo Error building application!
    pause
    exit /b 1
)

echo.
echo [4/5] Copying additional files...
if not exist "dist\Database" mkdir "dist\Database"
copy "Database\*.sql" "dist\Database\" >nul 2>&1
copy "*.md" "dist\" >nul 2>&1
copy "optimize-performance.bat" "dist\" >nul 2>&1

echo.
echo [5/5] Creating installer...
if exist "ShabakaPro-Setup.iss" (
    "C:\Program Files (x86)\Inno Setup 6\ISCC.exe" "ShabakaPro-Setup.iss"
    if %errorlevel% neq 0 (
        echo Warning: Installer not created. Make sure Inno Setup is installed.
    ) else (
        echo Installer created successfully!
    )
) else (
    echo Warning: ShabakaPro-Setup.iss file not found.
)

echo.
echo ========================================
echo Application built successfully!
echo ========================================
echo.
echo Output files:
echo - Application folder: dist\
echo - Installer file: Output\ShabakaPro-Setup.exe
echo.
echo Application size:
for /f %%A in ('dir /s /-c "dist" ^| find "bytes"') do echo %%A
echo.
pause
