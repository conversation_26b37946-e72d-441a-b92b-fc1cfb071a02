using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using NetworkManagement.Services;
using Microsoft.Win32;

namespace NetworkManagement.ViewModels
{
    public partial class DatabaseSetupViewModel : ObservableObject
    {
        private readonly ISettingsService _settingsService;
        private readonly IDatabaseService _databaseService;

        public DatabaseSetupViewModel(ISettingsService settingsService, IDatabaseService databaseService)
        {
            _settingsService = settingsService;
            _databaseService = databaseService;
            
            // Load current settings
            LoadCurrentSettings();
        }

        [ObservableProperty]
        private string mySQLServer = "localhost";

        [ObservableProperty]
        private int mySQLPort = 3306;

        [ObservableProperty]
        private string mySQLDatabase = "NetworkManagementDB";

        [ObservableProperty]
        private string mySQLUser = "root";

        [ObservableProperty]
        private string mySQLPassword = "";

        [ObservableProperty]
        private bool isDatabaseConnected = false;

        [ObservableProperty]
        private string databaseConnectionStatus = "غير متصل";

        [ObservableProperty]
        private bool isLoading = false;

        [ObservableProperty]
        private string progressMessage = "";

        public bool IsNotLoading => !IsLoading;
        public bool CanBackupDatabase => IsDatabaseConnected && !IsLoading;
        public bool CanDeleteDatabase => IsDatabaseConnected && !IsLoading;

        // Events
        public event Action? DatabaseSetupCompleted;
        public event Action? ExitRequested;

        private void LoadCurrentSettings()
        {
            try
            {
                var settings = _settingsService.LoadSettings();
                MySQLServer = settings.MySQLServer;
                MySQLDatabase = settings.MySQLDatabase;
                MySQLUser = settings.MySQLUser;
                MySQLPassword = settings.MySQLPassword;
                MySQLPort = settings.MySQLPort;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading settings: {ex.Message}");
            }
        }

        [RelayCommand]
        private async Task TestConnectionAsync()
        {
            try
            {
                // التحقق من صحة المدخلات
                if (!ValidateInputs())
                {
                    return;
                }

                IsLoading = true;
                ProgressMessage = "جاري اختبار الاتصال...";
                IsDatabaseConnected = false;
                DatabaseConnectionStatus = "جاري الاختبار...";

                // Save current settings temporarily
                await SaveCurrentSettingsAsync();

                // Test connection
                var canConnect = await _settingsService.TestDatabaseConnectionAsync();

                if (canConnect)
                {
                    IsDatabaseConnected = true;
                    DatabaseConnectionStatus = "متصل بنجاح";
                    
                    MessageBox.Show(
                        "✅ تم الاتصال بقاعدة البيانات بنجاح!",
                        "نجح الاختبار",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information);
                }
                else
                {
                    IsDatabaseConnected = false;
                    DatabaseConnectionStatus = "فشل الاتصال";
                    
                    MessageBox.Show(
                        "❌ فشل الاتصال بقاعدة البيانات.\n\n" +
                        "تحقق من:\n" +
                        "• تشغيل خادم MySQL\n" +
                        "• صحة عنوان الخادم والمنفذ\n" +
                        "• صحة اسم المستخدم وكلمة المرور\n" +
                        "• وجود قاعدة البيانات",
                        "فشل الاختبار",
                        MessageBoxButton.OK,
                        MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                IsDatabaseConnected = false;
                DatabaseConnectionStatus = "خطأ في الاختبار";
                MessageBox.Show($"خطأ في اختبار الاتصال: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
                ProgressMessage = "";
                OnPropertyChanged(nameof(IsNotLoading));
                OnPropertyChanged(nameof(CanBackupDatabase));
                OnPropertyChanged(nameof(CanDeleteDatabase));
            }
        }

        [RelayCommand]
        private async Task DeleteDatabaseAsync()
        {
            try
            {
                var result = MessageBox.Show(
                    "⚠️ تحذير خطير!\n\n" +
                    $"سيتم حذف قاعدة البيانات '{MySQLDatabase}' نهائياً!\n" +
                    "جميع البيانات ستفقد ولا يمكن استرجاعها.\n\n" +
                    "هل أنت متأكد من أنك تريد المتابعة؟",
                    "تأكيد حذف قاعدة البيانات",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    var confirmResult = MessageBox.Show(
                        "تأكيد نهائي!\n\n" +
                        "هذا الإجراء لا يمكن التراجع عنه.\n" +
                        "هل تريد حذف قاعدة البيانات فعلاً؟",
                        "تأكيد نهائي",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Stop);

                    if (confirmResult == MessageBoxResult.Yes)
                    {
                        IsLoading = true;
                        ProgressMessage = "جاري حذف قاعدة البيانات...";

                        await SaveCurrentSettingsAsync();
                        var success = await _databaseService.DeleteDatabaseAsync();

                        if (success)
                        {
                            IsDatabaseConnected = false;
                            DatabaseConnectionStatus = "تم حذف قاعدة البيانات";

                            MessageBox.Show(
                                "✅ تم حذف قاعدة البيانات بنجاح!",
                                "تم الحذف",
                                MessageBoxButton.OK,
                                MessageBoxImage.Information);
                        }
                        else
                        {
                            MessageBox.Show(
                                "❌ فشل في حذف قاعدة البيانات.",
                                "فشل الحذف",
                                MessageBoxButton.OK,
                                MessageBoxImage.Error);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف قاعدة البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
                ProgressMessage = "";
                OnPropertyChanged(nameof(IsNotLoading));
                OnPropertyChanged(nameof(CanBackupDatabase));
                OnPropertyChanged(nameof(CanDeleteDatabase));
            }
        }

        [RelayCommand]
        private async Task CreateDatabaseAsync()
        {
            try
            {
                // التحقق من صحة المدخلات
                if (!ValidateInputs())
                {
                    return;
                }

                var result = MessageBox.Show(
                    $"هل تريد إنشاء قاعدة البيانات '{MySQLDatabase}' الجديدة؟\n\n" +
                    "سيتم إنشاء جميع الجداول والبيانات الأساسية.",
                    "إنشاء قاعدة البيانات",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    IsLoading = true;
                    ProgressMessage = "جاري إنشاء قاعدة البيانات...";

                    await SaveCurrentSettingsAsync();
                    var success = await _databaseService.CreateDatabaseAsync();

                    if (success)
                    {
                        MessageBox.Show(
                            "✅ تم إنشاء قاعدة البيانات بنجاح!",
                            "نجح الإنشاء",
                            MessageBoxButton.OK,
                            MessageBoxImage.Information);

                        // Test connection after creation
                        await TestConnectionAsync();
                    }
                    else
                    {
                        MessageBox.Show(
                            "❌ فشل في إنشاء قاعدة البيانات.",
                            "فشل الإنشاء",
                            MessageBoxButton.OK,
                            MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء قاعدة البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
                ProgressMessage = "";
                OnPropertyChanged(nameof(IsNotLoading));
                OnPropertyChanged(nameof(CanBackupDatabase));
            }
        }

        [RelayCommand]
        private async Task ImportDatabaseAsync()
        {
            try
            {
                var openFileDialog = new OpenFileDialog
                {
                    Title = "اختيار ملف قاعدة البيانات",
                    Filter = "SQL Files (*.sql)|*.sql|All Files (*.*)|*.*",
                    DefaultExt = "sql"
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    IsLoading = true;
                    ProgressMessage = "جاري استيراد قاعدة البيانات...";

                    await SaveCurrentSettingsAsync();
                    var success = await _databaseService.ImportDatabaseAsync(openFileDialog.FileName);

                    if (success)
                    {
                        MessageBox.Show(
                            "✅ تم استيراد قاعدة البيانات بنجاح!",
                            "نجح الاستيراد",
                            MessageBoxButton.OK,
                            MessageBoxImage.Information);

                        // Test connection after import
                        await TestConnectionAsync();
                    }
                    else
                    {
                        MessageBox.Show(
                            "❌ فشل في استيراد قاعدة البيانات.",
                            "فشل الاستيراد",
                            MessageBoxButton.OK,
                            MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في استيراد قاعدة البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
                ProgressMessage = "";
                OnPropertyChanged(nameof(IsNotLoading));
                OnPropertyChanged(nameof(CanBackupDatabase));
            }
        }

        [RelayCommand]
        private async Task BackupDatabaseAsync()
        {
            try
            {
                var saveFileDialog = new SaveFileDialog
                {
                    Title = "حفظ النسخة الاحتياطية",
                    Filter = "SQL Files (*.sql)|*.sql",
                    DefaultExt = "sql",
                    FileName = $"backup_{MySQLDatabase}_{DateTime.Now:yyyyMMdd_HHmmss}.sql"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    IsLoading = true;
                    ProgressMessage = "جاري إنشاء النسخة الاحتياطية...";

                    var success = await _databaseService.BackupDatabaseAsync(saveFileDialog.FileName);

                    if (success)
                    {
                        MessageBox.Show(
                            "✅ تم إنشاء النسخة الاحتياطية بنجاح!",
                            "نجح النسخ الاحتياطي",
                            MessageBoxButton.OK,
                            MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show(
                            "❌ فشل في إنشاء النسخة الاحتياطية.",
                            "فشل النسخ الاحتياطي",
                            MessageBoxButton.OK,
                            MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في النسخ الاحتياطي: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
                ProgressMessage = "";
                OnPropertyChanged(nameof(IsNotLoading));
                OnPropertyChanged(nameof(CanBackupDatabase));
            }
        }

        [RelayCommand]
        private async Task RestoreDatabaseAsync()
        {
            try
            {
                var openFileDialog = new OpenFileDialog
                {
                    Title = "اختيار النسخة الاحتياطية",
                    Filter = "SQL Files (*.sql)|*.sql|All Files (*.*)|*.*",
                    DefaultExt = "sql"
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    var result = MessageBox.Show(
                        "⚠️ تحذير: سيتم استبدال قاعدة البيانات الحالية بالكامل!\n\n" +
                        "هل تريد المتابعة؟",
                        "تأكيد الاستعادة",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Warning);

                    if (result == MessageBoxResult.Yes)
                    {
                        IsLoading = true;
                        ProgressMessage = "جاري استعادة قاعدة البيانات...";

                        var success = await _databaseService.RestoreDatabaseAsync(openFileDialog.FileName);

                        if (success)
                        {
                            MessageBox.Show(
                                "✅ تم استعادة قاعدة البيانات بنجاح!",
                                "نجحت الاستعادة",
                                MessageBoxButton.OK,
                                MessageBoxImage.Information);

                            // Test connection after restore
                            await TestConnectionAsync();
                        }
                        else
                        {
                            MessageBox.Show(
                                "❌ فشل في استعادة قاعدة البيانات.",
                                "فشلت الاستعادة",
                                MessageBoxButton.OK,
                                MessageBoxImage.Error);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في استعادة قاعدة البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
                ProgressMessage = "";
                OnPropertyChanged(nameof(IsNotLoading));
                OnPropertyChanged(nameof(CanBackupDatabase));
            }
        }

        [RelayCommand]
        private async Task ContinueToLoginAsync()
        {
            if (IsDatabaseConnected)
            {
                await SaveCurrentSettingsAsync();
                DatabaseSetupCompleted?.Invoke();
            }
            else
            {
                MessageBox.Show(
                    "يجب اختبار الاتصال بقاعدة البيانات أولاً.",
                    "تنبيه",
                    MessageBoxButton.OK,
                    MessageBoxImage.Warning);
            }
        }

        [RelayCommand]
        private void ExitApplication()
        {
            ExitRequested?.Invoke();
        }

        private bool ValidateInputs()
        {
            var validationErrors = new List<string>();

            if (string.IsNullOrWhiteSpace(MySQLServer))
                validationErrors.Add("• عنوان الخادم مطلوب");

            if (MySQLPort <= 0 || MySQLPort > 65535)
                validationErrors.Add("• المنفذ يجب أن يكون بين 1 و 65535");

            if (string.IsNullOrWhiteSpace(MySQLDatabase))
                validationErrors.Add("• اسم قاعدة البيانات مطلوب");

            if (string.IsNullOrWhiteSpace(MySQLUser))
                validationErrors.Add("• اسم المستخدم مطلوب");

            // التحقق من صحة اسم قاعدة البيانات
            if (!string.IsNullOrWhiteSpace(MySQLDatabase) && !IsValidDatabaseName(MySQLDatabase))
                validationErrors.Add("• اسم قاعدة البيانات يحتوي على أحرف غير صالحة");

            if (validationErrors.Any())
            {
                var errorMessage = "يرجى تصحيح الأخطاء التالية:\n\n" + string.Join("\n", validationErrors);
                MessageBox.Show(errorMessage, "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            return true;
        }

        private static bool IsValidDatabaseName(string name)
        {
            // التحقق من صحة اسم قاعدة البيانات MySQL
            if (string.IsNullOrWhiteSpace(name) || name.Length > 64)
                return false;

            // يجب أن يبدأ بحرف أو رقم
            if (!char.IsLetterOrDigit(name[0]))
                return false;

            // يمكن أن يحتوي على أحرف وأرقام و _ فقط
            return name.All(c => char.IsLetterOrDigit(c) || c == '_');
        }

        private async Task SaveCurrentSettingsAsync()
        {
            try
            {
                var settings = new AppSettings
                {
                    MySQLServer = MySQLServer,
                    MySQLDatabase = MySQLDatabase,
                    MySQLUser = MySQLUser,
                    MySQLPassword = MySQLPassword,
                    MySQLPort = MySQLPort
                };

                await _settingsService.SaveSettingsAsync(settings);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving settings: {ex.Message}");
            }
        }
    }
}
