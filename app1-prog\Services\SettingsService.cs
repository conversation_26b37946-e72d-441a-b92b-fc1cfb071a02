using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.Win32;
using MySqlConnector;

namespace NetworkManagement.Services
{
    public class SettingsService : ISettingsService
    {
        private const string REGISTRY_KEY = @"SOFTWARE\NetworkManagement";
        private const string USERNAME_VALUE = "RememberedUsername";
        private const string PASSWORD_VALUE = "RememberedPassword";

        public async Task SaveRememberedCredentialsAsync(string username, string password)
        {
            try
            {
                await Task.Run(() =>
                {
                    using var key = Registry.CurrentUser.CreateSubKey(REGISTRY_KEY);
                    if (key != null)
                    {
                        // تشفير بسيط للبيانات (Base64)
                        var encodedUsername = Convert.ToBase64String(
                            System.Text.Encoding.UTF8.GetBytes(username));
                        var encodedPassword = Convert.ToBase64String(
                            System.Text.Encoding.UTF8.GetBytes(password));

                        key.SetValue(USERNAME_VALUE, encodedUsername);
                        key.SetValue(PASSWORD_VALUE, encodedPassword);
                    }
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving credentials: {ex.Message}");
            }
        }

        public async Task<(string? username, string? password)> GetRememberedCredentialsAsync()
        {
            try
            {
                return await Task.Run(() =>
                {
                    using (var key = Registry.CurrentUser.OpenSubKey(REGISTRY_KEY))
                    {
                        if (key != null)
                        {
                            var encodedUsername = key.GetValue(USERNAME_VALUE) as string;
                            var encodedPassword = key.GetValue(PASSWORD_VALUE) as string;

                            if (!string.IsNullOrEmpty(encodedUsername) && !string.IsNullOrEmpty(encodedPassword))
                            {
                                // فك التشفير
                                var usernameBytes = Convert.FromBase64String(encodedUsername);
                                var passwordBytes = Convert.FromBase64String(encodedPassword);

                                var username = System.Text.Encoding.UTF8.GetString(usernameBytes);
                                var password = System.Text.Encoding.UTF8.GetString(passwordBytes);

                                return ((string?)username, (string?)password);
                            }
                        }
                    }
                    return ((string?)null, (string?)null);
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting credentials: {ex.Message}");
                return ((string?)null, (string?)null);
            }
        }

        public async Task ClearRememberedCredentialsAsync()
        {
            try
            {
                await Task.Run(() =>
                {
                    using (var key = Registry.CurrentUser.OpenSubKey(REGISTRY_KEY, true))
                    {
                        if (key != null)
                        {
                            key.DeleteValue(USERNAME_VALUE, false);
                            key.DeleteValue(PASSWORD_VALUE, false);
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error clearing credentials: {ex.Message}");
            }
        }

        public async Task<bool> HasRememberedCredentialsAsync()
        {
            var (username, password) = await GetRememberedCredentialsAsync();
            return !string.IsNullOrEmpty(username) && !string.IsNullOrEmpty(password);
        }

        public async Task<AppSettings> GetSettingsAsync()
        {
            try
            {
                return await Task.Run(() =>
                {
                    using (var key = Registry.CurrentUser.OpenSubKey(REGISTRY_KEY))
                    {
                        if (key != null)
                        {
                            return new AppSettings
                            {
                                RememberLogin = bool.Parse(key.GetValue("RememberLogin", "true")?.ToString() ?? "true"),
                                DefaultNetwork = key.GetValue("DefaultNetwork")?.ToString(),
                                PingTimeout = int.Parse(key.GetValue("PingTimeout", "5")?.ToString() ?? "5"),
                                ShowNotifications = bool.Parse(key.GetValue("ShowNotifications", "true")?.ToString() ?? "true"),
                                Theme = key.GetValue("Theme")?.ToString() ?? "Light",
                                Language = key.GetValue("Language")?.ToString() ?? "العربية",
                                BackupLocation = key.GetValue("BackupLocation")?.ToString(),
                                AutoBackupEnabled = bool.Parse(key.GetValue("AutoBackupEnabled", "false")?.ToString() ?? "false"),
                                AutoBackupDays = int.Parse(key.GetValue("AutoBackupDays", "7")?.ToString() ?? "7"),
                                LastBackupDate = DateTime.TryParse(key.GetValue("LastBackupDate")?.ToString(), out var date) ? date : null,
                                AutoPingEnabled = bool.Parse(key.GetValue("AutoPingEnabled", "true")?.ToString() ?? "true"),
                                AutoPingInterval = int.Parse(key.GetValue("AutoPingInterval", "30")?.ToString() ?? "30")
                            };
                        }
                        return new AppSettings();
                    }
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting settings: {ex.Message}");
                return new AppSettings();
            }
        }

        public async Task SaveSettingsAsync(object settings)
        {
            try
            {
                if (settings == null)
                {
                    throw new ArgumentNullException(nameof(settings));
                }

                await Task.Run(() =>
                {
                    using (var key = Registry.CurrentUser.CreateSubKey(REGISTRY_KEY))
                    {
                        if (key != null && settings is AppSettings appSettings)
                        {
                            // إعدادات التطبيق العامة
                            key.SetValue("RememberLogin", appSettings.RememberLogin.ToString());
                            key.SetValue("DefaultNetwork", appSettings.DefaultNetwork ?? "");
                            key.SetValue("PingTimeout", appSettings.PingTimeout.ToString());
                            key.SetValue("ShowNotifications", appSettings.ShowNotifications.ToString());
                            key.SetValue("Theme", appSettings.Theme ?? "Light");
                            key.SetValue("Language", appSettings.Language ?? "العربية");

                            // إعدادات النسخ الاحتياطي
                            key.SetValue("BackupLocation", appSettings.BackupLocation ?? "");
                            key.SetValue("AutoBackupEnabled", appSettings.AutoBackupEnabled.ToString());
                            key.SetValue("AutoBackupDays", appSettings.AutoBackupDays.ToString());
                            if (appSettings.LastBackupDate.HasValue)
                            {
                                key.SetValue("LastBackupDate", appSettings.LastBackupDate.Value.ToString("yyyy-MM-dd HH:mm:ss"));
                            }

                            // إعدادات قاعدة البيانات
                            key.SetValue("MySQLServer", appSettings.MySQLServer ?? "localhost");
                            key.SetValue("MySQLDatabase", appSettings.MySQLDatabase ?? "NetworkManagementDB");
                            key.SetValue("MySQLUser", appSettings.MySQLUser ?? "root");
                            key.SetValue("MySQLPassword", appSettings.MySQLPassword ?? "");
                            key.SetValue("MySQLPort", appSettings.MySQLPort.ToString());

                            // إعدادات الفحص التلقائي
                            key.SetValue("AutoPingEnabled", appSettings.AutoPingEnabled.ToString());
                            key.SetValue("AutoPingInterval", appSettings.AutoPingInterval.ToString());
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving settings: {ex.Message}");
                throw new InvalidOperationException($"فشل في حفظ الإعدادات: {ex.Message}", ex);
            }
        }

        public async Task ResetSettingsAsync()
        {
            try
            {
                await Task.Run(() =>
                {
                    using (var key = Registry.CurrentUser.OpenSubKey(REGISTRY_KEY, true))
                    {
                        if (key != null)
                        {
                            var valueNames = key.GetValueNames();
                            foreach (var valueName in valueNames)
                            {
                                if (valueName != USERNAME_VALUE && valueName != PASSWORD_VALUE)
                                {
                                    key.DeleteValue(valueName, false);
                                }
                            }
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error resetting settings: {ex.Message}");
                throw;
            }
        }

        public async Task UpdateLastBackupDateAsync(DateTime date)
        {
            try
            {
                await Task.Run(() =>
                {
                    using (var key = Registry.CurrentUser.CreateSubKey(REGISTRY_KEY))
                    {
                        if (key != null)
                        {
                            key.SetValue("LastBackupDate", date.ToString("yyyy-MM-dd HH:mm:ss"));
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating backup date: {ex.Message}");
                throw;
            }
        }


        public async Task<bool> TestDatabaseConnectionAsync()
        {
            try
            {
                // اختبار اتصال MySQL
                var settings = LoadSettings();
                var connectionString = $"Server={settings.MySQLServer};Database={settings.MySQLDatabase};User={settings.MySQLUser};Password={settings.MySQLPassword};ConnectionTimeout=10;";

                return await Task.Run(async () =>
                {
                    try
                    {
                        using var connection = new MySqlConnection(connectionString);
                        await connection.OpenAsync();

                        // اختبار بسيط للتأكد من الاتصال
                        using var command = connection.CreateCommand();
                        command.CommandText = "SELECT 1";
                        var result = await command.ExecuteScalarAsync();

                        // اختبار إضافي للتأكد من وجود قاعدة البيانات
                        command.CommandText = "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = @dbName";
                        command.Parameters.AddWithValue("@dbName", settings.MySQLDatabase);
                        var tableCount = await command.ExecuteScalarAsync();

                        System.Diagnostics.Debug.WriteLine($"MySQL connection successful. Database: {settings.MySQLDatabase}, Tables: {tableCount}");
                        return result != null;
                    }
                    catch (MySqlException mysqlEx)
                    {
                        string errorMessage = mysqlEx.Number switch
                        {
                            1045 => "خطأ في اسم المستخدم أو كلمة المرور",
                            1049 => "قاعدة البيانات غير موجودة",
                            2003 => "لا يمكن الاتصال بخادم MySQL",
                            _ => $"خطأ MySQL: {mysqlEx.Message}"
                        };
                        System.Diagnostics.Debug.WriteLine($"MySQL error ({mysqlEx.Number}): {errorMessage}");
                        return false;
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"MySQL connection test failed: {ex.Message}");
                        return false;
                    }
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Database connection test failed: {ex.Message}");
                return false;
            }
        }

        public AppSettings LoadSettings()
        {
            try
            {
                using (var key = Registry.CurrentUser.OpenSubKey(REGISTRY_KEY))
                {
                    if (key != null)
                    {
                        return new AppSettings
                        {
                            RememberLogin = bool.TryParse(key.GetValue("RememberLogin")?.ToString(), out bool rememberLogin) ? rememberLogin : true,
                            DefaultNetwork = key.GetValue("DefaultNetwork")?.ToString() ?? "",
                            PingTimeout = int.TryParse(key.GetValue("PingTimeout")?.ToString(), out int pingTimeout) ? pingTimeout : 5,
                            ShowNotifications = bool.TryParse(key.GetValue("ShowNotifications")?.ToString(), out bool showNotifications) ? showNotifications : true,
                            Theme = key.GetValue("Theme")?.ToString() ?? "Light",
                            Language = key.GetValue("Language")?.ToString() ?? "العربية",
                            BackupLocation = key.GetValue("BackupLocation")?.ToString() ?? "",
                            AutoBackupEnabled = bool.TryParse(key.GetValue("AutoBackupEnabled")?.ToString(), out bool autoBackup) ? autoBackup : false,
                            AutoBackupDays = int.TryParse(key.GetValue("AutoBackupDays")?.ToString(), out int backupDays) ? backupDays : 7,
                            LastBackupDate = DateTime.TryParse(key.GetValue("LastBackupDate")?.ToString(), out DateTime lastBackup) ? lastBackup : (DateTime?)null,
                            MySQLServer = key.GetValue("MySQLServer")?.ToString() ?? "localhost",
                            MySQLDatabase = key.GetValue("MySQLDatabase")?.ToString() ?? "NetworkManagementDB",
                            MySQLUser = key.GetValue("MySQLUser")?.ToString() ?? "root",
                            MySQLPassword = key.GetValue("MySQLPassword")?.ToString() ?? "",
                            MySQLPort = int.TryParse(key.GetValue("MySQLPort")?.ToString(), out int port) ? port : 3306,
                            AutoPingEnabled = bool.TryParse(key.GetValue("AutoPingEnabled")?.ToString(), out bool autoPing) ? autoPing : true,
                            AutoPingInterval = int.TryParse(key.GetValue("AutoPingInterval")?.ToString(), out int pingInterval) ? pingInterval : 30
                        };
                    }
                    return new AppSettings();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading settings: {ex.Message}");
                return new AppSettings();
            }
        }
    }
}
