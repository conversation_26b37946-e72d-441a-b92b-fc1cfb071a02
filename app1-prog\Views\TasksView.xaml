<UserControl x:Class="NetworkManagement.Views.TasksView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Page Header -->
        <TextBlock Grid.Row="0" Text="أعمالي" Style="{StaticResource PageHeaderStyle}"/>

        <!-- Toolbar - استخدام النمط المحسن -->
        <materialDesign:Card Grid.Row="1" Style="{StaticResource MinimalCardStyle}" Margin="{StaticResource SpacingXS}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" MinWidth="200"/>
                    <ColumnDefinition Width="Auto" MinWidth="100"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Filters -->
                <WrapPanel Grid.Column="0" Orientation="Horizontal">
                    <!-- Network Filter -->
                    <ComboBox materialDesign:HintAssist.Hint="فلترة حسب الشبكة"
                             ItemsSource="{Binding AvailableNetworks}"
                             SelectedItem="{Binding SelectedNetwork}"
                             DisplayMemberPath="Name"
                             MinWidth="120" Width="150" Margin="{StaticResource SpacingXS}"
                             Visibility="{Binding AvailableNetworks.Count, Converter={StaticResource CountToVisibilityConverter}}"/>

                    <!-- Status Filter -->
                    <ComboBox materialDesign:HintAssist.Hint="فلترة حسب الحالة"
                             SelectedValue="{Binding StatusFilter}"
                             MinWidth="120" Width="150" Margin="{StaticResource SpacingXS}">
                        <ComboBoxItem Content="الكل"/>
                        <ComboBoxItem Content="معلق"/>
                        <ComboBoxItem Content="قيد التنفيذ"/>
                        <ComboBoxItem Content="مكتمل"/>
                        <ComboBoxItem Content="ملغي"/>
                    </ComboBox>

                    <!-- Priority Filter -->
                    <ComboBox materialDesign:HintAssist.Hint="فلترة حسب الأولوية"
                             SelectedValue="{Binding PriorityFilter}"
                             MinWidth="120" Width="150" Margin="0,0,10,5">
                        <ComboBoxItem Content="الكل"/>
                        <ComboBoxItem Content="منخفضة"/>
                        <ComboBoxItem Content="متوسطة"/>
                        <ComboBoxItem Content="عالية"/>
                        <ComboBoxItem Content="عاجلة"/>
                    </ComboBox>

                    <!-- Search Box -->
                    <TextBox materialDesign:HintAssist.Hint="البحث في المهام..."
                            Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                            MinWidth="150" Width="200" Margin="0,0,10,5">
                        <TextBox.Style>
                            <Style TargetType="TextBox" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
                                <Style.Triggers>
                                    <Trigger Property="Text" Value="">
                                        <Setter Property="materialDesign:HintAssist.Hint" Value="البحث في الوصف والملاحظات..."/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </TextBox.Style>
                    </TextBox>

                    <!-- Sort Options -->
                    <StackPanel Orientation="Horizontal" Margin="10,0,10,5">
                        <TextBlock Text="ترتيب حسب:" VerticalAlignment="Center" Margin="0,0,5,0" FontSize="12"/>
                        <ComboBox x:Name="SortComboBox"
                                 ItemsSource="{Binding SortOptionsDisplay}"
                                 SelectedIndex="{Binding SelectedSortIndex, Mode=TwoWay}"
                                 MinWidth="100" Width="120" Height="32" FontSize="12"
                                 Style="{StaticResource MaterialDesignOutlinedComboBox}"/>

                        <Button Command="{Binding ToggleSortDirectionCommand}"
                               Style="{StaticResource MaterialDesignIconButton}"
                               Width="32" Height="32" Margin="5,0,0,0"
                               ToolTip="{Binding SortDescending, Converter={StaticResource BoolToSortDirectionConverter}}">
                            <materialDesign:PackIcon Kind="{Binding SortDescending, Converter={StaticResource BoolToSortIconConverter}}"
                                                   Width="16" Height="16"/>
                        </Button>
                    </StackPanel>
                </WrapPanel>

                <!-- Spacer -->
                <Grid Grid.Column="1"/>

                <!-- Action Buttons -->
                <WrapPanel Grid.Column="2" Orientation="Horizontal" HorizontalAlignment="Right">
                    <!-- Add Task Button - الأهم أولاً -->
                    <Button Command="{Binding AddTaskCommand}"
                           Style="{StaticResource MaterialDesignRaisedButton}"
                           Margin="0,0,10,0"
                           Visibility="{Binding CanAddTasks, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <Button.Content>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Plus" Width="16" Height="16" Margin="0,0,5,0"/>
                                <TextBlock Text="إضافة مهمة"/>
                            </StackPanel>
                        </Button.Content>
                    </Button>

                    <Button Command="{Binding RefreshCommand}"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Margin="0,0,10,0">
                        <Button.Content>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Refresh" Width="16" Height="16" Margin="0,0,5,0"/>
                                <TextBlock Text="تحديث"/>
                            </StackPanel>
                        </Button.Content>
                    </Button>

                    <!-- More Actions Menu -->
                    <Button x:Name="MoreActionsButton"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Click="MoreActionsButton_Click">
                        <Button.Content>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="DotsVertical" Width="16" Height="16" Margin="0,0,5,0"/>
                                <TextBlock Text="المزيد"/>
                            </StackPanel>
                        </Button.Content>
                        <Button.ContextMenu>
                            <ContextMenu>
                                <MenuItem Header="استيراد مهام"
                                         Command="{Binding ImportTasksCommand}"
                                         Visibility="{Binding CanAddTasks, Converter={StaticResource BooleanToVisibilityConverter}}">
                                    <MenuItem.Icon>
                                        <materialDesign:PackIcon Kind="Import"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                                <Separator/>
                                <MenuItem Header="تصدير المهام المفلترة"
                                         Command="{Binding ExportTasksCommand}">
                                    <MenuItem.Icon>
                                        <materialDesign:PackIcon Kind="Export"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                                <MenuItem Header="تصدير جميع المهام"
                                         Command="{Binding ExportAllTasksCommand}"
                                         Visibility="{Binding AvailableNetworks.Count, Converter={StaticResource CountToVisibilityConverter}}">
                                    <MenuItem.Icon>
                                        <materialDesign:PackIcon Kind="DatabaseExport"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                            </ContextMenu>
                        </Button.ContextMenu>
                    </Button>
                </WrapPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Tasks List -->
        <materialDesign:Card Grid.Row="2">
            <Grid>
                <!-- Loading Indicator -->
                <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center"
                           Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <ProgressBar Width="50" Height="50"
                               Style="{StaticResource MaterialDesignCircularProgressBar}"
                               IsIndeterminate="True"
                               Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                    <TextBlock Text="جاري تحميل المهام..."
                              FontSize="14"
                              HorizontalAlignment="Center"
                              Margin="0,10,0,0"
                              Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                </StackPanel>

                <!-- Tasks Content -->
                <ScrollViewer VerticalScrollBarVisibility="Auto"
                             Visibility="{Binding IsLoading, Converter={StaticResource InverseBooleanToVisibilityConverter}}">
                    <StackPanel Margin="15">
                    <ItemsControl ItemsSource="{Binding FilteredTasks}">
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                    <materialDesign:Card Margin="0,0,0,10" Padding="15">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <!-- Task Header -->
                            <StackPanel Grid.Row="0" Grid.Column="0" Orientation="Horizontal" Margin="0,0,0,5">
                                <materialDesign:PackIcon Kind="ClipboardText" Width="20" Height="20"
                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                       VerticalAlignment="Center"/>
                                <TextBlock Text="{Binding Description}"
                                          FontSize="16" FontWeight="Medium"
                                          VerticalAlignment="Center" Margin="10,0,0,0"/>
                            </StackPanel>

                            <!-- Status Badge -->
                            <Border Grid.Row="0" Grid.Column="1"
                                   Background="{Binding StatusColor}" CornerRadius="12"
                                   Padding="8,4" VerticalAlignment="Center">
                                <TextBlock Text="{Binding StatusDisplay}" Foreground="White" FontSize="12" FontWeight="Medium"/>
                            </Border>

                            <!-- Task Description -->
                            <TextBlock Grid.Row="1" Grid.ColumnSpan="2"
                                      Text="{Binding Notes}"
                                      FontSize="14"
                                      Foreground="{DynamicResource MaterialDesignBodyLight}"
                                      TextWrapping="Wrap" Margin="30,5,0,10"/>

                            <!-- Task Footer -->
                            <Grid Grid.Row="2" Grid.ColumnSpan="2">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0" Orientation="Horizontal">
                                    <!-- تاريخ الطلب -->
                                    <materialDesign:PackIcon Kind="CalendarPlus" Width="16" Height="16"
                                                           Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                           VerticalAlignment="Center"/>
                                    <TextBlock Text="{Binding RequestDateDisplay, StringFormat='تاريخ الطلب: {0}'}"
                                              FontSize="12"
                                              Foreground="{DynamicResource MaterialDesignBodyLight}"
                                              VerticalAlignment="Center" Margin="5,0,15,0"/>

                                    <!-- تاريخ الإكمال (يظهر فقط للمهام المكتملة) -->
                                    <materialDesign:PackIcon Kind="CalendarCheck" Width="16" Height="16"
                                                           Foreground="#4CAF50"
                                                           VerticalAlignment="Center"
                                                           Visibility="{Binding IsCompleted, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                                    <TextBlock Text="{Binding CompletedAtDisplay, StringFormat='تاريخ الإكمال: {0}'}"
                                              FontSize="12"
                                              Foreground="#4CAF50"
                                              VerticalAlignment="Center" Margin="5,0,15,0"
                                              Visibility="{Binding IsCompleted, Converter={StaticResource BooleanToVisibilityConverter}}"/>

                                    <!-- الأولوية -->
                                    <materialDesign:PackIcon Kind="Flag" Width="16" Height="16"
                                                           Foreground="{Binding PriorityColor}"
                                                           VerticalAlignment="Center"/>
                                    <TextBlock Text="{Binding PriorityDisplay}"
                                              FontSize="12"
                                              Foreground="{Binding PriorityColor}"
                                              VerticalAlignment="Center" Margin="5,0,0,0"/>
                                </StackPanel>

                                <StackPanel Grid.Column="1" Orientation="Horizontal">
                                    <Button Content="تحرير"
                                           Command="{Binding DataContext.EditTaskCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                           CommandParameter="{Binding}"
                                           Style="{StaticResource MaterialDesignFlatButton}"
                                           Foreground="{DynamicResource PrimaryHueMidBrush}"
                                           Margin="0,0,5,0"
                                           Visibility="{Binding Converter={StaticResource CanEditTaskConverter}}"/>
                                    <Button Content="إكمال"
                                           Command="{Binding DataContext.CompleteTaskCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                           CommandParameter="{Binding}"
                                           Style="{StaticResource MaterialDesignFlatButton}"
                                           Foreground="#4CAF50"
                                           Visibility="{Binding CanComplete, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                                    <Button Content="حذف"
                                           Command="{Binding DataContext.DeleteTaskCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                           CommandParameter="{Binding}"
                                           Style="{StaticResource MaterialDesignFlatButton}"
                                           Foreground="Red"
                                           Margin="5,0,0,0"
                                           Visibility="{Binding Converter={StaticResource CanDeleteTaskConverter}}"/>
                                </StackPanel>
                            </Grid>
                        </Grid>
                    </materialDesign:Card>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>

                    <!-- Empty State (when no tasks) -->
                    <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center" Margin="0,50,0,0"
                               Visibility="{Binding FilteredTasks.Count, Converter={StaticResource ZeroToVisibilityConverter}}">
                        <materialDesign:PackIcon Kind="ClipboardList" Width="64" Height="64"
                                               Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                        <TextBlock Text="لا توجد مهام"
                                  FontSize="16"
                                  Foreground="{DynamicResource MaterialDesignBodyLight}"
                                  HorizontalAlignment="Center" Margin="0,10,0,0"/>
                        <TextBlock Text="انقر على 'إضافة مهمة' لإضافة المهمة الأولى"
                                  FontSize="12"
                                  Foreground="{DynamicResource MaterialDesignBodyLight}"
                                  HorizontalAlignment="Center"/>
                    </StackPanel>
                </StackPanel>
            </ScrollViewer>
            </Grid>
        </materialDesign:Card>

        <!-- Floating Action Button للشاشات الصغيرة -->
        <Button Grid.Row="2"
               Command="{Binding AddTaskCommand}"
               Style="{StaticResource MaterialDesignFloatingActionButton}"
               HorizontalAlignment="Left"
               VerticalAlignment="Bottom"
               Margin="20"
               Width="56" Height="56"
               ToolTip="إضافة مهمة جديدة"
               Visibility="{Binding CanAddTasks, Converter={StaticResource BooleanToVisibilityConverter}}">
            <materialDesign:PackIcon Kind="Plus" Width="24" Height="24"/>
        </Button>
    </Grid>
</UserControl>
