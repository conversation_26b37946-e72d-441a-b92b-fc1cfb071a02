using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using NetworkManagement.ViewModels;

namespace NetworkManagement.Views
{
    public partial class ReportsView : UserControl
    {
        public ReportsView()
        {
            InitializeComponent();
        }

        private void ReportCard_Click(object sender, MouseButtonEventArgs e)
        {
            if (sender is FrameworkElement element && element.Tag is string reportType && DataContext is ReportsViewModel viewModel)
            {
                viewModel.SelectedReportType = reportType;
                viewModel.GenerateReportCommand.Execute(null);
            }
        }


    }
}
