using System.Collections.Generic;
using System.Threading.Tasks;
using NetworkManagement.Models;
using NetworkManagement.ViewModels;

namespace NetworkManagement.Services
{
    public interface IReportExportService
    {
        // CSV Export Methods
        Task<string> ExportDevicesReportToCsvAsync(IEnumerable<Device> devices, string filePath);
        Task<string> ExportSitesReportToCsvAsync(IEnumerable<Site> sites, string filePath);
        Task<string> ExportUsersReportToCsvAsync(IEnumerable<User> users, string filePath);
        Task<string> ExportPurchasesReportToCsvAsync(IEnumerable<Purchase> purchases, string filePath);
        Task<string> ExportInventoryReportToCsvAsync(IEnumerable<Inventory> inventory, string filePath);
        Task<string> ExportTasksReportToCsvAsync(IEnumerable<Models.Task> tasks, string filePath);
        Task<string> ExportUserEvaluationsReportToCsvAsync(IEnumerable<UserEvaluation> evaluations, string filePath);
        Task<string> ExportNetworkStatisticsToCsvAsync(IEnumerable<NetworkStatistic> statistics, string filePath);
        Task<string> ExportMonthlyPurchasesToCsvAsync(IEnumerable<MonthlyPurchase> purchases, string filePath);
        Task<string> ExportDeviceStatusCountsToCsvAsync(IEnumerable<DeviceStatusCount> statusCounts, string filePath);
        Task<string> ExportGeneralReportToCsvAsync(ReportsViewModel viewModel, string filePath);

        // Excel Export Methods
        Task<string> ExportDevicesReportToExcelAsync(IEnumerable<Device> devices, string filePath);
        Task<string> ExportSitesReportToExcelAsync(IEnumerable<Site> sites, string filePath);
        Task<string> ExportUsersReportToExcelAsync(IEnumerable<User> users, string filePath);
        Task<string> ExportPurchasesReportToExcelAsync(IEnumerable<Purchase> purchases, string filePath);
        Task<string> ExportInventoryReportToExcelAsync(IEnumerable<Inventory> inventory, string filePath);
        Task<string> ExportTasksReportToExcelAsync(IEnumerable<Models.Task> tasks, string filePath);
        Task<string> ExportUserEvaluationsReportToExcelAsync(IEnumerable<UserEvaluation> evaluations, string filePath);
        Task<string> ExportCompleteReportToExcelAsync(ReportsViewModel viewModel, string filePath);

        // Import Methods
        Task<IEnumerable<Device>> ImportDevicesFromCsvAsync(string filePath);
        Task<IEnumerable<Device>> ImportDevicesFromExcelAsync(string filePath);
        Task<IEnumerable<Site>> ImportSitesFromCsvAsync(string filePath);
        Task<IEnumerable<Site>> ImportSitesFromExcelAsync(string filePath);
        Task<IEnumerable<User>> ImportUsersFromCsvAsync(string filePath);
        Task<IEnumerable<User>> ImportUsersFromExcelAsync(string filePath);
        Task<IEnumerable<Purchase>> ImportPurchasesFromCsvAsync(string filePath);
        Task<IEnumerable<Purchase>> ImportPurchasesFromExcelAsync(string filePath);
        Task<IEnumerable<Inventory>> ImportInventoryFromCsvAsync(string filePath);
        Task<IEnumerable<Inventory>> ImportInventoryFromExcelAsync(string filePath);

        // Import Tasks
        Task<IEnumerable<Models.Task>> ImportTasksFromCsvAsync(string filePath);
        Task<IEnumerable<Models.Task>> ImportTasksFromExcelAsync(string filePath);

        // Utility Methods
        Task<string> GetSaveFilePathAsync(string defaultFileName, string filter);
        Task<string> GetOpenFilePathAsync(string filter);
    }
}
