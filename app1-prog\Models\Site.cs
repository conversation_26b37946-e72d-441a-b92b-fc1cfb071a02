using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace NetworkManagement.Models
{
    public class Site
    {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        [MaxLength(200)]
        public string? Address { get; set; }

        [MaxLength(20)]
        public string? Phone { get; set; }

        public double? GpsLat { get; set; }

        public double? GpsLng { get; set; }

        [MaxLength(100)]
        public string? PowerSource { get; set; }

        // نظام البطاريات
        [MaxLength(50)]
        public string? BatteryType { get; set; }

        [MaxLength(50)]
        public string? BatterySize { get; set; }

        // نظام القواعد
        [MaxLength(50)]
        public string? BaseType { get; set; }

        public int? BaseCount { get; set; }

        // نظام الصناديق
        [MaxLength(50)]
        public string? BoxType { get; set; }

        public int? BoxCount { get; set; }

        // نظام أسلاك الشبكة
        [MaxLength(50)]
        public string? NetworkCableType { get; set; }

        public int? NetworkCableLength { get; set; }

        [MaxLength(50)]
        public string? PowerCableType { get; set; }

        public int? PowerCableLength { get; set; }

        // أنواع الأجهزة الموجودة في الموقع
        public string? DeviceTypes { get; set; } // JSON string

        // عناصر إضافية من المخزون
        public string? AdditionalInventoryItems { get; set; } // JSON string

        // Network relationship
        public string? NetworkId { get; set; }
        public virtual Network? Network { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual ICollection<Device> Devices { get; set; } = new List<Device>();

        // Display properties
        public string CoordinatesDisplay =>
            GpsLat.HasValue && GpsLng.HasValue
                ? $"{GpsLat:F6}, {GpsLng:F6}"
                : "غير محدد";

        public string BatteryDisplay =>
            !string.IsNullOrEmpty(BatteryType) && !string.IsNullOrEmpty(BatterySize)
                ? $"{BatteryType} - {BatterySize}"
                : "غير محدد";

        public string BaseDisplay =>
            !string.IsNullOrEmpty(BaseType) && BaseCount.HasValue
                ? $"{BaseType} - {BaseCount} قطعة"
                : "غير محدد";

        public string BoxDisplay =>
            !string.IsNullOrEmpty(BoxType) && BoxCount.HasValue
                ? $"{BoxType} - {BoxCount} قطعة"
                : "غير محدد";

        public string NetworkCableDisplay =>
            !string.IsNullOrEmpty(NetworkCableType) && NetworkCableLength.HasValue
                ? $"{NetworkCableType} - {NetworkCableLength} متر"
                : "غير محدد";

        public string PowerCableDisplay =>
            !string.IsNullOrEmpty(PowerCableType) && PowerCableLength.HasValue
                ? $"{PowerCableType} - {PowerCableLength} متر"
                : PowerCableLength.HasValue
                    ? $"{PowerCableLength} متر"
                    : "غير محدد";
    }

    // Helper class for additional inventory items
    public class AdditionalInventoryItem
    {
        public string InventoryId { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public int Quantity { get; set; } = 1;
    }
}
