<UserControl x:Class="NetworkManagement.Views.MyEvaluationView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:local="clr-namespace:NetworkManagement.Views"
             FlowDirection="RightToLeft">

    <UserControl.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <local:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter"/>
    </UserControl.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- <PERSON> Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,20">
            <TextBlock Text="تقييماتي" Style="{StaticResource PageHeaderStyle}"/>
            <TextBlock Text="{Binding WelcomeMessage}" 
                       Style="{StaticResource SubHeaderTextStyle}" 
                       Opacity="0.7" 
                       Margin="0,5,0,0"/>
        </StackPanel>

        <!-- Performance Summary -->
        <materialDesign:Card Grid.Row="1" Style="{StaticResource CardStyle}" Margin="0,0,0,15">
            <Grid Margin="20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Current Performance -->
                <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="TrendingUp" 
                                           Width="40" Height="40" 
                                           Foreground="{Binding PerformanceLevelColor}"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding CurrentAveragePercentage, StringFormat='{}{0:F1}%'}" 
                               FontSize="28" 
                               FontWeight="Bold" 
                               Foreground="{Binding PerformanceLevelColor}"
                               HorizontalAlignment="Center" 
                               Margin="0,5,0,0"/>
                    <TextBlock Text="النسبة الإجمالية" 
                               Style="{StaticResource CaptionTextStyle}" 
                               HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding PerformanceLevel}" 
                               FontWeight="Medium" 
                               Foreground="{Binding PerformanceLevelColor}"
                               HorizontalAlignment="Center" 
                               Margin="0,3,0,0"/>
                </StackPanel>

                <!-- Statistics -->
                <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="ChartLine" 
                                           Width="32" Height="32" 
                                           Foreground="{DynamicResource PrimaryHueMidBrush}"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding TotalEvaluationsText}" 
                               FontSize="14" 
                               HorizontalAlignment="Center" 
                               Margin="0,8,0,3"/>
                    <TextBlock Text="{Binding AverageScoreText}" 
                               FontSize="12" 
                               Opacity="0.7" 
                               HorizontalAlignment="Center" 
                               Margin="0,0,0,3"/>
                    <TextBlock Text="{Binding ScoreRangeText}" 
                               FontSize="12" 
                               Opacity="0.7" 
                               HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- Last Evaluation -->
                <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="Calendar" 
                                           Width="32" Height="32" 
                                           Foreground="{DynamicResource SecondaryHueMidBrush}"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding LastEvaluationText}" 
                               FontSize="14" 
                               HorizontalAlignment="Center" 
                               Margin="0,8,0,3"/>
                    <TextBlock Text="{Binding SelectedPeriod}" 
                               FontSize="12" 
                               Opacity="0.7" 
                               HorizontalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Motivational Message -->
        <materialDesign:Card Grid.Row="2" Style="{StaticResource CardStyle}" Margin="0,0,0,15"
                           Background="{Binding PerformanceLevelColor}"
                           Foreground="White">
            <StackPanel Margin="15" Orientation="Horizontal" HorizontalAlignment="Center">
                <materialDesign:PackIcon Kind="MessageText" 
                                       Width="24" Height="24" 
                                       VerticalAlignment="Center" 
                                       Margin="0,0,10,0"/>
                <TextBlock Text="{Binding MotivationalMessage}" 
                           FontSize="14" 
                           FontWeight="Medium" 
                           VerticalAlignment="Center"/>
            </StackPanel>
        </materialDesign:Card>

        <!-- Controls and Evaluations List -->
        <Grid Grid.Row="3">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Filter Controls -->
            <materialDesign:Card Grid.Row="0" Style="{StaticResource CardStyle}" Margin="0,0,0,10">
                <StackPanel Orientation="Horizontal" Margin="15">
                    <ComboBox ItemsSource="{Binding PeriodOptions}"
                              SelectedItem="{Binding SelectedPeriod}"
                              materialDesign:HintAssist.Hint="اختر الفترة"
                              Width="150" Margin="0,0,10,0"/>

                    <DatePicker SelectedDate="{Binding StartDate}"
                                materialDesign:HintAssist.Hint="من تاريخ"
                                Width="120" Margin="0,0,10,0"/>

                    <DatePicker SelectedDate="{Binding EndDate}"
                                materialDesign:HintAssist.Hint="إلى تاريخ"
                                Width="120" Margin="0,0,10,0"/>

                    <Button Command="{Binding ChangePeriodCommand}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Height="32" Margin="0,0,10,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Update" Width="14" Height="14" Margin="2"/>
                            <TextBlock Text="تطبيق" FontSize="12"/>
                        </StackPanel>
                    </Button>

                    <Button Command="{Binding RefreshDataCommand}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Height="32">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Refresh" Width="14" Height="14" Margin="2"/>
                            <TextBlock Text="تحديث" FontSize="12"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </materialDesign:Card>

            <!-- Evaluations List -->
            <materialDesign:Card Grid.Row="1" Style="{StaticResource CardStyle}">
                <Grid>
                    <!-- Loading Indicator -->
                    <ProgressBar IsIndeterminate="True"
                                Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                                VerticalAlignment="Top" Height="4" Panel.ZIndex="1"/>

                    <!-- No Data Message -->
                    <StackPanel HorizontalAlignment="Center" 
                               VerticalAlignment="Center"
                               Visibility="{Binding HasEvaluations, Converter={StaticResource InverseBooleanToVisibilityConverter}}">
                        <materialDesign:PackIcon Kind="EmoticonSad" 
                                               Width="64" Height="64" 
                                               Foreground="{DynamicResource MaterialDesignBodyLight}"
                                               HorizontalAlignment="Center"/>
                        <TextBlock Text="{Binding NoEvaluationsMessage}" 
                                   Style="{StaticResource SubHeaderTextStyle}" 
                                   Opacity="0.6" 
                                   HorizontalAlignment="Center" 
                                   Margin="0,10,0,0"/>
                        <TextBlock Text="جرب تغيير الفترة الزمنية أو تحديث البيانات" 
                                   Style="{StaticResource CaptionTextStyle}" 
                                   Opacity="0.5" 
                                   HorizontalAlignment="Center" 
                                   Margin="0,5,0,0"/>
                    </StackPanel>

                    <!-- Data Grid -->
                    <DataGrid ItemsSource="{Binding MyEvaluations}"
                             AutoGenerateColumns="False"
                             AlternatingRowBackground="Transparent"
                             GridLinesVisibility="Horizontal"
                             HeadersVisibility="Column"
                             CanUserAddRows="False"
                             CanUserDeleteRows="False"
                             CanUserSortColumns="True"
                             CanUserReorderColumns="False"
                             CanUserResizeColumns="True"
                             IsReadOnly="True"
                             SelectionMode="Single"
                             SelectionUnit="FullRow"
                             EnableRowVirtualization="True"
                             EnableColumnVirtualization="True"
                             VirtualizingPanel.VirtualizationMode="Recycling"
                             VirtualizingPanel.IsVirtualizing="True"
                             ScrollViewer.CanContentScroll="True"
                             RowHeight="50"
                             ColumnHeaderHeight="40"
                             Visibility="{Binding HasEvaluations, Converter={StaticResource BooleanToVisibilityConverter}}">

                        <DataGrid.Columns>
                            <DataGridTextColumn Header="التاريخ" Binding="{Binding EvaluationDateDisplay}" Width="120"/>
                            <DataGridTextColumn Header="النقاط" Binding="{Binding ScoreDisplay}" Width="100">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="FontSize" Value="14"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="النسبة المئوية" Binding="{Binding ScorePercentageDisplay}" Width="120">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="FontSize" Value="14"/>
                                        <Setter Property="Foreground" Value="{DynamicResource PrimaryHueMidBrush}"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="التقدير" Binding="{Binding ScoreDescription}" Width="120"/>
                            <DataGridTextColumn Header="المقيم" Binding="{Binding EvaluatedByName}" Width="150"/>
                            <DataGridTextColumn Header="الملاحظات" Binding="{Binding NotesDisplay}" Width="*"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </materialDesign:Card>
        </Grid>
    </Grid>
</UserControl>
