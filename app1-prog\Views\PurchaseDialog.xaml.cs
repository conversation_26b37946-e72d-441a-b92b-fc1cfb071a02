using System.Windows;
using NetworkManagement.ViewModels;

namespace NetworkManagement.Views
{
    public partial class PurchaseDialog : Window
    {
        public PurchaseDialog()
        {
            InitializeComponent();
        }

        public PurchaseDialog(PurchaseDialogViewModel viewModel) : this()
        {
            DataContext = viewModel;
            
            // Subscribe to events
            viewModel.DialogClosed += (s, e) => Close();
        }
    }
}
