using System;
using System.Threading.Tasks;
using System.Linq;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using NetworkManagement.Services;
using NetworkManagement.Views;
using NetworkManagement.Models;
using NetworkManagement.Helpers;
using System.Windows;
using System.Collections.ObjectModel;

namespace NetworkManagement.ViewModels
{
    public partial class MainViewModel : ObservableObject
    {
        private readonly IAuthService _authService;
        private readonly INotificationService _notificationService;
        private readonly INetworkService _networkService;
        private readonly ISyncService _syncService;

        [ObservableProperty]
        private string currentUserName = string.Empty;

        [ObservableProperty]
        private string currentUserRole = string.Empty;

        private object? _currentView;
        public object? CurrentView
        {
            get => _currentView;
            set
            {
                // تنظيف الـ View السابق إذا كان يدعم IDisposable
                if (_currentView is IDisposable disposableView)
                {
                    try
                    {
                        disposableView.Dispose();
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"خطأ في تنظيف View السابق: {ex.Message}");
                    }
                }

                SetProperty(ref _currentView, value);
            }
        }

        [ObservableProperty]
        private bool hasUnreadNotifications = false;

        [ObservableProperty]
        private int unreadNotificationsCount = 0;

        [ObservableProperty]
        private bool isNotificationsPanelOpen = false;

        [ObservableProperty]
        private bool hasNotifications = false;

        [ObservableProperty]
        private bool isSyncing = false;

        [ObservableProperty]
        private string lastSyncTime = "لم يتم المزامنة بعد";

        [ObservableProperty]
        private string syncStatus = "متصل";

        // خصائص الصلاحيات
        [ObservableProperty]
        private bool canManageUsers = false;

        [ObservableProperty]
        private bool canManageNetworks = false;

        [ObservableProperty]
        private bool canViewAllNetworks = false;

        [ObservableProperty]
        private bool canManagePurchases = false;

        [ObservableProperty]
        private bool canViewSettings = false;

        [ObservableProperty]
        private bool canViewReports = false;

        [ObservableProperty]
        private bool canEditDatabaseSettings = false;

        [ObservableProperty]
        private bool canManageEvaluations = false;

        // خصائص إدارة الشبكات للأدمن
        [ObservableProperty]
        private ObservableCollection<Network> availableNetworks = new();

        [ObservableProperty]
        private Network? selectedNetwork;

        [ObservableProperty]
        private bool isNetworkDropdownOpen = false;

        public System.Collections.ObjectModel.ObservableCollection<NotificationItem> Notifications =>
            ((NotificationService)_notificationService).Notifications;

        public MainViewModel(IAuthService authService, INotificationService notificationService, INetworkService networkService, ISyncService syncService)
        {
            _authService = authService;
            _notificationService = notificationService;
            _networkService = networkService;
            _syncService = syncService;

            // تهيئة MessageHelper و ErrorHandler مع خدمات الإشعارات
            MessageHelper.Initialize(notificationService, authService);
            ErrorHandler.Initialize(notificationService, authService);

            // الاستماع لتغييرات المستخدم
            _authService.UserChanged += OnUserChanged;

            // تحديث بيانات المستخدم الحالي
            UpdateCurrentUser();

            // Initialize notifications
            InitializeNotifications();

            // تحميل الشبكات للأدمن
            _ = LoadNetworksAsync();

            // Initialize sync service
            InitializeSyncService();

            // Set default view to Dashboard
            ShowDashboard();
        }

        private void OnUserChanged(object? sender, User? user)
        {
            UpdateCurrentUser();
        }

        private void UpdateCurrentUser()
        {
            if (_authService.CurrentUser != null)
            {
                CurrentUserName = _authService.CurrentUser.Name;
                CurrentUserRole = _authService.CurrentUser.Role;

                System.Diagnostics.Debug.WriteLine($"MainViewModel: المستخدم الحالي: {CurrentUserName}, الدور: {CurrentUserRole}");

                // تحديد الصلاحيات حسب الدور
                UpdatePermissions();
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("MainViewModel: لا يوجد مستخدم حالي!");
                // إعادة تعيين الصلاحيات
                ResetPermissions();
            }
        }

        private void ResetPermissions()
        {
            CanManageUsers = false;
            CanManageNetworks = false;
            CanViewAllNetworks = false;
            CanManagePurchases = false;
            CanViewSettings = false;
            CanViewReports = false;
            CanEditDatabaseSettings = false;
            CanManageEvaluations = false;
        }

        [RelayCommand]
        private void ShowDashboard()
        {
            var view = new DashboardView
            {
                DataContext = App.GetService<DashboardViewModel>()
            };
            CurrentView = view;
        }

        [RelayCommand]
        private void ShowDevices()
        {
            try
            {
                // للأدمن: إظهار قائمة الشبكات
                if (_authService.IsSuperAdmin)
                {
                    IsNetworkDropdownOpen = !IsNetworkDropdownOpen;
                }
                else
                {
                    // للمستخدمين العاديين: عرض الأجهزة مباشرة
                    ShowDevicesForNetwork(null);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل صفحة الأجهزة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        [RelayCommand]
        private void ShowDevicesForNetwork(Network? network)
        {
            try
            {
                SelectedNetwork = network;
                IsNetworkDropdownOpen = false;

                // إنشاء DevicesView جديد
                var devicesView = new DevicesView();

                // سيتم تعيين ViewModel في DevicesView_Loaded
                // ولكن نحتاج لتمرير معرف الشبكة بطريقة أخرى

                // حفظ معرف الشبكة المحدد في خاصية عامة
                if (network != null && !string.IsNullOrEmpty(network.Id))
                {
                    // تمرير معرف الشبكة عبر Tag
                    devicesView.Tag = network.Id;
                }

                CurrentView = devicesView;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل أجهزة الشبكة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        [RelayCommand]
        private void ShowSites()
        {
            CurrentView = new SitesView
            {
                DataContext = App.GetService<SitesViewModel>()
            };
        }

        [RelayCommand]
        private void ShowUsers()
        {
            if (!CanManageUsers)
            {
                MessageBox.Show("ليس لديك صلاحية للوصول إلى إدارة المستخدمين", "غير مسموح",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                CurrentView = new UsersView
                {
                    DataContext = App.GetService<UsersViewModel>()
                };
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل صفحة المستخدمين: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        [RelayCommand]
        private void ShowTasks()
        {
            // السماح لـ TasksView بإدارة الـ ViewModel الخاص به
            CurrentView = new TasksView();
        }

        [RelayCommand]
        private void ShowUserEvaluations()
        {
            if (!CanManageEvaluations)
            {
                MessageBox.Show("ليس لديك صلاحية للوصول إلى إدارة التقييمات", "غير مسموح",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                CurrentView = new Views.UserEvaluationView
                {
                    DataContext = App.GetService<UserEvaluationViewModel>()
                };
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل صفحة التقييمات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        [RelayCommand]
        private void ShowMyEvaluations()
        {
            try
            {
                CurrentView = new Views.MyEvaluationView
                {
                    DataContext = App.GetService<MyEvaluationViewModel>()
                };
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل صفحة تقييماتي: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        [RelayCommand]
        private void ShowPurchases()
        {
            if (!CanManagePurchases)
            {
                MessageBox.Show("ليس لديك صلاحية للوصول إلى إدارة المشتريات", "غير مسموح",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            CurrentView = new PurchasesView
            {
                DataContext = App.GetService<PurchasesViewModel>()
            };
        }

        [RelayCommand]
        private void ShowInventory()
        {
            CurrentView = new InventoryView
            {
                DataContext = App.GetService<InventoryViewModel>()
            };
        }

        [RelayCommand]
        private void ShowReports()
        {
            if (!CanViewReports)
            {
                MessageBox.Show("ليس لديك صلاحية للوصول إلى التقارير", "غير مسموح",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                CurrentView = new ReportsView
                {
                    DataContext = App.GetService<ReportsViewModel>()
                };
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل صفحة التقارير: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        [RelayCommand]
        private void ShowSettings()
        {
            // التحقق من صلاحية الوصول للإعدادات
            if (!CanViewSettings)
            {
                MessageBox.Show("ليس لديك صلاحية للوصول إلى الإعدادات", "غير مسموح",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                CurrentView = new SettingsView
                {
                    DataContext = App.GetService<SettingsViewModel>()
                };
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل صفحة الإعدادات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        [RelayCommand]
        private void ShowDatabaseSettings()
        {
            // التحقق من صلاحية إعدادات قاعدة البيانات
            if (!CanEditDatabaseSettings)
            {
                MessageBox.Show("ليس لديك صلاحية للوصول إلى إعدادات قاعدة البيانات", "غير مسموح",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                var viewModel = App.GetService<DatabaseSetupViewModel>();
                var databaseSetupWindow = new DatabaseSetupWindow
                {
                    DataContext = viewModel
                };

                // ربط الأحداث
                viewModel.DatabaseSetupCompleted += () =>
                {
                    databaseSetupWindow.Close();
                    // إعادة تحميل الإعدادات في الواجهة الرئيسية إذا لزم الأمر
                    OnPropertyChanged(nameof(CanEditDatabaseSettings));
                };

                viewModel.ExitRequested += () =>
                {
                    databaseSetupWindow.Close();
                };

                databaseSetupWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل نافذة إعدادات قاعدة البيانات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task ShowNetworkManagementAsync()
        {
            // التحقق من صلاحية إدارة الشبكات
            if (!CanManageNetworks)
            {
                MessageBox.Show("ليس لديك صلاحية للوصول إلى إدارة الشبكات", "غير مسموح",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                var view = new NetworkManagementView();
                var viewModel = App.GetService<NetworkManagementViewModel>();

                // Initialize the ViewModel
                await viewModel.InitializeAsync();

                view.DataContext = viewModel;
                CurrentView = view;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل صفحة إدارة الشبكات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task LogoutAsync()
        {
            var result = MessageBox.Show("هل تريد تسجيل الخروج؟", "تأكيد",
                MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                await _authService.LogoutAsync();

                var loginWindow = new LoginWindow();
                loginWindow.Show();

                Application.Current.Windows.OfType<MainWindow>().FirstOrDefault()?.Close();
            }
        }

        [RelayCommand]
        private void ToggleNotifications()
        {
            IsNotificationsPanelOpen = !IsNotificationsPanelOpen;
            if (IsNotificationsPanelOpen)
            {
                // Mark notifications as read when panel is opened
                HasUnreadNotifications = false;
                UnreadNotificationsCount = 0;
            }
        }

        private void InitializeNotifications()
        {
            // Show welcome notification only
            _ = _notificationService.ShowSuccessAsync("مرحباً", $"أهلاً بك {CurrentUserName}");

            // Update notification counts
            UpdateNotificationCounts();
        }

        private void UpdateNotificationCounts()
        {
            // Update counts based on actual notifications
            UnreadNotificationsCount = Notifications.Count;
            HasUnreadNotifications = UnreadNotificationsCount > 0;
            HasNotifications = Notifications.Count > 0;
        }

        [RelayCommand]
        private void ClearAllNotifications()
        {
            _notificationService.ClearAllNotifications();
            UpdateNotificationCounts();
        }

        [RelayCommand]
        private void ViewAllNotifications()
        {
            // Close the popup
            IsNotificationsPanelOpen = false;

            // Show detailed notifications summary
            var notificationsSummary = $"ملخص الإشعارات:\n\n";
            notificationsSummary += $"إجمالي الإشعارات: {Notifications.Count}\n";
            notificationsSummary += $"الإشعارات غير المقروءة: {UnreadNotificationsCount}\n\n";

            if (Notifications.Any())
            {
                notificationsSummary += "آخر الإشعارات:\n";
                foreach (var notification in Notifications.Take(5))
                {
                    notificationsSummary += $"• {notification.Title}: {notification.Message}\n";
                }

                if (Notifications.Count > 5)
                {
                    notificationsSummary += $"... و {Notifications.Count - 5} إشعارات أخرى";
                }
            }
            else
            {
                notificationsSummary += "لا توجد إشعارات حالياً.";
            }

            MessageBox.Show(
                notificationsSummary,
                "جميع الإشعارات",
                MessageBoxButton.OK,
                MessageBoxImage.Information);
        }

        private void UpdatePermissions()
        {
            if (_authService.CurrentUser == null) return;

            // استخدام AuthService للحصول على الصلاحيات
            CanManageUsers = _authService.CanManageUsers;
            CanManageNetworks = _authService.CanManageNetworks;
            CanViewAllNetworks = _authService.CanViewAllNetworks;
            CanManagePurchases = _authService.CanManagePurchases;
            CanViewSettings = _authService.CanViewSettings;
            CanViewReports = _authService.CanViewReports;
            CanEditDatabaseSettings = _authService.IsSuperAdmin; // فقط الأدمن العام يمكنه الوصول لإعدادات قاعدة البيانات
            CanManageEvaluations = _authService.IsSuperAdmin || _authService.IsNetworkManager; // الأدمن ومدير الشبكة يمكنهم إدارة التقييمات
        }

        private async System.Threading.Tasks.Task LoadNetworksAsync()
        {
            try
            {
                // تحميل الشبكات للأدمن فقط
                if (_authService.IsSuperAdmin)
                {
                    var networks = await _networkService.GetAllAsync();

                    // تحديث القائمة على UI Thread
                    await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        AvailableNetworks.Clear();

                        // إضافة خيار "جميع الشبكات"
                        AvailableNetworks.Add(new Network { Id = "", Name = "جميع الشبكات" });

                        foreach (var network in networks)
                        {
                            AvailableNetworks.Add(network);
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الشبكات: {ex.Message}");
            }
        }

        /// <summary>
        /// تهيئة خدمة المزامنة التلقائية
        /// </summary>
        private void InitializeSyncService()
        {
            try
            {
                // الاستماع لأحداث المزامنة
                _syncService.SyncStatusChanged += OnSyncStatusChanged;
                _syncService.DataUpdated += OnDataUpdated;

                // بدء المزامنة التلقائية
                _ = System.Threading.Tasks.Task.Run(async () => await _syncService.StartSyncAsync());

                // تحديث حالة المزامنة الأولية
                UpdateSyncStatus();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تهيئة خدمة المزامنة: {ex.Message}");
            }
        }

        /// <summary>
        /// معالج تغيير حالة المزامنة
        /// </summary>
        private void OnSyncStatusChanged(object? sender, bool isSyncing)
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                IsSyncing = isSyncing;
                SyncStatus = isSyncing ? "جاري المزامنة..." : "متصل";
                UpdateSyncStatus();
            });
        }

        /// <summary>
        /// معالج تحديث البيانات
        /// </summary>
        private void OnDataUpdated(object? sender, SyncEventArgs e)
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                // إشعار بالتحديث
                var message = $"تم تحديث {GetTableDisplayName(e.TableName)}";
                if (e.AffectedRows > 0)
                {
                    message += $" ({e.AffectedRows} عنصر)";
                }

                _ = _notificationService.ShowInfoAsync("تحديث البيانات", message);

                // تحديث الواجهة إذا كانت مفتوحة
                RefreshCurrentView();
            });
        }

        /// <summary>
        /// تحديث حالة المزامنة
        /// </summary>
        private void UpdateSyncStatus()
        {
            var lastSync = _syncService.LastSyncTime;
            var timeDiff = DateTime.Now - lastSync;

            LastSyncTime = timeDiff.TotalMinutes < 1
                ? "منذ لحظات"
                : timeDiff.TotalMinutes < 60
                    ? $"منذ {(int)timeDiff.TotalMinutes} دقيقة"
                    : $"منذ {(int)timeDiff.TotalHours} ساعة";
        }

        /// <summary>
        /// الحصول على اسم الجدول للعرض
        /// </summary>
        private string GetTableDisplayName(string tableName)
        {
            return tableName switch
            {
                "Devices" => "الأجهزة",
                "Sites" => "المواقع",
                "Users" => "المستخدمين",
                "Tasks" => "المهام",
                "Purchases" => "المشتريات",
                "Inventory" => "المخزون",
                "Networks" => "الشبكات",
                _ => tableName
            };
        }

        /// <summary>
        /// تحديث الواجهة الحالية
        /// </summary>
        private void RefreshCurrentView()
        {
            try
            {
                // إعادة تحميل البيانات في الواجهة الحالية
                if (CurrentView is DashboardView)
                {
                    ShowDashboard();
                }
                // يمكن إضافة المزيد من الواجهات حسب الحاجة
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث الواجهة: {ex.Message}");
            }
        }

        /// <summary>
        /// فرض مزامنة فورية
        /// </summary>
        [RelayCommand]
        private async System.Threading.Tasks.Task ForceSyncAsync()
        {
            try
            {
                await _syncService.ForceSyncAsync();
                await _notificationService.ShowSuccessAsync("المزامنة", "تم تحديث البيانات بنجاح");
            }
            catch (Exception ex)
            {
                await _notificationService.ShowErrorAsync("خطأ في المزامنة", ex.Message);
            }
        }
    }
}

