using System.Windows;
using NetworkManagement.ViewModels;

namespace NetworkManagement.Views
{
    public partial class InventoryDialog : Window
    {
        public InventoryDialog()
        {
            InitializeComponent();
        }

        public InventoryDialog(InventoryDialogViewModel viewModel) : this()
        {
            DataContext = viewModel;
            
            // Subscribe to events
            viewModel.DialogClosed += (s, e) => Close();
        }
    }
}
