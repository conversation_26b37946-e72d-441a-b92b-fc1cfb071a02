using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using NetworkManagement.Views;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.DependencyInjection;
using NetworkManagement.Models;
using NetworkManagement.Services;
using NetworkManagement.Helpers;

namespace NetworkManagement.ViewModels
{
    public partial class InventoryViewModel : ObservableObject
    {
        private readonly IAuthService _authService;
        private readonly IServiceProvider _serviceProvider;
        private readonly IReportExportService _exportService;
        private readonly INotificationService _notificationService;

        [ObservableProperty]
        private ObservableCollection<Inventory> inventoryItems = new();

        [ObservableProperty]
        private ObservableCollection<Inventory> lowStockItems = new();

        [ObservableProperty]
        private Inventory? selectedItem;

        [ObservableProperty]
        private string searchText = string.Empty;

        [ObservableProperty]
        private string selectedCategory = "الكل";

        [ObservableProperty]
        private bool isLoading = false;

        // خصائص فلتر الشبكة
        [ObservableProperty]
        private ObservableCollection<Network> availableNetworks = new();

        [ObservableProperty]
        private Network? selectedNetwork;

        [ObservableProperty]
        private string networkFilter = "الكل";

        [ObservableProperty]
        private string errorMessage = string.Empty;

        [ObservableProperty]
        private bool hasError = false;

        // Statistics
        [ObservableProperty]
        private int totalItems;

        [ObservableProperty]
        private int lowStockCount;

        [ObservableProperty]
        private decimal totalValue;

        [ObservableProperty]
        private int totalQuantity;

        // Display properties for currency formatting
        public string TotalValueDisplay => $"{TotalValue:N0} ر.ي";

        partial void OnTotalValueChanged(decimal value)
        {
            OnPropertyChanged(nameof(TotalValueDisplay));
        }

        public string[] Categories { get; } = { "الكل", "أجهزة", "كابلات", "أدوات", "قطع غيار", "مواد استهلاكية", "أخرى" };

        // Permission properties
        public bool CanAddItems => _authService.CanAddData();
        public bool CanEditItems => _authService.CanEditData();
        public bool CanDeleteItems => _authService.CanDeleteData();
        public bool CanManageInventory => _authService.CanManageInventory;
        public bool CanExportInventory => !IsLoading;
        public bool CanImportInventory => !IsLoading && CanAddItems;
        public bool CanViewAllNetworks => _authService?.CanViewAllNetworks ?? false;

        public InventoryViewModel(IAuthService authService, IServiceProvider serviceProvider, INotificationService notificationService)
        {
            _authService = authService;
            _serviceProvider = serviceProvider;
            _exportService = serviceProvider.GetRequiredService<IReportExportService>()!;
            _notificationService = notificationService;

            // Subscribe to user changes to update permissions
            _authService.UserChanged += OnUserChanged;

            _ = LoadNetworksAsync(); // تحميل الشبكات أولاً
            _ = LoadInventoryAsync(); // Fire and forget - safe for initial load
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task LoadInventoryAsync()
        {
            try
            {
                IsLoading = true;
                HasError = false;
                ErrorMessage = string.Empty;

                // إنشاء scope جديد للحصول على InventoryService
                using var scope = _serviceProvider.CreateScope();
                var inventoryService = scope.ServiceProvider.GetRequiredService<IInventoryService>();

                // الحصول على فلتر الشبكة الفعال
                var networkFilter = GetEffectiveNetworkFilter();
                var inventoryList = await inventoryService.GetAllAsync(networkFilter);

                // تطبيق فلترة الصلاحيات باستخدام PermissionHelper
                inventoryList = PermissionHelper.ApplyPermissionFilter(inventoryList, _authService, item => item.NetworkId);

                // Apply filters using helper method
                inventoryList = ApplyFilters(inventoryList);

                InventoryItems.Clear();
                foreach (var item in inventoryList.OrderBy(i => i.Name))
                {
                    InventoryItems.Add(item);
                }

                // Load low stock items with permissions filter
                var lowStock = await inventoryService.GetLowStockItemsAsync(networkFilter);
                var filteredLowStock = PermissionHelper.ApplyPermissionFilter(lowStock, _authService, item => item.NetworkId);
                LowStockItems.Clear();
                foreach (var item in filteredLowStock)
                {
                    LowStockItems.Add(item);
                }

                await LoadStatisticsAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading inventory: {ex.Message}");
                ErrorMessage = "حدث خطأ أثناء تحميل بيانات المخزون";
                HasError = true;
                MessageHelper.HandleException(ex, "تحميل بيانات المخزون");
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task SearchInventoryAsync()
        {
            await LoadInventoryAsync();
        }

        [RelayCommand]
        private void AddItem()
        {
            // التحقق من صلاحية إضافة عناصر المخزون
            if (!CanAddItems)
            {
                PermissionHelper.ShowPermissionDeniedMessage("إضافة", "عناصر مخزون جديدة");
                return;
            }

            var dialogViewModel = App.GetService<InventoryDialogViewModel>();
            var dialog = new InventoryDialog(dialogViewModel);

            dialogViewModel.InventorySaved += async (s, inventory) =>
            {
                await LoadInventoryAsync();
            };

            dialog.ShowDialog();
        }

        [RelayCommand]
        private void EditItem()
        {
            if (SelectedItem != null)
            {
                // التحقق من صلاحية تعديل عنصر المخزون
                if (!PermissionHelper.CanEditItem(_authService, SelectedItem.NetworkId))
                {
                    PermissionHelper.ShowPermissionDeniedMessage("تعديل", "هذا العنصر");
                    return;
                }

                var dialogViewModel = App.GetService<InventoryDialogViewModel>();
                dialogViewModel.SetEditInventory(SelectedItem);
                var dialog = new InventoryDialog(dialogViewModel);

                dialogViewModel.InventorySaved += async (s, inventory) =>
                {
                    await LoadInventoryAsync();
                };

                dialog.ShowDialog();
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task DeleteItemAsync()
        {
            if (SelectedItem != null)
            {
                // التحقق من صلاحية حذف عنصر المخزون
                if (!PermissionHelper.CanDeleteItem(_authService, SelectedItem.NetworkId))
                {
                    PermissionHelper.ShowPermissionDeniedMessage("حذف", "هذا العنصر");
                    return;
                }

                var itemInfo = $"{SelectedItem.Name} - الكمية: {SelectedItem.Quantity}";
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف العنصر:\n{itemInfo}؟\n\nلا يمكن التراجع عن هذا الإجراء.",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        // إنشاء scope جديد للحصول على InventoryService
                        using var scope = _serviceProvider.CreateScope();
                        var inventoryService = scope.ServiceProvider.GetRequiredService<IInventoryService>();

                        await inventoryService.DeleteAsync(SelectedItem.Id);
                        await LoadInventoryAsync();

                        // إشعار حذف عنصر المخزون
                        var userName = _authService.CurrentUser?.Name ?? "المستخدم";
                        var userId = _authService.CurrentUser?.Id;
                        await _notificationService.ShowOperationAsync(
                            "تم حذف عنصر المخزون",
                            $"قام {userName} بحذف العنصر: {SelectedItem.Name}",
                            NotificationType.Success,
                            userName, userId, "حذف", SelectedItem.Name, SelectedItem.NetworkId);
                    }
                    catch (Exception ex)
                    {
                        await ErrorHandler.HandleExceptionWithNotificationAsync(ex, "حذف عنصر المخزون", "InventoryViewModel", SelectedItem.NetworkId);
                    }
                }
            }
        }

        private IEnumerable<Inventory> ApplyFilters(IEnumerable<Inventory> items)
        {
            if (!string.IsNullOrEmpty(SearchText))
            {
                items = items.Where(i =>
                    i.Name.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                    (i.Description?.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ?? false) ||
                    i.Category.Contains(SearchText, StringComparison.OrdinalIgnoreCase));
            }

            if (SelectedCategory != "الكل")
            {
                items = items.Where(i => i.Category == SelectedCategory);
            }

            return items;
        }

        private System.Threading.Tasks.Task LoadStatisticsAsync()
        {
            try
            {
                // Calculate statistics from current filtered items
                TotalItems = InventoryItems.Count;
                LowStockCount = LowStockItems.Count;
                TotalQuantity = InventoryItems.Sum(i => i.Quantity);

                // Calculate total value (quantity * unit price)
                TotalValue = InventoryItems
                    .Where(i => i.UnitPrice.HasValue)
                    .Sum(i => i.Quantity * (i.UnitPrice ?? 0));
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading statistics: {ex.Message}");
            }

            return System.Threading.Tasks.Task.CompletedTask;
        }

        partial void OnSelectedCategoryChanged(string value)
        {
            _ = LoadInventoryAsync();
        }

        partial void OnIsLoadingChanged(bool value)
        {
            OnPropertyChanged(nameof(CanExportInventory));
            OnPropertyChanged(nameof(CanImportInventory));
            ExportInventoryCommand?.NotifyCanExecuteChanged();
            ImportInventoryCommand?.NotifyCanExecuteChanged();
        }

        partial void OnHasErrorChanged(bool value)
        {
            if (!value)
            {
                ErrorMessage = string.Empty;
            }
        }

        [RelayCommand(CanExecute = nameof(CanExportInventory))]
        private async System.Threading.Tasks.Task ExportInventoryAsync()
        {
            try
            {
                IsLoading = true;
                var filter = "Excel Files (*.xlsx)|*.xlsx|CSV Files (*.csv)|*.csv";

                // تحسين اسم الملف ليتضمن معلومات الفلترة
                var networkName = SelectedNetwork?.Name ?? "الكل";
                var categoryName = SelectedCategory != "الكل" ? SelectedCategory : "الكل";
                var defaultFileName = $"المخزون_{networkName}_{categoryName}_{DateTime.Now:yyyy-MM-dd}";

                var filePath = await _exportService.GetSaveFilePathAsync(defaultFileName, filter);
                if (string.IsNullOrEmpty(filePath))
                    return;

                string result = "";
                var isExcel = filePath.EndsWith(".xlsx", StringComparison.OrdinalIgnoreCase);

                // تصدير عناصر المخزون المعروضة فقط (المفلترة)
                var exportList = InventoryItems.ToList();
                result = isExcel
                    ? await _exportService.ExportInventoryReportToExcelAsync(exportList, filePath)
                    : await _exportService.ExportInventoryReportToCsvAsync(exportList, filePath);

                if (!string.IsNullOrEmpty(result))
                {
                    var filterInfo = networkName != "الكل" ? $" من شبكة {networkName}" : "";
                    if (categoryName != "الكل") filterInfo += $" فئة {categoryName}";

                    MessageBox.Show(
                        $"تم تصدير {exportList.Count} عنصر{filterInfo} بنجاح إلى:\n{result}",
                        "تم التصدير",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"حدث خطأ أثناء تصدير المخزون:\n{ex.Message}",
                    "خطأ في التصدير",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand(CanExecute = nameof(CanImportInventory))]
        private async System.Threading.Tasks.Task ImportInventoryAsync()
        {
            if (!CanAddItems)
            {
                PermissionHelper.ShowPermissionDeniedMessage("استيراد", "عناصر مخزون جديدة");
                return;
            }
            try
            {
                IsLoading = true;
                var filter = "Excel Files (*.xlsx)|*.xlsx|CSV Files (*.csv)|*.csv";
                var filePath = await _exportService.GetOpenFilePathAsync(filter);
                if (string.IsNullOrEmpty(filePath))
                    return;

                var confirmMessage = "هل تريد استيراد عناصر المخزون من الملف المحدد؟\n\n" +
                    "⚠️ تحذير:\n" +
                    "• سيتم إضافة العناصر الجديدة إلى القائمة الحالية\n" +
                    "• تأكد من صحة تنسيق الملف\n" +
                    "• يجب أن تحتوي الأعمدة على: الاسم، الفئة، الكمية، الوحدة، الحد الأدنى، الحد الأقصى، سعر الوحدة، الموقع، المورد، الشبكة، الوصف\n\n" +
                    "هل تريد المتابعة؟";
                if (!MessageHelper.ShowConfirmation(confirmMessage, "تأكيد الاستيراد"))
                    return;

                IEnumerable<Inventory> importedItems;
                var isExcel = filePath.EndsWith(".xlsx", StringComparison.OrdinalIgnoreCase);
                if (isExcel)
                    importedItems = await _exportService.ImportInventoryFromExcelAsync(filePath);
                else
                    importedItems = await _exportService.ImportInventoryFromCsvAsync(filePath);

                var itemsList = importedItems.ToList();
                if (!itemsList.Any())
                {
                    var noDataMessage = "لم يتم العثور على بيانات صالحة في الملف.\n\n" +
                        "تأكد من:\n• وجود بيانات في الملف\n• صحة تنسيق الأعمدة\n• وجود سطر العناوين";
                    MessageHelper.ShowWarningMessage(noDataMessage, "لا توجد بيانات");
                    return;
                }

                // حفظ العناصر المستوردة
                int successCount = 0;
                int errorCount = 0;
                var errors = new List<string>();
                using var scope = _serviceProvider.CreateScope();
                var inventoryService = scope.ServiceProvider.GetRequiredService<IInventoryService>();
                foreach (var item in itemsList)
                {
                    try
                    {
                        item.Id = Guid.NewGuid().ToString();
                        await inventoryService.CreateAsync(item);
                        successCount++;
                    }
                    catch (Exception ex)
                    {
                        errorCount++;
                        var itemName = item.Name ?? "عنصر غير محدد";
                        errors.Add($"خطأ في حفظ عنصر {itemName}: {ex.Message}");
                    }
                }

                // تحديث القائمة
                await LoadInventoryAsync();
                MessageHelper.ShowOperationResults("استيراد", successCount, errorCount, errors.ToArray());
            }
            catch (Exception ex)
            {
                MessageHelper.HandleException(ex, "استيراد بيانات المخزون");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async void OnUserChanged(object? sender, User? user)
        {
            try
            {
                // تحديث خصائص الصلاحيات على UI thread
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    OnPropertyChanged(nameof(CanAddItems));
                    OnPropertyChanged(nameof(CanEditItems));
                    OnPropertyChanged(nameof(CanDeleteItems));
                    OnPropertyChanged(nameof(CanManageInventory));
                    OnPropertyChanged(nameof(CanExportInventory));
                    OnPropertyChanged(nameof(CanImportInventory));
                    ExportInventoryCommand?.NotifyCanExecuteChanged();
                    ImportInventoryCommand?.NotifyCanExecuteChanged();
                });

                // تأخير قصير لتجنب التحديثات المتكررة
                await System.Threading.Tasks.Task.Delay(100);

                // إعادة تحميل البيانات حسب الصلاحيات الجديدة
                await LoadInventoryAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in OnUserChanged: {ex.Message}");
            }
        }

        private async System.Threading.Tasks.Task LoadNetworksAsync()
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var networkService = scope.ServiceProvider.GetRequiredService<INetworkService>();

                // استخدام NetworkHelper لتحميل الشبكات مع تطبيق الصلاحيات
                var (networks, defaultNetworkId) = await NetworkHelper.LoadNetworksForSelectionAsync(
                    _authService,
                    networkService);

                AvailableNetworks.Clear();

                // إضافة خيار "الكل" للأدمن فقط
                if (_authService.CanViewAllNetworks)
                {
                    AvailableNetworks.Add(new Network { Id = "", Name = "الكل" });
                }

                foreach (var network in networks)
                {
                    AvailableNetworks.Add(network);
                }

                // تعيين الشبكة الافتراضية
                if (_authService.CanViewAllNetworks)
                {
                    SelectedNetwork = AvailableNetworks.FirstOrDefault(n => n.Id == "");
                    NetworkFilter = "الكل";
                }
                else if (!string.IsNullOrEmpty(defaultNetworkId))
                {
                    SelectedNetwork = AvailableNetworks.FirstOrDefault(n => n.Id == defaultNetworkId);
                    NetworkFilter = SelectedNetwork?.Name ?? "الكل";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading networks: {ex.Message}");
            }
        }

        private string? GetEffectiveNetworkFilter()
        {
            // إذا كان المستخدم لا يستطيع رؤية جميع الشبكات، استخدم شبكته فقط
            if (!_authService.CanViewAllNetworks)
            {
                return _authService.CurrentUserNetworkId;
            }

            // إذا كان يستطيع رؤية جميع الشبكات، استخدم الفلتر المختار
            if (SelectedNetwork == null || SelectedNetwork.Id == "" || NetworkFilter == "الكل")
            {
                return null; // عرض جميع الشبكات
            }

            return SelectedNetwork.Id;
        }

        partial void OnSelectedNetworkChanged(Network? value)
        {
            NetworkFilter = value?.Name ?? "الكل";
            _ = LoadInventoryAsync();
        }

        partial void OnNetworkFilterChanged(string value)
        {
            // تحديث الشبكة المحددة عند تغيير النص
            if (value == "الكل")
            {
                SelectedNetwork = AvailableNetworks.FirstOrDefault(n => n.Id == "");
            }
            else
            {
                SelectedNetwork = AvailableNetworks.FirstOrDefault(n => n.Name == value);
            }
        }


    }
}
