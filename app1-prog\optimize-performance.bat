@echo off
echo ========================================
echo    تحسين أداء تطبيق شبكة برو
echo ========================================
echo.

echo [1/5] تنظيف ملفات البناء القديمة...
if exist "bin" rmdir /s /q "bin"
if exist "obj" rmdir /s /q "obj"
echo تم تنظيف ملفات البناء القديمة.

echo.
echo [2/5] استعادة الحزم...
dotnet restore --no-cache
if %errorlevel% neq 0 (
    echo خطأ في استعادة الحزم!
    pause
    exit /b 1
)

echo.
echo [3/5] بناء التطبيق بوضع الإصدار...
dotnet build --configuration Release --no-restore
if %errorlevel% neq 0 (
    echo خطأ في بناء التطبيق!
    pause
    exit /b 1
)

echo.
echo [4/5] تحسين قاعدة البيانات...
echo تطبيق الفهارس المحسنة...
mysql -u root -p -e "source Database/OptimizeDatabase.sql"
if %errorlevel% neq 0 (
    echo تحذير: لم يتم تطبيق تحسينات قاعدة البيانات. تأكد من تشغيل MySQL.
)

echo.
echo [5/5] تنظيف الذاكرة...
echo تنظيف ملفات Windows المؤقتة...
del /q /f "%temp%\*.*" 2>nul
for /d %%x in ("%temp%\*") do rd /s /q "%%x" 2>nul

echo.
echo ========================================
echo تم تحسين الأداء بنجاح!
echo ========================================
echo.
echo التحسينات المطبقة:
echo - تنظيف ملفات البناء القديمة
echo - بناء محسن للإصدار
echo - فهارس قاعدة بيانات محسنة
echo - تنظيف الذاكرة المؤقتة
echo.
echo يمكنك الآن تشغيل التطبيق بأداء محسن.
echo.
pause
