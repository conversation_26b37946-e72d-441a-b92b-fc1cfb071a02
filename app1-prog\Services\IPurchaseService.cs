using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using NetworkManagement.Models;

namespace NetworkManagement.Services
{
    public interface IPurchaseService
    {
        Task<IEnumerable<Purchase>> GetAllAsync(string? networkFilter = null);
        Task<Purchase?> GetByIdAsync(string id);
        Task<Purchase> CreateAsync(Purchase purchase);
        Task<Purchase> UpdateAsync(Purchase purchase);
        Task<bool> DeleteAsync(string id);
        Task<IEnumerable<Purchase>> SearchAsync(string searchTerm, string? networkFilter = null);
        Task<decimal> GetTotalSpentAsync(string? networkFilter = null, DateTime? startDate = null, DateTime? endDate = null);

        // إضافة دالة فلترة شاملة
        Task<IEnumerable<Purchase>> GetFilteredAsync(
            string? networkFilter = null,
            string? searchText = null,
            string? category = null,
            DateTime? startDate = null,
            DateTime? endDate = null);
    }
}

