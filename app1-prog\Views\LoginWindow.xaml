<Window x:Class="NetworkManagement.Views.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="تسجيل الدخول - إدارة الشبكات المتقدم"
        Height="500" Width="800"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Icon="pack://application:,,,/Resources/shabaka-pro.ico"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FlowDirection="RightToLeft"
        KeyDown="OnKeyDown">

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="1.2*"/>
        </Grid.ColumnDefinitions>

        <!-- Right Side - Brand/Logo -->
        <Border Grid.Column="1" Background="{StaticResource StatisticsBlueBrush}">
            <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                <!-- App Logo -->
                <Image Source="pack://application:,,,/Resources/shabaka-pro.ico"
                       Width="80" Height="80"
                       Margin="{StaticResource SpacingL}"/>

                <TextBlock Text="Shabaka Pro"
                          Style="{StaticResource StatisticNumberStyle}"
                          Foreground="White"
                          HorizontalAlignment="Center"
                          Margin="{StaticResource SpacingS}"/>

                <TextBlock Text="نظام إدارة الشبكات المتقدم"
                          Style="{StaticResource SubHeaderTextStyle}"
                          Foreground="White"
                          Opacity="0.8"
                          HorizontalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- Left Side - Login Form -->
        <Border Grid.Column="0"
                Background="{DynamicResource MaterialDesignCardBackground}"
                Padding="{StaticResource SpacingXL}">
            <StackPanel VerticalAlignment="Center">

                <!-- Form Title -->
                <TextBlock Text="تسجيل الدخول"
                          Style="{StaticResource HeaderTextStyle}"
                          HorizontalAlignment="Right"
                          Margin="{StaticResource SpacingXL}"/>

                <!-- Username Field -->
                <TextBox x:Name="UsernameTextBox"
                        materialDesign:HintAssist.Hint="اسم المستخدم"
                        materialDesign:HintAssist.IsFloating="True"
                        Text="{Binding Username, UpdateSourceTrigger=PropertyChanged}"
                        Margin="{StaticResource ElementMargin}"
                        FontSize="14"
                        TabIndex="1"
                        KeyDown="OnKeyDown"/>

                <!-- Password Field -->
                <PasswordBox x:Name="PasswordBox"
                            materialDesign:HintAssist.Hint="كلمة المرور"
                            materialDesign:HintAssist.IsFloating="True"
                            PasswordChanged="PasswordBox_PasswordChanged"
                            GotFocus="PasswordBox_GotFocus"
                            Margin="{StaticResource ElementMargin}"
                            FontSize="14"
                            TabIndex="2"
                            KeyDown="OnKeyDown"/>

                <!-- Remember Me -->
                <CheckBox Content="تذكرني"
                         IsChecked="{Binding RememberMe}"
                         Margin="{StaticResource ElementMargin}"
                         TabIndex="3"
                         HorizontalAlignment="Right"/>

                <!-- Error Message -->
                <Border Background="#FFEBEE"
                       BorderBrush="{StaticResource StatusDisconnectedBrush}"
                       BorderThickness="1"
                       CornerRadius="4"
                       Padding="{StaticResource SpacingM}"
                       Margin="{StaticResource ElementMargin}"
                       Visibility="{Binding HasError, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="AlertCircle"
                                               Foreground="{StaticResource StatusDisconnectedBrush}"
                                               VerticalAlignment="Center"
                                               Margin="{StaticResource SpacingS}"/>
                        <TextBlock Text="{Binding ErrorMessage}"
                                  Foreground="{StaticResource StatusDisconnectedBrush}"
                                  TextWrapping="Wrap"
                                  VerticalAlignment="Center"/>
                    </StackPanel>
                </Border>

                <!-- Login Button -->
                <Button Content="تسجيل الدخول"
                       Command="{Binding LoginCommand}"
                       IsEnabled="{Binding IsLoading, Converter={StaticResource InverseBooleanConverter}}"
                       Style="{StaticResource MaterialDesignRaisedButton}"
                       Background="{DynamicResource PrimaryHueMidBrush}"
                       Foreground="White"
                       Height="40"
                       FontSize="14"
                       TabIndex="4"
                       IsDefault="True"
                       Margin="{StaticResource SpacingM}"/>

                <!-- Loading Indicator -->
                <ProgressBar IsIndeterminate="True"
                           Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                           Margin="{StaticResource SpacingM}"/>

                <!-- Forgot Password -->
                <Button Content="نسيت كلمة المرور؟"
                       Command="{Binding ForgotPasswordCommand}"
                       Style="{StaticResource MaterialDesignFlatButton}"
                       HorizontalAlignment="Center"
                       TabIndex="5"
                       Foreground="{DynamicResource PrimaryHueMidBrush}"/>

            </StackPanel>
        </Border>
    </Grid>
</Window>
