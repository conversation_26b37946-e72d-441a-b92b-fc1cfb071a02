using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using NetworkManagement.Models;

namespace NetworkManagement.Services
{
    public interface ISiteService
    {
        Task<IEnumerable<Site>> GetAllAsync(string? networkFilter = null);
        Task<Site?> GetByIdAsync(string id);
        Task<Site> CreateAsync(Site site);
        Task<Site> UpdateAsync(Site site);
        Task<bool> DeleteAsync(string id);
        Task<IEnumerable<Site>> SearchAsync(string searchTerm, string? networkFilter = null);
    }
}

