// All usings must be at the top
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using Microsoft.Win32;
using Microsoft.Extensions.DependencyInjection;
using NetworkManagement.Models;
using NetworkManagement.ViewModels;
using NetworkManagement.Helpers;
using OfficeOpenXml;

namespace NetworkManagement.Services
{
    public class ReportExportService : IReportExportService
    {
        private readonly IServiceProvider _serviceProvider;

        public ReportExportService(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        public async System.Threading.Tasks.Task<IEnumerable<Models.Task>> ImportTasksFromCsvAsync(string filePath)
        {
            var result = new List<Models.Task>();
            var lines = await File.ReadAllLinesAsync(filePath, Encoding.UTF8);
            if (lines.Length < 2) return result;

            // توقع أن أول سطر هو العناوين
            for (int i = 1; i < lines.Length; i++)
            {
                var fields = ParseCsvLine(lines[i]);
                if (fields.Length < 9) continue;

                var task = new Models.Task
                {
                    Id = Guid.NewGuid().ToString(),
                    Description = fields[0]?.Trim() ?? string.Empty,
                    Status = TaskHelper.GetStatusKey(fields[1]),
                    Priority = TaskHelper.GetPriorityKey(fields[2]),
                    RequestDate = DateTime.TryParse(fields[3], out var requested) ? requested : DateTime.Now,
                    CompletedAt = DateTime.TryParse(fields[4], out var comp) ? comp : (DateTime?)null,
                    // User و Network يمكن ربطهم لاحقًا حسب الحاجة
                    Notes = fields[5]?.Trim()
                };
                result.Add(task);
            }
            return result;
        }

        public System.Threading.Tasks.Task<IEnumerable<Models.Task>> ImportTasksFromExcelAsync(string filePath)
        {
            var result = new List<Models.Task>();
            using var package = new OfficeOpenXml.ExcelPackage(new FileInfo(filePath));
            var worksheet = package.Workbook.Worksheets.FirstOrDefault();
            if (worksheet == null) return System.Threading.Tasks.Task.FromResult<IEnumerable<Models.Task>>(result);

            int row = 2;
            while (true)
            {
                var desc = worksheet.Cells[row, 1].Text?.Trim();
                if (string.IsNullOrWhiteSpace(desc)) break;

                var task = new Models.Task
                {
                    Id = Guid.NewGuid().ToString(),
                    Description = desc,
                    Status = TaskHelper.GetStatusKey(worksheet.Cells[row, 2].Text),
                    Priority = TaskHelper.GetPriorityKey(worksheet.Cells[row, 3].Text),
                    RequestDate = DateTime.TryParse(worksheet.Cells[row, 4].Text, out var requested) ? requested : DateTime.Now,
                    CompletedAt = DateTime.TryParse(worksheet.Cells[row, 5].Text, out var comp) ? comp : (DateTime?)null,
                    Notes = worksheet.Cells[row, 6].Text?.Trim()
                };
                result.Add(task);
                row++;
            }
            return System.Threading.Tasks.Task.FromResult<IEnumerable<Models.Task>>(result);
        }




        public async Task<string> ExportDevicesReportToCsvAsync(IEnumerable<Device> devices, string filePath)
        {
            var csv = new StringBuilder();
            csv.AppendLine("الموقع,النوع,عنوان IP,المسؤول,رقم الهاتف,الشبكة,طريقة الربط,الشبكة المرتبطة,القناة,الأجهزة المتصلة,نوع المحول المركب,نوع سلك الشبكة,طول سلك الشبكة,نوع سلك الكهرباء,طول سلك الكهرباء,الحالة,تاريخ التثبيت,آخر فحص");

            foreach (var device in devices)
            {
                csv.AppendLine($"\"{device.Location ?? ""}\",\"{device.Type ?? ""}\",\"{device.Ip ?? ""}\",\"{device.Responsible ?? ""}\",\"{device.Phone ?? ""}\",\"{device.Network?.Name ?? ""}\",\"{device.ConnectionMethod ?? ""}\",\"{device.LinkedNetwork ?? ""}\",\"{device.Channel?.ToString() ?? ""}\",\"{device.ConnectedDevices?.ToString() ?? ""}\",\"{device.InstalledAdapterType ?? ""}\",\"{device.NetworkCableType ?? ""}\",\"{device.NetworkCableLength?.ToString() ?? ""}\",\"{device.PowerCableType ?? ""}\",\"{device.PowerCableLength?.ToString() ?? ""}\",\"{device.Status ?? ""}\",\"{device.InstallDate?.ToString("dd/MM/yyyy") ?? ""}\",\"{device.LastCheck?.ToString("dd/MM/yyyy") ?? ""}\"");
            }

            await File.WriteAllTextAsync(filePath, csv.ToString(), Encoding.UTF8);
            return filePath;
        }

        public async Task<string> ExportSitesReportToCsvAsync(IEnumerable<Site> sites, string filePath)
        {
            var csv = new StringBuilder();
            csv.AppendLine("الاسم,العنوان,خط الطول,خط العرض,مصدر الطاقة,نوع البطارية,حجم البطارية,نوع القواعد,عدد القواعد,نوع الصناديق,عدد الصناديق,نوع سلك الشبكة,طول سلك الشبكة,نوع سلك الكهرباء,طول سلك الكهرباء,الشبكة");

            foreach (var site in sites)
            {
                csv.AppendLine($"\"{site.Name ?? ""}\",\"{site.Address ?? ""}\",\"{site.GpsLng?.ToString() ?? ""}\",\"{site.GpsLat?.ToString() ?? ""}\",\"{site.PowerSource ?? ""}\",\"{site.BatteryType ?? ""}\",\"{site.BatterySize ?? ""}\",\"{site.BaseType ?? ""}\",\"{site.BaseCount?.ToString() ?? ""}\",\"{site.BoxType ?? ""}\",\"{site.BoxCount?.ToString() ?? ""}\",\"{site.NetworkCableType ?? ""}\",\"{site.NetworkCableLength?.ToString() ?? ""}\",\"{site.PowerCableType ?? ""}\",\"{site.PowerCableLength?.ToString() ?? ""}\",\"{site.Network?.Name ?? ""}\"");
            }

            await File.WriteAllTextAsync(filePath, csv.ToString(), Encoding.UTF8);
            return filePath;
        }

        public async Task<string> ExportUsersReportToCsvAsync(IEnumerable<User> users, string filePath)
        {
            var csv = new StringBuilder();
            csv.AppendLine("اسم المستخدم,الاسم,الدور,الشبكة,تاريخ الإنشاء");

            foreach (var user in users)
            {
                csv.AppendLine($"\"{user.Username ?? ""}\",\"{user.Name ?? ""}\",\"{user.Role ?? ""}\",\"{user.Network?.Name ?? ""}\",\"{user.CreatedAt.ToString("dd/MM/yyyy") ?? ""}\"");
            }

            await File.WriteAllTextAsync(filePath, csv.ToString(), Encoding.UTF8);
            return filePath;
        }

        public async Task<string> ExportPurchasesReportToCsvAsync(IEnumerable<Purchase> purchases, string filePath)
        {
            var csv = new StringBuilder();
            csv.AppendLine("نوع الصنف,الكمية,الوحدة,السعر,التاريخ,المورد,الشبكة,الوصف,رقم الفاتورة,الفئة");

            foreach (var purchase in purchases)
            {
                csv.AppendLine($"\"{purchase.ItemType ?? ""}\",\"{purchase.Quantity}\",\"{purchase.Unit ?? ""}\",\"{purchase.Price:C}\",\"{purchase.Date.ToString("dd/MM/yyyy")}\",\"{purchase.Supplier ?? ""}\",\"{purchase.Network?.Name ?? ""}\",\"{purchase.Description ?? ""}\",\"{purchase.InvoiceNumber ?? ""}\",\"{purchase.Category ?? ""}\"");
            }

            await File.WriteAllTextAsync(filePath, csv.ToString(), Encoding.UTF8);
            return filePath;
        }

        public async Task<string> ExportInventoryReportToCsvAsync(IEnumerable<Inventory> inventory, string filePath)
        {
            var csv = new StringBuilder();
            csv.AppendLine("الاسم,الفئة,الكمية,الوحدة,الحد الأدنى,الحد الأقصى,سعر الوحدة,الموقع,المورد,الشبكة,الوصف");

            foreach (var item in inventory)
            {
                csv.AppendLine($"\"{item.Name ?? ""}\",\"{item.Category ?? ""}\",\"{item.Quantity}\",\"{item.Unit ?? ""}\",\"{item.MinimumStock}\",\"{item.MaximumStock}\",\"{item.UnitPrice:C}\",\"{item.Location ?? ""}\",\"{item.Supplier ?? ""}\",\"{item.Network?.Name ?? ""}\",\"{item.Description ?? ""}\"");
            }

            await File.WriteAllTextAsync(filePath, csv.ToString(), Encoding.UTF8);
            return filePath;
        }

        public async Task<string> ExportTasksReportToCsvAsync(IEnumerable<Models.Task> tasks, string filePath)
        {
            var csv = new StringBuilder();
            csv.AppendLine("الوصف,الحالة,الأولوية,تاريخ الطلب,تاريخ الإكمال,المستخدم المسؤول,الشبكة,الملاحظات");

            foreach (var task in tasks)
            {
                csv.AppendLine($"\"{task.Description ?? ""}\",\"{task.StatusDisplay}\",\"{task.PriorityDisplay}\",\"{task.RequestDate.ToString("dd/MM/yyyy")}\",\"{task.CompletedAt?.ToString("dd/MM/yyyy") ?? ""}\",\"{task.User?.Name ?? ""}\",\"{task.Network?.Name ?? ""}\",\"{task.Notes ?? ""}\"");
            }

            await File.WriteAllTextAsync(filePath, csv.ToString(), Encoding.UTF8);
            return filePath;
        }

        public async Task<string> ExportUserEvaluationsReportToCsvAsync(IEnumerable<UserEvaluation> evaluations, string filePath)
        {
            var csv = new StringBuilder();
            csv.AppendLine("المستخدم,المقيم,التاريخ,النقاط,النسبة المئوية,التقدير,الملاحظات,الشبكة");

            foreach (var evaluation in evaluations)
            {
                csv.AppendLine($"\"{evaluation.UserName}\",\"{evaluation.EvaluatedByName}\",\"{evaluation.EvaluationDateDisplay}\",\"{evaluation.Score}\",\"{evaluation.ScorePercentageDisplay}\",\"{evaluation.ScoreDescription}\",\"{evaluation.NotesDisplay}\",\"{evaluation.NetworkName}\"");
            }

            await File.WriteAllTextAsync(filePath, csv.ToString(), Encoding.UTF8);
            return filePath;
        }

        public async Task<string> ExportNetworkStatisticsToCsvAsync(IEnumerable<NetworkStatistic> statistics, string filePath)
        {
            var csv = new StringBuilder();
            csv.AppendLine("الشبكة,عدد الأجهزة,نشط,غير نشط");

            foreach (var stat in statistics)
            {
                csv.AppendLine($"\"{stat.NetworkName}\",\"{stat.DeviceCount}\",\"{stat.ActiveCount}\",\"{stat.InactiveCount}\"");
            }

            await File.WriteAllTextAsync(filePath, csv.ToString(), Encoding.UTF8);
            return filePath;
        }

        public async Task<string> ExportMonthlyPurchasesToCsvAsync(IEnumerable<MonthlyPurchase> purchases, string filePath)
        {
            var csv = new StringBuilder();
            csv.AppendLine("الشهر,المبلغ,عدد المشتريات");

            foreach (var purchase in purchases)
            {
                csv.AppendLine($"\"{purchase.Month}\",\"{purchase.Amount:C}\",\"{purchase.Count}\"");
            }

            await File.WriteAllTextAsync(filePath, csv.ToString(), Encoding.UTF8);
            return filePath;
        }

        public async Task<string> ExportDeviceStatusCountsToCsvAsync(IEnumerable<DeviceStatusCount> statusCounts, string filePath)
        {
            var csv = new StringBuilder();
            csv.AppendLine("الحالة,العدد,النسبة %");

            foreach (var status in statusCounts)
            {
                csv.AppendLine($"\"{status.Status}\",\"{status.Count}\",\"{status.Percentage:F1}\"");
            }

            await File.WriteAllTextAsync(filePath, csv.ToString(), Encoding.UTF8);
            return filePath;
        }

        public async Task<string> ExportGeneralReportToCsvAsync(ReportsViewModel viewModel, string filePath)
        {
            var csv = new StringBuilder();

            // General Statistics
            csv.AppendLine("=== الإحصائيات العامة ===");
            csv.AppendLine($"إجمالي الأجهزة,{viewModel.TotalDevices}");
            csv.AppendLine($"الأجهزة النشطة,{viewModel.ActiveDevices}");
            csv.AppendLine($"إجمالي المواقع,{viewModel.TotalSites}");
            csv.AppendLine($"إجمالي المستخدمين,{viewModel.TotalUsers}");
            csv.AppendLine($"إجمالي المشتريات,{viewModel.TotalPurchases:N0} ر.ي");
            csv.AppendLine($"إجمالي المخزون,{viewModel.TotalInventoryItems}");
            csv.AppendLine($"المخزون المنخفض,{viewModel.LowStockItems}");
            csv.AppendLine();

            await File.WriteAllTextAsync(filePath, csv.ToString(), Encoding.UTF8);
            return filePath;
        }



        #region Excel Export Methods

        public async Task<string> ExportDevicesReportToExcelAsync(IEnumerable<Device> devices, string filePath)
        {
            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("الأجهزة");

            // Headers
            var headers = new[] { "الموقع", "النوع", "عنوان IP", "المسؤول", "رقم الهاتف", "الشبكة", "طريقة الربط", "الشبكة المرتبطة", "القناة", "الأجهزة المتصلة", "نوع المحول المركب", "نوع سلك الشبكة", "طول سلك الشبكة", "نوع سلك الكهرباء", "طول سلك الكهرباء", "الحالة", "تاريخ التثبيت", "آخر فحص" };
            for (int i = 0; i < headers.Length; i++)
            {
                worksheet.Cells[1, i + 1].Value = headers[i];
                worksheet.Cells[1, i + 1].Style.Font.Bold = true;
            }

            // Data
            int row = 2;
            foreach (var device in devices)
            {
                worksheet.Cells[row, 1].Value = device.Location ?? "";
                worksheet.Cells[row, 2].Value = device.Type ?? "";
                worksheet.Cells[row, 3].Value = device.Ip ?? "";
                worksheet.Cells[row, 4].Value = device.Responsible ?? "";
                worksheet.Cells[row, 5].Value = device.Phone ?? "";
                worksheet.Cells[row, 6].Value = device.Network?.Name ?? "";
                worksheet.Cells[row, 7].Value = device.ConnectionMethod ?? "";
                worksheet.Cells[row, 8].Value = device.LinkedNetwork ?? "";
                worksheet.Cells[row, 9].Value = device.Channel?.ToString() ?? "";
                worksheet.Cells[row, 10].Value = device.ConnectedDevices?.ToString() ?? "";
                worksheet.Cells[row, 11].Value = device.InstalledAdapterType ?? "";
                worksheet.Cells[row, 12].Value = device.NetworkCableType ?? "";
                worksheet.Cells[row, 13].Value = device.NetworkCableLength?.ToString() ?? "";
                worksheet.Cells[row, 14].Value = device.PowerCableType ?? "";
                worksheet.Cells[row, 15].Value = device.PowerCableLength?.ToString() ?? "";
                worksheet.Cells[row, 16].Value = device.Status ?? "";
                worksheet.Cells[row, 17].Value = device.InstallDate?.ToString("dd/MM/yyyy") ?? "";
                worksheet.Cells[row, 18].Value = device.LastCheck?.ToString("dd/MM/yyyy") ?? "";
                row++;
            }

            // Auto-fit columns
            worksheet.Cells.AutoFitColumns();

            await package.SaveAsAsync(new FileInfo(filePath));
            return filePath;
        }

        public async Task<string> ExportSitesReportToExcelAsync(IEnumerable<Site> sites, string filePath)
        {
            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("المواقع");

            // Headers
            var headers = new[] { "الاسم", "العنوان", "خط الطول", "خط العرض", "مصدر الطاقة", "نوع البطارية", "حجم البطارية", "نوع القواعد", "عدد القواعد", "نوع الصناديق", "عدد الصناديق", "نوع سلك الشبكة", "طول سلك الشبكة", "نوع سلك الكهرباء", "طول سلك الكهرباء", "الشبكة" };
            for (int i = 0; i < headers.Length; i++)
            {
                worksheet.Cells[1, i + 1].Value = headers[i];
                worksheet.Cells[1, i + 1].Style.Font.Bold = true;
            }

            // Data
            int row = 2;
            foreach (var site in sites)
            {
                worksheet.Cells[row, 1].Value = site.Name ?? "";
                worksheet.Cells[row, 2].Value = site.Address ?? "";
                worksheet.Cells[row, 3].Value = site.GpsLng?.ToString() ?? "";
                worksheet.Cells[row, 4].Value = site.GpsLat?.ToString() ?? "";
                worksheet.Cells[row, 5].Value = site.PowerSource ?? "";
                worksheet.Cells[row, 6].Value = site.BatteryType ?? "";
                worksheet.Cells[row, 7].Value = site.BatterySize ?? "";
                worksheet.Cells[row, 8].Value = site.BaseType ?? "";
                worksheet.Cells[row, 9].Value = site.BaseCount?.ToString() ?? "";
                worksheet.Cells[row, 10].Value = site.BoxType ?? "";
                worksheet.Cells[row, 11].Value = site.BoxCount?.ToString() ?? "";
                worksheet.Cells[row, 12].Value = site.NetworkCableType ?? "";
                worksheet.Cells[row, 13].Value = site.NetworkCableLength?.ToString() ?? "";
                worksheet.Cells[row, 14].Value = site.PowerCableType ?? "";
                worksheet.Cells[row, 15].Value = site.PowerCableLength?.ToString() ?? "";
                worksheet.Cells[row, 16].Value = site.Network?.Name ?? "";
                row++;
            }

            worksheet.Cells.AutoFitColumns();
            await package.SaveAsAsync(new FileInfo(filePath));
            return filePath;
        }

        public async Task<string> ExportUsersReportToExcelAsync(IEnumerable<User> users, string filePath)
        {
            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("المستخدمين");

            // Headers
            var headers = new[] { "اسم المستخدم", "الاسم", "الدور", "الشبكة", "تاريخ الإنشاء" };
            for (int i = 0; i < headers.Length; i++)
            {
                worksheet.Cells[1, i + 1].Value = headers[i];
                worksheet.Cells[1, i + 1].Style.Font.Bold = true;
            }

            // Data
            int row = 2;
            foreach (var user in users)
            {
                worksheet.Cells[row, 1].Value = user.Username ?? "";
                worksheet.Cells[row, 2].Value = user.Name ?? "";
                worksheet.Cells[row, 3].Value = user.Role ?? "";
                worksheet.Cells[row, 4].Value = user.Network?.Name ?? "";
                worksheet.Cells[row, 5].Value = user.CreatedAt.ToString("dd/MM/yyyy");
                row++;
            }

            worksheet.Cells.AutoFitColumns();
            await package.SaveAsAsync(new FileInfo(filePath));
            return filePath;
        }

        public async Task<string> ExportPurchasesReportToExcelAsync(IEnumerable<Purchase> purchases, string filePath)
        {
            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("المشتريات");

            // Headers
            var headers = new[] { "نوع الصنف", "الكمية", "الوحدة", "السعر", "التاريخ", "المورد", "الشبكة", "الوصف", "رقم الفاتورة", "الفئة" };
            for (int i = 0; i < headers.Length; i++)
            {
                worksheet.Cells[1, i + 1].Value = headers[i];
                worksheet.Cells[1, i + 1].Style.Font.Bold = true;
            }

            // Data
            int row = 2;
            foreach (var purchase in purchases)
            {
                worksheet.Cells[row, 1].Value = purchase.ItemType ?? "";
                worksheet.Cells[row, 2].Value = purchase.Quantity;
                worksheet.Cells[row, 3].Value = purchase.Unit ?? "";
                worksheet.Cells[row, 4].Value = purchase.Price;
                worksheet.Cells[row, 5].Value = purchase.Date.ToString("dd/MM/yyyy");
                worksheet.Cells[row, 6].Value = purchase.Supplier ?? "";
                worksheet.Cells[row, 7].Value = purchase.Network?.Name ?? "";
                worksheet.Cells[row, 8].Value = purchase.Description ?? "";
                worksheet.Cells[row, 9].Value = purchase.InvoiceNumber ?? "";
                worksheet.Cells[row, 10].Value = purchase.Category ?? "";
                row++;
            }

            worksheet.Cells.AutoFitColumns();
            await package.SaveAsAsync(new FileInfo(filePath));
            return filePath;
        }

        public async Task<string> ExportInventoryReportToExcelAsync(IEnumerable<Inventory> inventory, string filePath)
        {
            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("المخزون");

            // Headers
            var headers = new[] { "الاسم", "الفئة", "الكمية", "الوحدة", "الحد الأدنى", "الحد الأقصى", "سعر الوحدة", "الموقع", "المورد", "الشبكة", "الوصف" };
            for (int i = 0; i < headers.Length; i++)
            {
                worksheet.Cells[1, i + 1].Value = headers[i];
                worksheet.Cells[1, i + 1].Style.Font.Bold = true;
            }

            // Data
            int row = 2;
            foreach (var item in inventory)
            {
                worksheet.Cells[row, 1].Value = item.Name ?? "";
                worksheet.Cells[row, 2].Value = item.Category ?? "";
                worksheet.Cells[row, 3].Value = item.Quantity;
                worksheet.Cells[row, 4].Value = item.Unit ?? "";
                worksheet.Cells[row, 5].Value = item.MinimumStock;
                worksheet.Cells[row, 6].Value = item.MaximumStock;
                worksheet.Cells[row, 7].Value = item.UnitPrice;
                worksheet.Cells[row, 8].Value = item.Location ?? "";
                worksheet.Cells[row, 9].Value = item.Supplier ?? "";
                worksheet.Cells[row, 10].Value = item.Network?.Name ?? "";
                worksheet.Cells[row, 11].Value = item.Description ?? "";
                row++;
            }

            worksheet.Cells.AutoFitColumns();
            await package.SaveAsAsync(new FileInfo(filePath));
            return filePath;
        }

        public async Task<string> ExportTasksReportToExcelAsync(IEnumerable<Models.Task> tasks, string filePath)
        {
            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("المهام");

            // Headers
            var headers = new[] { "الوصف", "الحالة", "الأولوية", "تاريخ الطلب", "تاريخ الإكمال", "المستخدم المسؤول", "الشبكة", "الملاحظات" };
            for (int i = 0; i < headers.Length; i++)
            {
                worksheet.Cells[1, i + 1].Value = headers[i];
                worksheet.Cells[1, i + 1].Style.Font.Bold = true;
            }

            // Data
            int row = 2;
            foreach (var task in tasks)
            {
                worksheet.Cells[row, 1].Value = task.Description ?? "";
                worksheet.Cells[row, 2].Value = task.StatusDisplay;
                worksheet.Cells[row, 3].Value = task.PriorityDisplay;
                worksheet.Cells[row, 4].Value = task.RequestDate.ToString("dd/MM/yyyy");
                worksheet.Cells[row, 5].Value = task.CompletedAt?.ToString("dd/MM/yyyy") ?? "";
                worksheet.Cells[row, 6].Value = task.User?.Name ?? "";
                worksheet.Cells[row, 7].Value = task.Network?.Name ?? "";
                worksheet.Cells[row, 8].Value = task.Notes ?? "";
                row++;
            }

            worksheet.Cells.AutoFitColumns();
            await package.SaveAsAsync(new FileInfo(filePath));
            return filePath;
        }

        public async Task<string> ExportUserEvaluationsReportToExcelAsync(IEnumerable<UserEvaluation> evaluations, string filePath)
        {
            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("التقييمات");

            // Headers
            var headers = new[] { "المستخدم", "المقيم", "التاريخ", "النقاط", "النسبة المئوية", "التقدير", "الملاحظات", "الشبكة" };
            for (int i = 0; i < headers.Length; i++)
            {
                worksheet.Cells[1, i + 1].Value = headers[i];
                worksheet.Cells[1, i + 1].Style.Font.Bold = true;
                worksheet.Cells[1, i + 1].Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                worksheet.Cells[1, i + 1].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightBlue);
            }

            // Data
            int row = 2;
            foreach (var evaluation in evaluations)
            {
                worksheet.Cells[row, 1].Value = evaluation.UserName;
                worksheet.Cells[row, 2].Value = evaluation.EvaluatedByName;
                worksheet.Cells[row, 3].Value = evaluation.EvaluationDateDisplay;
                worksheet.Cells[row, 4].Value = evaluation.Score;
                worksheet.Cells[row, 5].Value = evaluation.ScorePercentageDisplay;
                worksheet.Cells[row, 6].Value = evaluation.ScoreDescription;
                worksheet.Cells[row, 7].Value = evaluation.NotesDisplay;
                worksheet.Cells[row, 8].Value = evaluation.NetworkName;

                // تلوين الصفوف حسب النقاط
                var scoreColor = evaluation.Score switch
                {
                    >= 9 => System.Drawing.Color.LightGreen,
                    >= 7 => System.Drawing.Color.LightYellow,
                    >= 5 => System.Drawing.Color.LightCoral,
                    _ => System.Drawing.Color.LightPink
                };

                for (int col = 1; col <= 8; col++)
                {
                    worksheet.Cells[row, col].Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                    worksheet.Cells[row, col].Style.Fill.BackgroundColor.SetColor(scoreColor);
                }

                row++;
            }

            worksheet.Cells.AutoFitColumns();
            await package.SaveAsAsync(new FileInfo(filePath));
            return filePath;
        }

        public async Task<string> ExportCompleteReportToExcelAsync(ReportsViewModel viewModel, string filePath)
        {
            using var package = new ExcelPackage();

            // Summary Sheet
            var summarySheet = package.Workbook.Worksheets.Add("الملخص");
            summarySheet.Cells[1, 1].Value = "الإحصائيات العامة";
            summarySheet.Cells[1, 1].Style.Font.Bold = true;
            summarySheet.Cells[1, 1].Style.Font.Size = 16;

            summarySheet.Cells[3, 1].Value = "إجمالي الأجهزة";
            summarySheet.Cells[3, 2].Value = viewModel.TotalDevices;
            summarySheet.Cells[4, 1].Value = "الأجهزة النشطة";
            summarySheet.Cells[4, 2].Value = viewModel.ActiveDevices;
            summarySheet.Cells[5, 1].Value = "إجمالي المواقع";
            summarySheet.Cells[5, 2].Value = viewModel.TotalSites;
            summarySheet.Cells[6, 1].Value = "إجمالي المستخدمين";
            summarySheet.Cells[6, 2].Value = viewModel.TotalUsers;
            summarySheet.Cells[7, 1].Value = "إجمالي المشتريات";
            summarySheet.Cells[7, 2].Value = viewModel.TotalPurchases;
            summarySheet.Cells[8, 1].Value = "إجمالي المخزون";
            summarySheet.Cells[8, 2].Value = viewModel.TotalInventoryItems;
            summarySheet.Cells[9, 1].Value = "المخزون المنخفض";
            summarySheet.Cells[9, 2].Value = viewModel.LowStockItems;

            summarySheet.Cells.AutoFitColumns();

            await package.SaveAsAsync(new FileInfo(filePath));
            return filePath;
        }

        #endregion

        #region Import Methods - Will be implemented in next part

        public async System.Threading.Tasks.Task<IEnumerable<Device>> ImportDevicesFromCsvAsync(string filePath)
        {
            var devices = new List<Device>();

            try
            {
                var lines = await File.ReadAllLinesAsync(filePath, Encoding.UTF8);
                if (lines.Length <= 1) return devices; // No data or header only

                // Skip header line
                for (int i = 1; i < lines.Length; i++)
                {
                    var line = lines[i];
                    if (string.IsNullOrWhiteSpace(line)) continue;

                    var values = ParseCsvLine(line);
                    if (values.Length >= 8) // Minimum required fields
                    {
                        var device = new Device
                        {
                            Id = Guid.NewGuid().ToString(),
                            Location = values[0],
                            Type = values[1],
                            Ip = values[2],
                            Responsible = values[3],
                            Phone = values.Length > 4 ? values[4] : "",
                            NetworkId = values.Length > 5 ? values[5] : "",
                            ConnectionMethod = values.Length > 6 ? values[6] : "",
                            LinkedNetwork = values.Length > 7 ? values[7] : "",
                            Channel = values.Length > 8 && int.TryParse(values[8], out var channel) ? channel : null,
                            ConnectedDevices = values.Length > 9 && int.TryParse(values[9], out var connected) ? connected : null,
                            InstalledAdapterType = values.Length > 10 ? values[10] : "",
                            NetworkCableType = values.Length > 11 ? values[11] : "",
                            NetworkCableLength = values.Length > 12 && int.TryParse(values[12], out var netCable) ? netCable : null,
                            PowerCableType = values.Length > 13 ? values[13] : "",
                            PowerCableLength = values.Length > 14 && int.TryParse(values[14], out var powerCable) ? powerCable : null,
                            Status = values.Length > 15 ? values[15] : "غير محدد",
                            InstallDate = values.Length > 16 && DateTime.TryParse(values[16], out var installDate) ? installDate : DateTime.Now,
                            LastCheck = values.Length > 17 && DateTime.TryParse(values[17], out var lastCheck) ? lastCheck : null
                        };
                        devices.Add(device);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في قراءة ملف CSV: {ex.Message}");
            }

            return devices;
        }

        public async System.Threading.Tasks.Task<IEnumerable<Device>> ImportDevicesFromExcelAsync(string filePath)
        {
            var devices = new List<Device>();

            try
            {
                return await System.Threading.Tasks.Task.Run(() =>
                {
                    using var package = new ExcelPackage(new FileInfo(filePath));
                    var worksheet = package.Workbook.Worksheets.FirstOrDefault();
                    if (worksheet == null) return devices;

                    var rowCount = worksheet.Dimension?.Rows ?? 0;
                    if (rowCount <= 1) return devices; // No data or header only

                    // Start from row 2 (skip header)
                    for (int row = 2; row <= rowCount; row++)
                    {
                        var location = worksheet.Cells[row, 1].Text;
                        var type = worksheet.Cells[row, 2].Text;
                        var ip = worksheet.Cells[row, 3].Text;
                        var responsible = worksheet.Cells[row, 4].Text;

                        // Skip empty rows
                        if (string.IsNullOrWhiteSpace(location) && string.IsNullOrWhiteSpace(responsible)) continue;

                        var device = new Device
                        {
                            Id = Guid.NewGuid().ToString(),
                            Location = location,
                            Type = type,
                            Ip = ip,
                            Responsible = responsible,
                            Phone = worksheet.Cells[row, 5].Text,
                            NetworkId = worksheet.Cells[row, 6].Text,
                            ConnectionMethod = worksheet.Cells[row, 7].Text,
                            LinkedNetwork = worksheet.Cells[row, 8].Text,
                            Channel = int.TryParse(worksheet.Cells[row, 9].Text, out var channel) ? channel : null,
                            ConnectedDevices = int.TryParse(worksheet.Cells[row, 10].Text, out var connected) ? connected : null,
                            InstalledAdapterType = worksheet.Cells[row, 11].Text,
                            NetworkCableType = worksheet.Cells[row, 12].Text,
                            NetworkCableLength = int.TryParse(worksheet.Cells[row, 13].Text, out var netCable) ? netCable : null,
                            PowerCableType = worksheet.Cells[row, 14].Text,
                            PowerCableLength = int.TryParse(worksheet.Cells[row, 15].Text, out var powerCable) ? powerCable : null,
                            Status = !string.IsNullOrWhiteSpace(worksheet.Cells[row, 16].Text) ? worksheet.Cells[row, 16].Text : "غير محدد",
                            InstallDate = DateTime.TryParse(worksheet.Cells[row, 17].Text, out var installDate) ? installDate : DateTime.Now,
                            LastCheck = DateTime.TryParse(worksheet.Cells[row, 18].Text, out var lastCheck) ? lastCheck : null
                        };
                        devices.Add(device);
                    }

                    return devices;
                });
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في قراءة ملف Excel: {ex.Message}");
            }
        }

        public async System.Threading.Tasks.Task<IEnumerable<Site>> ImportSitesFromCsvAsync(string filePath)
        {
            var sites = new List<Site>();
            try
            {
                var lines = await File.ReadAllLinesAsync(filePath, Encoding.UTF8);
                if (lines.Length <= 1) return sites;

                // تحميل جميع الشبكات مرة واحدة لتحسين الأداء
                var networkService = _serviceProvider.GetRequiredService<INetworkService>();
                var allNetworks = await networkService.GetAllAsync();
                var networkLookup = allNetworks.ToDictionary(n => n.Name, n => n.Id, StringComparer.OrdinalIgnoreCase);

                // Skip header line
                for (int i = 1; i < lines.Length; i++)
                {
                    var line = lines[i];
                    if (string.IsNullOrWhiteSpace(line)) continue;

                    var values = ParseCsvLine(line);
                    // الأعمدة: اسم الموقع,العنوان,الهاتف,الشبكة,إحداثيات GPS,مصدر الطاقة
                    if (values.Length >= 2)
                    {
                        var networkName = values.Length > 3 ? values[3] : null;
                        var networkId = !string.IsNullOrWhiteSpace(networkName) && networkLookup.TryGetValue(networkName, out var id) ? id : null;

                        var site = new Site
                        {
                            Id = Guid.NewGuid().ToString(),
                            Name = values[0],
                            Address = values.Length > 1 ? values[1] : null,
                            GpsLng = values.Length > 2 && double.TryParse(values[2], out var lng) ? lng : null,
                            GpsLat = values.Length > 3 && double.TryParse(values[3], out var lat) ? lat : null,
                            PowerSource = values.Length > 4 ? values[4] : null,
                            BatteryType = values.Length > 5 ? values[5] : null,
                            BatterySize = values.Length > 6 ? values[6] : null,
                            BaseType = values.Length > 7 ? values[7] : null,
                            BaseCount = values.Length > 8 && int.TryParse(values[8], out var baseCount) ? baseCount : null,
                            BoxType = values.Length > 9 ? values[9] : null,
                            BoxCount = values.Length > 10 && int.TryParse(values[10], out var boxCount) ? boxCount : null,
                            NetworkCableType = values.Length > 11 ? values[11] : null,
                            NetworkCableLength = values.Length > 12 && int.TryParse(values[12], out var netCableLength) ? netCableLength : null,
                            PowerCableType = values.Length > 13 ? values[13] : null,
                            PowerCableLength = values.Length > 14 && int.TryParse(values[14], out var powerCableLength) ? powerCableLength : null,
                            NetworkId = values.Length > 15 && !string.IsNullOrWhiteSpace(values[15]) && networkLookup.TryGetValue(values[15], out var netId) ? netId : null,
                            CreatedAt = DateTime.Now,
                            UpdatedAt = DateTime.Now
                        };
                        sites.Add(site);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في قراءة ملف CSV: {ex.Message}");
            }
            return sites;
        }

        public async System.Threading.Tasks.Task<IEnumerable<Site>> ImportSitesFromExcelAsync(string filePath)
        {
            var sites = new List<Site>();
            try
            {
                // تحميل جميع الشبكات مرة واحدة لتحسين الأداء
                var networkService = _serviceProvider.GetRequiredService<INetworkService>();
                var allNetworks = await networkService.GetAllAsync();
                var networkLookup = allNetworks.ToDictionary(n => n.Name, n => n.Id, StringComparer.OrdinalIgnoreCase);

                return await System.Threading.Tasks.Task.Run(() =>
                {
                    using var package = new ExcelPackage(new FileInfo(filePath));
                    var worksheet = package.Workbook.Worksheets.FirstOrDefault();
                    if (worksheet == null) return sites;

                    var rowCount = worksheet.Dimension?.Rows ?? 0;
                    if (rowCount <= 1) return sites;

                    // Start from row 2 (skip header)
                    for (int row = 2; row <= rowCount; row++)
                    {
                        var name = worksheet.Cells[row, 1].Text;
                        if (string.IsNullOrWhiteSpace(name)) continue;

                        var address = worksheet.Cells[row, 2].Text;
                        var gpsLng = double.TryParse(worksheet.Cells[row, 3].Text, out var lng) ? lng : (double?)null;
                        var gpsLat = double.TryParse(worksheet.Cells[row, 4].Text, out var lat) ? lat : (double?)null;
                        var powerSource = worksheet.Cells[row, 5].Text;
                        var batteryType = worksheet.Cells[row, 6].Text;
                        var batterySize = worksheet.Cells[row, 7].Text;
                        var baseType = worksheet.Cells[row, 8].Text;
                        var baseCount = int.TryParse(worksheet.Cells[row, 9].Text, out var bCount) ? bCount : (int?)null;
                        var boxType = worksheet.Cells[row, 10].Text;
                        var boxCount = int.TryParse(worksheet.Cells[row, 11].Text, out var boxCnt) ? boxCnt : (int?)null;
                        var networkCableType = worksheet.Cells[row, 12].Text;
                        var networkCableLength = int.TryParse(worksheet.Cells[row, 13].Text, out var netCableLen) ? netCableLen : (int?)null;
                        var powerCableType = worksheet.Cells[row, 14].Text;
                        var powerCableLength = int.TryParse(worksheet.Cells[row, 15].Text, out var powerCableLen) ? powerCableLen : (int?)null;
                        var networkName = worksheet.Cells[row, 16].Text;

                        var networkId = !string.IsNullOrWhiteSpace(networkName) && networkLookup.TryGetValue(networkName, out var id) ? id : null;

                        var site = new Site
                        {
                            Id = Guid.NewGuid().ToString(),
                            Name = name,
                            Address = address,
                            GpsLng = gpsLng,
                            GpsLat = gpsLat,
                            PowerSource = powerSource,
                            BatteryType = batteryType,
                            BatterySize = batterySize,
                            BaseType = baseType,
                            BaseCount = baseCount,
                            BoxType = boxType,
                            BoxCount = boxCount,
                            NetworkCableType = networkCableType,
                            NetworkCableLength = networkCableLength,
                            PowerCableType = powerCableType,
                            PowerCableLength = powerCableLength,
                            NetworkId = networkId,
                            CreatedAt = DateTime.Now,
                            UpdatedAt = DateTime.Now
                        };
                        sites.Add(site);
                    }
                    return sites;
                });
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في قراءة ملف Excel: {ex.Message}");
            }
        }

        public async System.Threading.Tasks.Task<IEnumerable<User>> ImportUsersFromCsvAsync(string filePath)
        {
            var users = new List<User>();
            try
            {
                var lines = await File.ReadAllLinesAsync(filePath, Encoding.UTF8);
                if (lines.Length <= 1) return users;

                // تحميل جميع الشبكات مرة واحدة لتحسين الأداء
                var networkService = _serviceProvider.GetRequiredService<INetworkService>();
                var allNetworks = await networkService.GetAllAsync();
                var networkLookup = allNetworks.ToDictionary(n => n.Name, n => n.Id, StringComparer.OrdinalIgnoreCase);

                // Skip header line
                for (int i = 1; i < lines.Length; i++)
                {
                    var line = lines[i];
                    if (string.IsNullOrWhiteSpace(line)) continue;

                    var values = ParseCsvLine(line);
                    // الأعمدة: اسم المستخدم,الاسم الكامل,الدور,الشبكة,البريد الإلكتروني
                    if (values.Length >= 3)
                    {
                        var networkName = values.Length > 3 ? values[3] : null;
                        var networkId = !string.IsNullOrWhiteSpace(networkName) && networkLookup.TryGetValue(networkName, out var id) ? id : null;

                        var user = new User
                        {
                            Id = Guid.NewGuid().ToString(),
                            Username = values[0],
                            Name = values[1],
                            Role = values[2],
                            NetworkId = networkId,
                            Email = values.Length > 4 ? values[4] : null,
                            Password = "DefaultPassword123!", // يجب تغييرها عند أول تسجيل دخول
                            IsActive = true,
                            CreatedAt = DateTime.Now,
                            UpdatedAt = DateTime.Now
                        };
                        users.Add(user);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في قراءة ملف CSV: {ex.Message}");
            }
            return users;
        }

        public async System.Threading.Tasks.Task<IEnumerable<User>> ImportUsersFromExcelAsync(string filePath)
        {
            var users = new List<User>();
            try
            {
                // تحميل جميع الشبكات مرة واحدة لتحسين الأداء
                var networkService = _serviceProvider.GetRequiredService<INetworkService>();
                var allNetworks = await networkService.GetAllAsync();
                var networkLookup = allNetworks.ToDictionary(n => n.Name, n => n.Id, StringComparer.OrdinalIgnoreCase);

                return await System.Threading.Tasks.Task.Run(() =>
                {
                    using var package = new ExcelPackage(new FileInfo(filePath));
                    var worksheet = package.Workbook.Worksheets.FirstOrDefault();
                    if (worksheet == null) return users;

                    var rowCount = worksheet.Dimension?.Rows ?? 0;
                    if (rowCount <= 1) return users;

                    // Start from row 2 (skip header)
                    for (int row = 2; row <= rowCount; row++)
                    {
                        var username = worksheet.Cells[row, 1].Text;
                        var name = worksheet.Cells[row, 2].Text;
                        var role = worksheet.Cells[row, 3].Text;

                        if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(name) || string.IsNullOrWhiteSpace(role))
                            continue;

                        var networkName = worksheet.Cells[row, 4].Text;
                        var email = worksheet.Cells[row, 5].Text;

                        var networkId = !string.IsNullOrWhiteSpace(networkName) && networkLookup.TryGetValue(networkName, out var id) ? id : null;

                        var user = new User
                        {
                            Id = Guid.NewGuid().ToString(),
                            Username = username,
                            Name = name,
                            Role = role,
                            NetworkId = networkId,
                            Email = email,
                            Password = "DefaultPassword123!", // يجب تغييرها عند أول تسجيل دخول
                            IsActive = true,
                            CreatedAt = DateTime.Now,
                            UpdatedAt = DateTime.Now
                        };
                        users.Add(user);
                    }
                    return users;
                });
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في قراءة ملف Excel: {ex.Message}");
            }
        }

        public async System.Threading.Tasks.Task<IEnumerable<Purchase>> ImportPurchasesFromCsvAsync(string filePath)

        {
            var purchases = new List<Purchase>();
            try
            {
                var lines = await File.ReadAllLinesAsync(filePath, Encoding.UTF8);
                if (lines.Length <= 1) return purchases; // No data or header only

                // تحميل جميع الشبكات مرة واحدة لتحسين الأداء
                var networkService = _serviceProvider.GetRequiredService<INetworkService>();
                var allNetworks = await networkService.GetAllAsync();
                var networkLookup = allNetworks.ToDictionary(n => n.Name, n => n.Id, StringComparer.OrdinalIgnoreCase);

                // Skip header line
                for (int i = 1; i < lines.Length; i++)
                {
                    var line = lines[i];
                    if (string.IsNullOrWhiteSpace(line)) continue;

                    var values = ParseCsvLine(line);
                    // الأعمدة: نوع الصنف,الكمية,الوحدة,السعر,التاريخ,المورد,الشبكة,الوصف,رقم الفاتورة,الفئة
                    if (values.Length >= 4)
                    {
                        // العثور على NetworkId بناءً على اسم الشبكة (محسن للأداء)
                        var networkName = values.Length > 6 ? values[6] : null;
                        var networkId = !string.IsNullOrWhiteSpace(networkName) && networkLookup.TryGetValue(networkName, out var id) ? id : null;

                        var purchase = new Purchase
                        {
                            Id = Guid.NewGuid().ToString(),
                            ItemType = values[0],
                            Quantity = int.TryParse(values[1], out var qty) ? qty : 1,
                            Unit = values.Length > 2 ? values[2] : null,
                            Price = TryParseDecimal(values.Length > 3 ? values[3] : "0"),
                            Date = values.Length > 4 && DateTime.TryParseExact(values[4], "dd/MM/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out var date) ? date : DateTime.Now,
                            Supplier = values.Length > 5 ? values[5] : null,
                            NetworkId = networkId,
                            Description = values.Length > 7 ? values[7] : null,
                            InvoiceNumber = values.Length > 8 ? values[8] : null,
                            Category = values.Length > 9 ? values[9] : null
                        };
                        purchases.Add(purchase);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في قراءة ملف CSV: {ex.Message}");
            }
            return purchases;
        }

        public async System.Threading.Tasks.Task<IEnumerable<Purchase>> ImportPurchasesFromExcelAsync(string filePath)
        {
            var purchases = new List<Purchase>();
            try
            {
                // تحميل جميع الشبكات مرة واحدة لتحسين الأداء
                var networkService = _serviceProvider.GetRequiredService<INetworkService>();
                var allNetworks = await networkService.GetAllAsync();
                var networkLookup = allNetworks.ToDictionary(n => n.Name, n => n.Id, StringComparer.OrdinalIgnoreCase);

                return await System.Threading.Tasks.Task.Run(() =>
                {
                    using var package = new ExcelPackage(new FileInfo(filePath));
                    var worksheet = package.Workbook.Worksheets.FirstOrDefault();
                    if (worksheet == null) return purchases;

                    var rowCount = worksheet.Dimension?.Rows ?? 0;
                    if (rowCount <= 1) return purchases; // No data or header only

                    // Start from row 2 (skip header)
                    for (int row = 2; row <= rowCount; row++)
                    {
                        var itemType = worksheet.Cells[row, 1].Text;
                        var quantityText = worksheet.Cells[row, 2].Text;
                        var unit = worksheet.Cells[row, 3].Text;
                        var priceText = worksheet.Cells[row, 4].Text;
                        var dateText = worksheet.Cells[row, 5].Text;
                        var supplier = worksheet.Cells[row, 6].Text;
                        var networkName = worksheet.Cells[row, 7].Text; // اسم الشبكة
                        var description = worksheet.Cells[row, 8].Text;
                        var invoiceNumber = worksheet.Cells[row, 9].Text;
                        var category = worksheet.Cells[row, 10].Text;

                        if (string.IsNullOrWhiteSpace(itemType)) continue;

                        // العثور على NetworkId بناءً على اسم الشبكة (محسن للأداء)
                        var networkId = !string.IsNullOrWhiteSpace(networkName) && networkLookup.TryGetValue(networkName, out var id) ? id : null;

                        var purchase = new Purchase
                        {
                            Id = Guid.NewGuid().ToString(),
                            ItemType = itemType,
                            Quantity = int.TryParse(quantityText, out var qty) ? qty : 1,
                            Unit = unit,
                            Price = TryParseDecimal(priceText),
                            Date = DateTime.TryParseExact(dateText, "dd/MM/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out var date) ? date : DateTime.Now,
                            Supplier = supplier,
                            NetworkId = networkId,
                            Description = description,
                            InvoiceNumber = invoiceNumber,
                            Category = category
                        };
                        purchases.Add(purchase);
                    }
                    return purchases;
                });
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في قراءة ملف Excel: {ex.Message}");
            }
        }

        // دالة مساعدة لتحويل النص إلى رقم عشري
        private static decimal TryParseDecimal(string? value)
        {
            if (string.IsNullOrWhiteSpace(value)) return 0;

            // إزالة رموز العملات المختلفة بما في ذلك الريال اليمني
            value = value.Replace("ر.ي", "")
                        .Replace("ر.س", "")
                        .Replace("SAR", "")
                        .Replace("YER", "")
                        .Replace("$", "")
                        .Replace("USD", "")
                        .Replace(",", "")
                        .Trim();

            return decimal.TryParse(value, NumberStyles.Any, CultureInfo.InvariantCulture, out var result) ? result : 0;
        }

        // دالة مساعدة للعثور على NetworkId بناءً على اسم الشبكة
        private async Task<string?> FindNetworkIdByNameAsync(string? networkName)
        {
            if (string.IsNullOrWhiteSpace(networkName)) return null;

            try
            {
                var networkService = _serviceProvider.GetRequiredService<INetworkService>();
                var networks = await networkService.GetAllAsync();
                var network = networks.FirstOrDefault(n =>
                    string.Equals(n.Name, networkName, StringComparison.OrdinalIgnoreCase));
                return network?.Id;
            }
            catch
            {
                return null;
            }
        }

        public async System.Threading.Tasks.Task<IEnumerable<Inventory>> ImportInventoryFromCsvAsync(string filePath)
        {
            var result = new List<Inventory>();
            var lines = await File.ReadAllLinesAsync(filePath, Encoding.UTF8);
            if (lines.Length < 2) return result;

            // توقع أن أول سطر هو العناوين
            for (int i = 1; i < lines.Length; i++)
            {
                var fields = ParseCsvLine(lines[i]);
                if (fields.Length < 11) continue;

                var item = new Inventory
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = fields[0]?.Trim() ?? string.Empty,
                    Category = fields[1]?.Trim() ?? string.Empty,
                    Quantity = int.TryParse(fields[2], out var q) ? q : 0,
                    Unit = fields[3]?.Trim() ?? string.Empty,
                    MinimumStock = int.TryParse(fields[4], out var min) ? min : null,
                    MaximumStock = int.TryParse(fields[5], out var max) ? max : null,
                    UnitPrice = TryParseDecimal(fields.Length > 6 ? fields[6] : "0"),
                    Location = fields[7]?.Trim(),
                    Supplier = fields[8]?.Trim(),
                    Description = fields[10]?.Trim(),
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                };
                // الشبكة (Network) بالاسم فقط، NetworkId يربط لاحقًا حسب الحاجة
                // يمكن ربط NetworkId هنا إذا كان لديك قائمة بالشبكات
                result.Add(item);
            }
            return result;
        }

        public System.Threading.Tasks.Task<IEnumerable<Inventory>> ImportInventoryFromExcelAsync(string filePath)
        {
            var result = new List<Inventory>();
            using var package = new ExcelPackage(new FileInfo(filePath));
            var worksheet = package.Workbook.Worksheets.FirstOrDefault();
            if (worksheet == null) return System.Threading.Tasks.Task.FromResult<IEnumerable<Inventory>>(result);

            int row = 2;
            while (true)
            {
                var name = worksheet.Cells[row, 1].Text?.Trim();
                if (string.IsNullOrWhiteSpace(name)) break;

                var item = new Inventory
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = name,
                    Category = worksheet.Cells[row, 2].Text?.Trim() ?? string.Empty,
                    Quantity = int.TryParse(worksheet.Cells[row, 3].Text, out var q) ? q : 0,
                    Unit = worksheet.Cells[row, 4].Text?.Trim() ?? string.Empty,
                    MinimumStock = int.TryParse(worksheet.Cells[row, 5].Text, out var min) ? min : (int?)null,
                    MaximumStock = int.TryParse(worksheet.Cells[row, 6].Text, out var max) ? max : (int?)null,
                    UnitPrice = decimal.TryParse(worksheet.Cells[row, 7].Text, NumberStyles.Any, CultureInfo.CurrentCulture, out var up) ? up : (decimal?)null,
                    Location = worksheet.Cells[row, 8].Text?.Trim(),
                    Supplier = worksheet.Cells[row, 9].Text?.Trim(),
                    Description = worksheet.Cells[row, 11].Text?.Trim(),
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                };
                // الشبكة (Network) بالاسم فقط، NetworkId يربط لاحقًا حسب الحاجة
                result.Add(item);
                row++;
            }
            return System.Threading.Tasks.Task.FromResult<IEnumerable<Inventory>>(result);
        }

        #endregion

        #region Utility Methods

        public async System.Threading.Tasks.Task<string> GetSaveFilePathAsync(string defaultFileName, string filter)
        {
            await System.Threading.Tasks.Task.Delay(1); // Make it async

            var saveFileDialog = new SaveFileDialog
            {
                FileName = defaultFileName,
                Filter = filter,
                DefaultExt = filter.Contains("xlsx") ? "xlsx" : "csv"
            };

            return saveFileDialog.ShowDialog() == true ? saveFileDialog.FileName : string.Empty;
        }

        public async System.Threading.Tasks.Task<string> GetOpenFilePathAsync(string filter)
        {
            await System.Threading.Tasks.Task.Delay(1); // Make it async

            var openFileDialog = new OpenFileDialog
            {
                Filter = filter,
                Multiselect = false
            };

            return openFileDialog.ShowDialog() == true ? openFileDialog.FileName : string.Empty;
        }

        #endregion

        #region Helper Methods

        private string[] ParseCsvLine(string line)
        {
            var result = new List<string>();
            var current = new StringBuilder();
            bool inQuotes = false;

            for (int i = 0; i < line.Length; i++)
            {
                char c = line[i];

                if (c == '"')
                {
                    if (inQuotes && i + 1 < line.Length && line[i + 1] == '"')
                    {
                        // Escaped quote
                        current.Append('"');
                        i++; // Skip next quote
                    }
                    else
                    {
                        inQuotes = !inQuotes;
                    }
                }
                else if (c == ',' && !inQuotes)
                {
                    result.Add(current.ToString());
                    current.Clear();
                }
                else
                {
                    current.Append(c);
                }
            }

            result.Add(current.ToString());
            return result.ToArray();
        }



        #endregion
    }
}
