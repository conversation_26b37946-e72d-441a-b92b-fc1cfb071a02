using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using NetworkManagement.Data;
using NetworkManagement.Models;

namespace NetworkManagement.Services
{
    public class PurchaseService : IPurchaseService
    {
        private readonly NetworkDbContext _context;

        public PurchaseService(NetworkDbContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
        }

        public async Task<IEnumerable<Purchase>> GetAllAsync(string? networkFilter = null)
        {
            return await BuildQuery(networkFilter).ToListAsync();
        }

        public async Task<Purchase?> GetByIdAsync(string id)
        {
            if (string.IsNullOrEmpty(id))
                return null;

            if (_context.Purchases == null)
                throw new InvalidOperationException("DbSet<Purchase> is null");

            return await _context.Purchases
                .Include(p => p.Network)
                .FirstOrDefaultAsync(p => p.Id == id);
        }

        public async Task<Purchase> CreateAsync(Purchase purchase)
        {
            if (purchase == null)
                throw new ArgumentNullException(nameof(purchase));

            if (_context.Purchases == null)
                throw new InvalidOperationException("DbSet<Purchase> is null");

            purchase.Id = Guid.NewGuid().ToString();
            purchase.CreatedAt = DateTime.Now;
            purchase.UpdatedAt = DateTime.Now;

            _context.Purchases.Add(purchase);
            await _context.SaveChangesAsync();
            return purchase;
        }

        public async Task<Purchase> UpdateAsync(Purchase purchase)
        {
            if (purchase == null)
                throw new ArgumentNullException(nameof(purchase));

            if (_context.Purchases == null)
                throw new InvalidOperationException("DbSet<Purchase> is null");

            purchase.UpdatedAt = DateTime.Now;
            _context.Purchases.Update(purchase);
            await _context.SaveChangesAsync();
            return purchase;
        }

        public async Task<bool> DeleteAsync(string id)
        {
            if (string.IsNullOrEmpty(id))
                return false;

            if (_context.Purchases == null)
                throw new InvalidOperationException("DbSet<Purchase> is null");

            var purchase = await _context.Purchases.FindAsync(id);
            if (purchase == null)
                return false;

            _context.Purchases.Remove(purchase);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<IEnumerable<Purchase>> SearchAsync(string searchTerm, string? networkFilter = null)
        {
            var query = BuildQuery(networkFilter);

            if (!string.IsNullOrEmpty(searchTerm))
            {
                query = query.Where(p =>
                    p.ItemType.Contains(searchTerm) ||
                    (p.Supplier ?? "").Contains(searchTerm) ||
                    (p.Description ?? "").Contains(searchTerm) ||
                    (p.InvoiceNumber ?? "").Contains(searchTerm) ||
                    (p.Category ?? "").Contains(searchTerm));
            }

            return await query.ToListAsync();
        }

        public async Task<decimal> GetTotalSpentAsync(string? networkFilter = null, DateTime? startDate = null, DateTime? endDate = null)
        {
            if (_context.Purchases == null)
                throw new InvalidOperationException("DbSet<Purchase> is null");

            var query = _context.Purchases.AsQueryable();

            if (!string.IsNullOrEmpty(networkFilter))
            {
                query = query.Where(p => p.NetworkId == networkFilter);
            }

            if (startDate.HasValue)
            {
                query = query.Where(p => p.Date >= startDate.Value);
            }

            if (endDate.HasValue)
            {
                query = query.Where(p => p.Date <= endDate.Value);
            }

            return await query.SumAsync(p => p.Price * p.Quantity);
        }

        public async Task<IEnumerable<Purchase>> GetFilteredAsync(
            string? networkFilter = null,
            string? searchText = null,
            string? category = null,
            DateTime? startDate = null,
            DateTime? endDate = null)
        {
            var query = BuildQuery(networkFilter);

            // تطبيق فلتر البحث
            if (!string.IsNullOrEmpty(searchText))
            {
                query = query.Where(p =>
                    p.ItemType.Contains(searchText) ||
                    (p.Supplier ?? "").Contains(searchText) ||
                    (p.Description ?? "").Contains(searchText) ||
                    (p.InvoiceNumber ?? "").Contains(searchText) ||
                    (p.Category ?? "").Contains(searchText));
            }

            // تطبيق فلتر الفئة
            if (!string.IsNullOrEmpty(category) && category != "الكل")
            {
                query = query.Where(p => p.Category == category);
            }

            // تطبيق فلتر التاريخ
            if (startDate.HasValue)
            {
                query = query.Where(p => p.Date >= startDate.Value);
            }

            if (endDate.HasValue)
            {
                query = query.Where(p => p.Date <= endDate.Value);
            }

            return await query.ToListAsync();
        }

        /// <summary>
        /// بناء الاستعلام الأساسي مع تطبيق فلتر الشبكة
        /// </summary>
        private IQueryable<Purchase> BuildQuery(string? networkFilter = null)
        {
            if (_context.Purchases == null)
                throw new InvalidOperationException("DbSet<Purchase> is null");

            var query = _context.Purchases.Include(p => p.Network).AsQueryable();

            if (!string.IsNullOrEmpty(networkFilter))
            {
                query = query.Where(p => p.NetworkId == networkFilter);
            }

            return query.OrderByDescending(p => p.Date);
        }
    }
}

