<Window x:Class="NetworkManagement.Views.MapSelectionDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="اختيار الموقع من الخريطة"
        Height="600" Width="800"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize"
        FlowDirection="RightToLeft"
        Background="{DynamicResource MaterialDesignPaper}"
        Foreground="{DynamicResource MaterialDesignBody}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:Card Grid.Row="0" Margin="10" Padding="15">
            <StackPanel>
                <TextBlock Text="اختيار الموقع من الخريطة" 
                           Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                           HorizontalAlignment="Center"
                           Margin="0,0,0,10"/>
                
                <TextBlock Text="انقر على الخريطة لتحديد الموقع المطلوب"
                           Style="{StaticResource MaterialDesignBody2TextBlock}"
                           HorizontalAlignment="Center"
                           Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                
                <!-- Current Coordinates Display -->
                <Grid Margin="0,10,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0" Orientation="Horizontal" HorizontalAlignment="Center">
                        <materialDesign:PackIcon Kind="Latitude" VerticalAlignment="Center" Margin="0,0,5,0"/>
                        <TextBlock Text="خط العرض:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                        <TextBlock Text="{Binding SelectedLatitude, StringFormat=F6}" 
                                   VerticalAlignment="Center"
                                   FontWeight="Bold"/>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center">
                        <materialDesign:PackIcon Kind="Longitude" VerticalAlignment="Center" Margin="0,0,5,0"/>
                        <TextBlock Text="خط الطول:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                        <TextBlock Text="{Binding SelectedLongitude, StringFormat=F6}" 
                                   VerticalAlignment="Center"
                                   FontWeight="Bold"/>
                    </StackPanel>
                </Grid>
            </StackPanel>
        </materialDesign:Card>

        <!-- Map Container -->
        <materialDesign:Card Grid.Row="1" Margin="10">
            <StackPanel Margin="20" HorizontalAlignment="Center" VerticalAlignment="Center">
                <materialDesign:PackIcon Kind="MapMarker"
                                         Width="100" Height="100"
                                         HorizontalAlignment="Center"
                                         Foreground="{DynamicResource PrimaryHueMidBrush}"
                                         Margin="0,0,0,20"/>

                <TextBlock Text="إدخال الإحداثيات يدوياً"
                           Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                           HorizontalAlignment="Center"
                           Margin="0,0,0,20"/>

                <Grid Width="400">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <TextBox Grid.Column="0"
                             materialDesign:HintAssist.Hint="خط العرض"
                             materialDesign:HintAssist.IsFloating="True"
                             Text="{Binding SelectedLatitude, UpdateSourceTrigger=PropertyChanged}"
                             Margin="0,0,10,0"/>

                    <TextBox Grid.Column="1"
                             materialDesign:HintAssist.Hint="خط الطول"
                             materialDesign:HintAssist.IsFloating="True"
                             Text="{Binding SelectedLongitude, UpdateSourceTrigger=PropertyChanged}"
                             Margin="10,0,0,0"/>
                </Grid>

                <TextBlock Text="يمكنك الحصول على الإحداثيات من Google Maps أو أي خدمة خرائط أخرى"
                           Style="{StaticResource MaterialDesignBody2TextBlock}"
                           HorizontalAlignment="Center"
                           TextWrapping="Wrap"
                           Foreground="{DynamicResource MaterialDesignBodyLight}"
                           Margin="0,20,0,0"/>
            </StackPanel>
        </materialDesign:Card>

        <!-- Action Buttons -->
        <materialDesign:Card Grid.Row="2" Margin="10" Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Search Box -->
                <TextBox Grid.Column="0"
                         materialDesign:HintAssist.Hint="البحث عن موقع (مثال: صنعاء، اليمن)"
                         materialDesign:HintAssist.IsFloating="True"
                         Text="{Binding SearchQuery, UpdateSourceTrigger=PropertyChanged}"
                         Margin="0,0,10,0">
                    <TextBox.InputBindings>
                        <KeyBinding Key="Enter" Command="{Binding SearchLocationCommand}"/>
                    </TextBox.InputBindings>
                </TextBox>

                <Button Grid.Column="1"
                        Content="بحث"
                        Command="{Binding SearchLocationCommand}"
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        Margin="0,0,10,0"/>

                <Button Grid.Column="2"
                        Content="موافق"
                        Command="{Binding ConfirmSelectionCommand}"
                        Style="{StaticResource MaterialDesignRaisedButton}"
                        IsDefault="True"
                        Margin="0,0,10,0"/>

                <Button Grid.Column="3"
                        Content="إلغاء"
                        Command="{Binding CancelCommand}"
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        IsCancel="True"/>
            </Grid>
        </materialDesign:Card>
    </Grid>
</Window>
