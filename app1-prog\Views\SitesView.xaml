<UserControl x:Class="NetworkManagement.Views.SitesView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header and Actions - تصميم مضغوط -->
        <materialDesign:Card Grid.Row="0" Margin="2" Padding="10">
            <StackPanel>
                <!-- Title and Search Row -->
                <Grid Margin="5">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- Title Section -->
                    <StackPanel Grid.Column="0" VerticalAlignment="Center" Margin="5">
                        <TextBlock Text="إدارة المواقع"
                                  FontSize="16" FontWeight="Medium" Margin="2"/>
                        <TextBlock Text="إضافة وتعديل وحذف مواقع الشبكة • إدارة معلومات المواقع"
                                  FontSize="11" Opacity="0.68"/>
                    </StackPanel>

                    <!-- Search Box -->
                    <TextBox Grid.Column="1"
                            Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                            materialDesign:HintAssist.Hint="البحث في المواقع..."
                            Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                            Height="32" Margin="10,0">
                        <TextBox.InputBindings>
                            <KeyBinding Key="Enter" Command="{Binding SearchSitesCommand}"/>
                        </TextBox.InputBindings>
                    </TextBox>
                </Grid>

                <!-- Action Buttons - تصميم مضغوط -->
                <WrapPanel HorizontalAlignment="Right" Margin="5">
                    <Button Command="{Binding SearchSitesCommand}"
                           Style="{StaticResource MaterialDesignIconButton}"
                           ToolTip="بحث" Width="32" Height="32" Margin="2">
                        <materialDesign:PackIcon Kind="Magnify" Width="16" Height="16"/>
                    </Button>

                    <Button Command="{Binding LoadSitesCommand}"
                           Style="{StaticResource MaterialDesignIconButton}"
                           ToolTip="تحديث" Width="32" Height="32" Margin="2">
                        <materialDesign:PackIcon Kind="Refresh" Width="16" Height="16"/>
                    </Button>

                    <Button Command="{Binding AddSiteCommand}"
                           Style="{StaticResource MaterialDesignRaisedButton}"
                           Margin="3" Height="32"
                           Visibility="{Binding CanAddSites, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Plus" Width="14" Height="14" Margin="2"/>
                            <TextBlock Text="إضافة موقع" FontSize="12"/>
                        </StackPanel>
                    </Button>
                </WrapPanel>
            </StackPanel>
        </materialDesign:Card>

        <!-- Sites List - استغلال أقصى مساحة -->
        <Grid Grid.Row="1" Margin="2">
            <!-- Loading Indicator -->
            <ProgressBar IsIndeterminate="True"
                        Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                        VerticalAlignment="Top" Height="4" Panel.ZIndex="1"/>

            <!-- Data Grid - استغلال أقصى مساحة -->
            <DataGrid x:Name="SitesDataGrid"
                     ItemsSource="{Binding Sites}"
                     SelectedItem="{Binding SelectedSite}"
                     AutoGenerateColumns="False"
                     AlternatingRowBackground="Transparent"
                     GridLinesVisibility="Horizontal"
                     HeadersVisibility="Column"
                     CanUserAddRows="False"
                     CanUserDeleteRows="False"
                     CanUserSortColumns="True"
                     CanUserReorderColumns="True"
                     CanUserResizeColumns="True"
                     IsReadOnly="True"
                     SelectionMode="Extended"
                     SelectionUnit="FullRow"
                     EnableRowVirtualization="True"
                     EnableColumnVirtualization="True"
                     VirtualizingPanel.VirtualizationMode="Recycling"
                     VirtualizingPanel.IsVirtualizing="True"
                     ScrollViewer.CanContentScroll="True"
                     Margin="0"
                     RowHeight="50"
                     ColumnHeaderHeight="45">

                <!-- إزالة Focus وSelection Borders -->
                <DataGrid.CellStyle>
                    <Style TargetType="DataGridCell">
                        <Setter Property="BorderThickness" Value="0"/>
                        <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="DataGridCell">
                                    <Border Background="{TemplateBinding Background}"
                                           BorderThickness="0"
                                           Padding="8,0">
                                        <ContentPresenter VerticalAlignment="Center"/>
                                    </Border>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Style>
                </DataGrid.CellStyle>

                <DataGrid.RowStyle>
                    <Style TargetType="DataGridRow">
                        <Setter Property="Background" Value="Transparent"/>
                        <Setter Property="BorderThickness" Value="0"/>
                        <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
                        <Setter Property="Margin" Value="0,2"/>
                    </Style>
                </DataGrid.RowStyle>

                    <DataGrid.Columns>
                        <!-- اسم الموقع -->
                        <DataGridTextColumn Header="اسم الموقع" Binding="{Binding Name}" Width="180">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontSize" Value="14"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="FontWeight" Value="Medium"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- العنوان -->
                        <DataGridTextColumn Header="العنوان" Binding="{Binding Address}" Width="250">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontSize" Value="14"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- الهاتف -->
                        <DataGridTextColumn Header="الهاتف" Binding="{Binding Phone}" Width="140">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontSize" Value="14"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- مصدر الطاقة -->
                        <DataGridTextColumn Header="مصدر الطاقة" Binding="{Binding PowerSource}" Width="140">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontSize" Value="14"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- سعة التخزين -->
                        <DataGridTextColumn Header="سعة التخزين" Binding="{Binding StorageCapacity}" Width="120">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontSize" Value="14"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- الاستهلاك اليومي -->
                        <DataGridTextColumn Header="الاستهلاك اليومي" Binding="{Binding DailyConsumption}" Width="140">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontSize" Value="14"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- الشبكة -->
                        <DataGridTextColumn Header="الشبكة" Binding="{Binding Network.Name}" Width="120">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontSize" Value="14"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- تاريخ الإنشاء -->
                        <DataGridTextColumn Header="تاريخ الإنشاء" Binding="{Binding CreatedAt, StringFormat=dd/MM/yyyy}" Width="140">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontSize" Value="14"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- Actions Column -->
                        <DataGridTemplateColumn Header="الإجراءات" Width="120">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <Button Command="{Binding DataContext.EditSiteCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                               CommandParameter="{Binding}"
                                               Style="{StaticResource MaterialDesignIconButton}"
                                               ToolTip="تعديل الموقع" Width="32" Height="32"
                                               Visibility="{Binding Converter={StaticResource CanEditDataConverter}}"
                                               Margin="2">
                                            <materialDesign:PackIcon Kind="Edit" Width="16" Height="16"/>
                                        </Button>
                                        <Button Command="{Binding DataContext.DeleteSiteCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                               CommandParameter="{Binding}"
                                               Style="{StaticResource MaterialDesignIconButton}"
                                               ToolTip="حذف الموقع" Width="32" Height="32"
                                               Visibility="{Binding Converter={StaticResource CanDeleteDataConverter}}"
                                               Margin="2">
                                            <materialDesign:PackIcon Kind="Delete" Width="16" Height="16" Foreground="Red"/>
                                        </Button>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>

            <!-- Empty State -->
            <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center"
                       Visibility="{Binding Sites.Count, Converter={StaticResource ZeroToVisibilityConverter}}">
                <materialDesign:PackIcon Kind="MapMarker" Width="64" Height="64"
                                       Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                <TextBlock Text="لا توجد مواقع"
                          FontSize="16"
                          Foreground="{DynamicResource MaterialDesignBodyLight}"
                          HorizontalAlignment="Center" Margin="0,10,0,0"/>
                <TextBlock Text="انقر على 'إضافة موقع' لإضافة الموقع الأول"
                          FontSize="12"
                          Foreground="{DynamicResource MaterialDesignBodyLight}"
                          HorizontalAlignment="Center"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>
