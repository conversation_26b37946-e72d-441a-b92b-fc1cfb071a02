<UserControl x:Class="NetworkManagement.Views.InventoryView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Page Header -->
        <TextBlock Grid.Row="0" Text="إدارة المخزون" Style="{StaticResource PageHeaderStyle}" Margin="15,10,15,5"/>

        <!-- Statistics Cards - مضغوطة -->
        <Grid Grid.Row="1" Margin="15,5,15,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Total Items Card - مضغوطة -->
            <materialDesign:Card Grid.Column="0" Padding="10" Margin="2">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                        <materialDesign:PackIcon Kind="Package" Width="18" Height="18"
                                               Foreground="{StaticResource StatisticsBlueBrush}" VerticalAlignment="Center"/>
                        <TextBlock Text="إجمالي العناصر" FontSize="12" FontWeight="Medium"
                                  VerticalAlignment="Center" Margin="5,0,0,0"/>
                    </StackPanel>
                    <TextBlock Text="{Binding TotalItems}" FontSize="16" FontWeight="Bold"
                              Foreground="{StaticResource StatisticsBlueBrush}"/>
                    <TextBlock Text="{Binding TotalQuantity, StringFormat='إجمالي الكمية: {0}'}" FontSize="10"
                              Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- Low Stock Alert Card - مضغوطة -->
            <materialDesign:Card Grid.Column="1" Padding="10" Margin="2">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                        <materialDesign:PackIcon Kind="AlertCircle" Width="18" Height="18"
                                               Foreground="#F44336" VerticalAlignment="Center"/>
                        <TextBlock Text="مخزون منخفض" FontSize="12" FontWeight="Medium"
                                  VerticalAlignment="Center" Margin="5,0,0,0"/>
                    </StackPanel>
                    <TextBlock Text="{Binding LowStockCount}" FontSize="16" FontWeight="Bold"
                              Foreground="#F44336"/>
                    <TextBlock Text="عنصر يحتاج إعادة تموين" FontSize="10"
                              Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- Total Value Card - مضغوطة -->
            <materialDesign:Card Grid.Column="2" Padding="10" Margin="2">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                        <materialDesign:PackIcon Kind="CurrencyUsd" Width="18" Height="18"
                                               Foreground="#4CAF50" VerticalAlignment="Center"/>
                        <TextBlock Text="القيمة الإجمالية" FontSize="12" FontWeight="Medium"
                                  VerticalAlignment="Center" Margin="5,0,0,0"/>
                    </StackPanel>
                    <TextBlock Text="{Binding TotalValueDisplay}" FontSize="16" FontWeight="Bold"
                              Foreground="#4CAF50"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- Quick Actions Card - مضغوطة -->
            <materialDesign:Card Grid.Column="3" Padding="8" Margin="2">
                <StackPanel>
                    <TextBlock Text="إجراءات سريعة" FontSize="12" FontWeight="Medium" Margin="0,0,0,8"/>
                    <Button Content="إضافة عنصر" Command="{Binding AddItemCommand}"
                           Style="{StaticResource MaterialDesignRaisedButton}" Height="28" FontSize="11"
                           Margin="0,0,0,3" HorizontalAlignment="Stretch"
                           Visibility="{Binding CanAddItems, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                    <Button x:Name="ExportActionsButton" Content="تصدير/استيراد" Height="28" FontSize="11"
                           Style="{StaticResource MaterialDesignOutlinedButton}" HorizontalAlignment="Stretch"
                           Click="ExportActionsButton_Click">
                        <Button.ContextMenu>
                            <ContextMenu>
                                <MenuItem Header="تصدير المخزون المفلتر" Command="{Binding ExportInventoryCommand}"
                                         Visibility="{Binding CanExportInventory, Converter={StaticResource BooleanToVisibilityConverter}}">
                                    <MenuItem.Icon>
                                        <materialDesign:PackIcon Kind="Export"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                                <Separator/>
                                <MenuItem Header="استيراد مخزون" Command="{Binding ImportInventoryCommand}"
                                         Visibility="{Binding CanImportInventory, Converter={StaticResource BooleanToVisibilityConverter}}">
                                    <MenuItem.Icon>
                                        <materialDesign:PackIcon Kind="Import"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                            </ContextMenu>
                        </Button.ContextMenu>
                    </Button>
                </StackPanel>
            </materialDesign:Card>
        </Grid>

        <!-- Filters - مضغوطة -->
        <materialDesign:Card Grid.Row="2" Margin="15,5,15,10" Padding="12">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Search -->
                <TextBox Grid.Column="0" materialDesign:HintAssist.Hint="البحث في المخزون..."
                        materialDesign:HintAssist.IsFloating="False" Height="35"
                        Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,10,0">
                    <TextBox.InputBindings>
                        <KeyBinding Key="Enter" Command="{Binding SearchInventoryCommand}"/>
                    </TextBox.InputBindings>
                </TextBox>

                <!-- Network Filter -->
                <ComboBox Grid.Column="1" materialDesign:HintAssist.Hint="الشبكة" Height="35"
                         ItemsSource="{Binding AvailableNetworks}" SelectedItem="{Binding SelectedNetwork}"
                         DisplayMemberPath="Name" Width="120" Margin="0,0,10,0"
                         Visibility="{Binding CanViewAllNetworks, Converter={StaticResource BooleanToVisibilityConverter}}"/>

                <!-- Category Filter -->
                <ComboBox Grid.Column="2" materialDesign:HintAssist.Hint="الفئة" Height="35"
                         ItemsSource="{Binding Categories}" SelectedItem="{Binding SelectedCategory}"
                         Width="100" Margin="0,0,10,0"/>

                <!-- Action Buttons -->
                <StackPanel Grid.Column="3" Orientation="Horizontal">
                    <Button Command="{Binding SearchInventoryCommand}" Height="35" Width="70"
                           Style="{StaticResource MaterialDesignOutlinedButton}" Margin="0,0,5,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Magnify" Width="14" Height="14" Margin="0,0,3,0"/>
                            <TextBlock Text="بحث" FontSize="11"/>
                        </StackPanel>
                    </Button>
                    <Button Command="{Binding LoadInventoryCommand}" Height="35" Width="70"
                           Style="{StaticResource MaterialDesignOutlinedButton}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Refresh" Width="14" Height="14" Margin="0,0,3,0"/>
                            <TextBlock Text="تحديث" FontSize="11"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Data Grid Container - استغلال أقصى مساحة -->
        <Grid Grid.Row="3" Margin="15,5,15,15">
            <!-- Loading Indicator -->
            <ProgressBar IsIndeterminate="True"
                        Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                        VerticalAlignment="Top" Height="3"/>

            <!-- Data Grid - استغلال أقصى مساحة -->
            <DataGrid x:Name="InventoryDataGrid"
                     ItemsSource="{Binding InventoryItems}"
                     SelectedItem="{Binding SelectedItem}"
                     AutoGenerateColumns="False"
                     AlternatingRowBackground="Transparent"
                     GridLinesVisibility="Horizontal"
                     HeadersVisibility="Column"
                     CanUserAddRows="False"
                     CanUserDeleteRows="False"
                     CanUserSortColumns="True"
                     CanUserReorderColumns="True"
                     CanUserResizeColumns="True"
                     IsReadOnly="True"
                     SelectionMode="Extended"
                     SelectionUnit="FullRow"
                     EnableRowVirtualization="True"
                     EnableColumnVirtualization="True"
                     VirtualizingPanel.VirtualizationMode="Recycling"
                     VirtualizingPanel.IsVirtualizing="True"
                     ScrollViewer.CanContentScroll="True"
                     Margin="0"
                     RowHeight="55"
                     ColumnHeaderHeight="50"
                     FontSize="13">

                    <!-- إزالة Focus وSelection Borders -->
                    <DataGrid.CellStyle>
                        <Style TargetType="DataGridCell">
                            <Setter Property="BorderThickness" Value="0"/>
                            <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="DataGridCell">
                                        <Border Background="{TemplateBinding Background}"
                                               BorderThickness="0"
                                               Padding="8,0">
                                            <ContentPresenter VerticalAlignment="Center"/>
                                        </Border>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </DataGrid.CellStyle>

                    <DataGrid.RowStyle>
                        <Style TargetType="DataGridRow">
                            <Setter Property="Background" Value="Transparent"/>
                            <Setter Property="BorderThickness" Value="0"/>
                            <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
                            <Setter Property="Margin" Value="0,2"/>
                        </Style>
                    </DataGrid.RowStyle>
                        
                        <DataGrid.Columns>
                            <!-- 1. اسم العنصر -->
                            <DataGridTemplateColumn Header="اسم العنصر" Width="180" SortMemberPath="Name">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding Name}" FontSize="14" FontWeight="Medium"
                                                  VerticalAlignment="Center" Margin="8,0"/>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!-- 2. الفئة -->
                            <DataGridTemplateColumn Header="الفئة" Width="120" SortMemberPath="Category">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding CategoryDisplay}" FontSize="14"
                                                  VerticalAlignment="Center" Margin="8,0"/>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!-- 3. الكمية -->
                            <DataGridTemplateColumn Header="الكمية" Width="120" SortMemberPath="Quantity">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding QuantityDisplay}" FontSize="14" FontWeight="Medium"
                                                  VerticalAlignment="Center" Margin="8,0"
                                                  Foreground="{StaticResource StatisticsBlueBrush}"/>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!-- 4. الحد الأدنى -->
                            <DataGridTemplateColumn Header="الحد الأدنى" Width="120" SortMemberPath="MinimumStock">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding MinimumStock}" FontSize="14"
                                                  VerticalAlignment="Center" Margin="8,0"/>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!-- 5. سعر الوحدة -->
                            <DataGridTemplateColumn Header="سعر الوحدة" Width="140" SortMemberPath="UnitPrice">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding UnitPriceDisplay}" FontSize="14"
                                                  VerticalAlignment="Center" Margin="8,0"/>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!-- 6. القيمة الإجمالية -->
                            <DataGridTemplateColumn Header="القيمة الإجمالية" Width="150" SortMemberPath="TotalValue">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding TotalValueDisplay}" FontSize="14" FontWeight="Bold"
                                                  VerticalAlignment="Center" Margin="8,0"
                                                  Foreground="{StaticResource StatisticsGreenBrush}"/>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!-- 7. الموقع -->
                            <DataGridTemplateColumn Header="الموقع" Width="140" SortMemberPath="Location">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding LocationDisplay}" FontSize="14"
                                                  VerticalAlignment="Center" Margin="8,0"/>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!-- 8. الشبكة -->
                            <DataGridTemplateColumn Header="الشبكة" Width="140" SortMemberPath="NetworkName">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding NetworkName}" FontSize="14"
                                                  VerticalAlignment="Center" Margin="8,0"/>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!-- 9. حالة المخزون -->
                            <DataGridTemplateColumn Header="حالة المخزون" Width="130" SortMemberPath="StockStatus">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Border CornerRadius="12" Padding="8,4" HorizontalAlignment="Center"
                                               Background="{Binding StockStatusColor}">
                                            <TextBlock Text="{Binding StockStatusDisplay}"
                                                      Foreground="White"
                                                      FontSize="12"
                                                      FontWeight="Medium"/>
                                        </Border>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!-- 10. الإجراءات -->
                            <DataGridTemplateColumn Header="الإجراءات" Width="160">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                            <Button Content="تحرير" FontSize="12"
                                                   Command="{Binding DataContext.EditItemCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                   Style="{StaticResource MaterialDesignOutlinedButton}"
                                                   Height="32" Padding="12,0"
                                                   Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                   Margin="2"
                                                   Visibility="{Binding Converter={StaticResource CanEditDataConverter}}"/>
                                            <Button Content="حذف" FontSize="12"
                                                   Command="{Binding DataContext.DeleteItemCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                   Style="{StaticResource MaterialDesignOutlinedButton}"
                                                   Height="32" Padding="12,0"
                                                   Foreground="Red" Margin="2"
                                                   Visibility="{Binding Converter={StaticResource CanDeleteDataConverter}}"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>

            <!-- Empty State -->
            <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center"
                       Visibility="{Binding InventoryItems.Count, Converter={StaticResource ZeroToVisibilityConverter}}">
                <materialDesign:PackIcon Kind="Package" Width="64" Height="64"
                                       Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                <TextBlock Text="لا توجد عناصر في المخزون"
                          FontSize="16"
                          Foreground="{DynamicResource MaterialDesignBodyLight}"
                          HorizontalAlignment="Center" Margin="0,10,0,0"/>
                <TextBlock Text="انقر على 'إضافة عنصر' لإضافة العنصر الأول"
                          FontSize="12"
                          Foreground="{DynamicResource MaterialDesignBodyLight}"
                          HorizontalAlignment="Center"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>
