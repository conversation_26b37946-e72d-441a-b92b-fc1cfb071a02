<Window x:Class="NetworkManagement.Views.ColumnSettingsDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="{Binding Title}"
        Height="500" Width="450"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft"
        Background="{DynamicResource MaterialDesignPaper}"
        Foreground="{DynamicResource MaterialDesignBody}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- المحتوى الرئيسي -->
        <ScrollViewer Grid.Row="0" VerticalScrollBarVisibility="Auto" Margin="20">
            <StackPanel>
                <!-- العنوان -->
                <TextBlock Text="إعدادات عرض الأعمدة"
                          Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                          Margin="0,0,0,20"
                          HorizontalAlignment="Center"/>

                <!-- تعليمات -->
                <materialDesign:Card Margin="0,0,0,20" Padding="15">
                    <StackPanel>
                        <TextBlock Text="تعليمات:" FontWeight="Bold" Margin="0,0,0,10"/>
                        <TextBlock Text="• اختر العمود من القائمة لتعديل إعداداته" TextWrapping="Wrap" Margin="0,2"/>
                        <TextBlock Text="• استخدم الأزرار لتغيير ترتيب الأعمدة" TextWrapping="Wrap" Margin="0,2"/>
                        <TextBlock Text="• انقر على مربع الاختيار لإظهار/إخفاء العمود" TextWrapping="Wrap" Margin="0,2"/>
                        <TextBlock Text="• اضغط 'إعادة تعيين' للعودة للإعدادات الافتراضية" TextWrapping="Wrap" Margin="0,2"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- قائمة الأعمدة -->
                <materialDesign:Card Padding="15">
                    <StackPanel>
                        <TextBlock Text="الأعمدة المتاحة:" FontWeight="Bold" Margin="0,0,0,15"/>
                        
                        <!-- قائمة الأعمدة -->
                        <ListBox ItemsSource="{Binding Columns}"
                                SelectedItem="{Binding SelectedColumn}"
                                Height="200"
                                ScrollViewer.VerticalScrollBarVisibility="Auto">
                            <ListBox.ItemTemplate>
                                <DataTemplate>
                                    <Grid Margin="5">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- مربع الاختيار -->
                                        <CheckBox Grid.Column="0"
                                                 IsChecked="{Binding IsVisible}"
                                                 IsEnabled="{Binding CanHide}"
                                                 VerticalAlignment="Center"
                                                 Margin="0,0,10,0"/>

                                        <!-- اسم العمود -->
                                        <TextBlock Grid.Column="1"
                                                  Text="{Binding Header}"
                                                  VerticalAlignment="Center"
                                                  FontSize="14"/>

                                        <!-- رقم الترتيب -->
                                        <TextBlock Grid.Column="2"
                                                  Text="{Binding Order, StringFormat='#{0}'}"
                                                  VerticalAlignment="Center"
                                                  FontSize="12"
                                                  Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                  Margin="10,0,0,0"/>
                                    </Grid>
                                </DataTemplate>
                            </ListBox.ItemTemplate>
                        </ListBox>

                        <!-- أزرار التحكم -->
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,15,0,0">
                            <Button Command="{Binding MoveUpCommand}"
                                   Style="{StaticResource MaterialDesignOutlinedButton}"
                                   Content="تحريك لأعلى"
                                   Margin="5">
                                <Button.IsEnabled>
                                    <MultiBinding Converter="{StaticResource AndConverter}">
                                        <Binding Path="SelectedColumn" Converter="{StaticResource NotNullToBooleanConverter}"/>
                                        <Binding Path="SelectedColumn.CanReorder"/>
                                    </MultiBinding>
                                </Button.IsEnabled>
                            </Button>

                            <Button Command="{Binding MoveDownCommand}"
                                   Style="{StaticResource MaterialDesignOutlinedButton}"
                                   Content="تحريك لأسفل"
                                   Margin="5">
                                <Button.IsEnabled>
                                    <MultiBinding Converter="{StaticResource AndConverter}">
                                        <Binding Path="SelectedColumn" Converter="{StaticResource NotNullToBooleanConverter}"/>
                                        <Binding Path="SelectedColumn.CanReorder"/>
                                    </MultiBinding>
                                </Button.IsEnabled>
                            </Button>

                            <Button Command="{Binding ToggleVisibilityCommand}"
                                   Style="{StaticResource MaterialDesignOutlinedButton}"
                                   Content="إظهار/إخفاء"
                                   Margin="5">
                                <Button.IsEnabled>
                                    <MultiBinding Converter="{StaticResource AndConverter}">
                                        <Binding Path="SelectedColumn" Converter="{StaticResource NotNullToBooleanConverter}"/>
                                        <Binding Path="SelectedColumn.CanHide"/>
                                    </MultiBinding>
                                </Button.IsEnabled>
                            </Button>
                        </StackPanel>

                        <!-- زر إعادة التعيين -->
                        <Button Command="{Binding ResetToDefaultCommand}"
                               Style="{StaticResource MaterialDesignOutlinedButton}"
                               Content="إعادة تعيين للافتراضي"
                               HorizontalAlignment="Center"
                               Margin="0,15,0,0"/>
                    </StackPanel>
                </materialDesign:Card>
            </StackPanel>
        </ScrollViewer>

        <!-- أزرار الحفظ والإلغاء -->
        <Border Grid.Row="1" Background="{DynamicResource MaterialDesignDivider}" Height="1"/>
        <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Left" Margin="20,15">
            <Button Command="{Binding SaveSettingsCommand}"
                   Style="{StaticResource MaterialDesignRaisedButton}"
                   Content="حفظ"
                   Margin="0,0,10,0"
                   IsEnabled="{Binding IsLoading, Converter={StaticResource InverseBooleanConverter}}"/>

            <Button Command="{Binding CancelCommand}"
                   Style="{StaticResource MaterialDesignOutlinedButton}"
                   Content="إلغاء"
                   IsEnabled="{Binding IsLoading, Converter={StaticResource InverseBooleanConverter}}"/>
        </StackPanel>

        <!-- مؤشر التحميل -->
        <ProgressBar Grid.Row="0" Grid.RowSpan="2"
                    IsIndeterminate="True"
                    Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                    VerticalAlignment="Top" Height="4" Panel.ZIndex="1"/>
    </Grid>
</Window>
