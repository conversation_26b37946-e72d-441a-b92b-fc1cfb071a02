<UserControl x:Class="NetworkManagement.Views.SettingsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft">

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <StackPanel Margin="{StaticResource CardPadding}">
            <!-- Page Header -->
            <TextBlock Text="إعدادات التطبيق" Style="{StaticResource PageHeaderStyle}"/>

            <!-- Loading Indicator -->
            <ProgressBar IsIndeterminate="True"
                        Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                        Height="4" Margin="{StaticResource SpacingS}"/>

            <!-- Application Info - Compact -->
            <materialDesign:Card Style="{StaticResource CardStyle}" Margin="{StaticResource ElementMargin}">
                <StackPanel Margin="{StaticResource SpacingM}" Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Information"
                                           Width="24" Height="24"
                                           Foreground="{DynamicResource PrimaryHueMidBrush}"
                                           VerticalAlignment="Center"
                                           Margin="0,0,15,0"/>
                    <StackPanel>
                        <TextBlock Text="{Binding ApplicationName}"
                                  Style="{StaticResource SubHeaderTextStyle}"
                                  Margin="0,0,0,5"/>
                        <TextBlock Text="{Binding ApplicationVersion, StringFormat='الإصدار {0}'}"
                                  Style="{StaticResource CaptionTextStyle}"
                                  Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                    </StackPanel>
                </StackPanel>
            </materialDesign:Card>

            <!-- Statistics - Compact -->
            <materialDesign:Card Style="{StaticResource CardStyle}" Margin="{StaticResource ElementMargin}">
                <StackPanel Margin="{StaticResource SpacingM}">
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                        <materialDesign:PackIcon Kind="BarChart"
                                               Width="20" Height="20"
                                               Foreground="{DynamicResource PrimaryHueMidBrush}"
                                               VerticalAlignment="Center"
                                               Margin="0,0,10,0"/>
                        <TextBlock Text="إحصائيات البيانات"
                                  Style="{StaticResource SubHeaderTextStyle}"/>
                    </StackPanel>

                    <UniformGrid Columns="4" Rows="1">
                        <StackPanel HorizontalAlignment="Center" Margin="5">
                            <TextBlock Text="{Binding TotalDevices}"
                                      Style="{StaticResource StatisticNumberStyle}"
                                      Foreground="{StaticResource StatisticsBlueBrush}"
                                      HorizontalAlignment="Center"/>
                            <TextBlock Text="الأجهزة"
                                      Style="{StaticResource CaptionTextStyle}"
                                      HorizontalAlignment="Center"/>
                        </StackPanel>

                        <StackPanel HorizontalAlignment="Center" Margin="5">
                            <TextBlock Text="{Binding TotalSites}"
                                      Style="{StaticResource StatisticNumberStyle}"
                                      Foreground="{StaticResource StatisticsGreenBrush}"
                                      HorizontalAlignment="Center"/>
                            <TextBlock Text="المواقع"
                                      Style="{StaticResource CaptionTextStyle}"
                                      HorizontalAlignment="Center"/>
                        </StackPanel>

                        <StackPanel HorizontalAlignment="Center" Margin="5">
                            <TextBlock Text="{Binding TotalUsers}"
                                      Style="{StaticResource StatisticNumberStyle}"
                                      Foreground="{StaticResource StatisticsOrangeBrush}"
                                      HorizontalAlignment="Center"/>
                            <TextBlock Text="المستخدمين"
                                      Style="{StaticResource CaptionTextStyle}"
                                      HorizontalAlignment="Center"/>
                        </StackPanel>

                        <StackPanel HorizontalAlignment="Center" Margin="5">
                            <TextBlock Text="{Binding TotalInventoryItems}"
                                      Style="{StaticResource StatisticNumberStyle}"
                                      Foreground="{StaticResource StatisticsPurpleBrush}"
                                      HorizontalAlignment="Center"/>
                            <TextBlock Text="المخزون"
                                      Style="{StaticResource CaptionTextStyle}"
                                      HorizontalAlignment="Center"/>
                        </StackPanel>
                    </UniformGrid>
                </StackPanel>
            </materialDesign:Card>

            <!-- General Settings -->
            <materialDesign:Card Style="{StaticResource CardStyle}" Margin="{StaticResource ElementMargin}">
                <StackPanel Margin="{StaticResource CardPadding}">
                    <StackPanel Orientation="Horizontal" Margin="{StaticResource SpacingM}">
                        <materialDesign:PackIcon Kind="Settings"
                                               Width="20" Height="20"
                                               Foreground="{DynamicResource PrimaryHueMidBrush}"
                                               VerticalAlignment="Center"
                                               Margin="0,0,10,0"/>
                        <TextBlock Text="الإعدادات العامة"
                                  Style="{StaticResource SubHeaderTextStyle}"/>
                    </StackPanel>

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" Margin="0,0,20,0">
                            <CheckBox Content="تذكر بيانات تسجيل الدخول"
                                     IsChecked="{Binding RememberLoginEnabled}"
                                     Margin="{StaticResource ElementMargin}"/>

                            <CheckBox Content="إظهار الإشعارات"
                                     IsChecked="{Binding ShowNotifications}"
                                     Margin="{StaticResource ElementMargin}"/>

                            <TextBox Text="{Binding DefaultNetwork}"
                                    materialDesign:HintAssist.Hint="الشبكة الافتراضية (اختياري)"
                                    materialDesign:HintAssist.IsFloating="True"
                                    Margin="{StaticResource ElementMargin}"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1">
                            <TextBox Text="{Binding PingTimeoutSeconds}"
                                    materialDesign:HintAssist.Hint="مهلة فحص الاتصال (ثانية)"
                                    materialDesign:HintAssist.IsFloating="True"
                                    Margin="{StaticResource ElementMargin}"/>

                            <ComboBox ItemsSource="{Binding AvailableThemes}"
                                     SelectedItem="{Binding Theme}"
                                     materialDesign:HintAssist.Hint="المظهر"
                                     materialDesign:HintAssist.IsFloating="True"
                                     Margin="{StaticResource ElementMargin}"/>

                            <ComboBox ItemsSource="{Binding AvailableLanguages}"
                                     SelectedItem="{Binding Language}"
                                     materialDesign:HintAssist.Hint="اللغة"
                                     materialDesign:HintAssist.IsFloating="True"
                                     Margin="{StaticResource ElementMargin}"/>
                        </StackPanel>
                    </Grid>
                </StackPanel>
            </materialDesign:Card>

            <!-- Auto Ping Settings -->
            <materialDesign:Card Style="{StaticResource CardStyle}" Margin="{StaticResource ElementMargin}">
                <StackPanel Margin="{StaticResource CardPadding}">
                    <StackPanel Orientation="Horizontal" Margin="{StaticResource SpacingM}">
                        <materialDesign:PackIcon Kind="Network"
                                               Width="20" Height="20"
                                               Foreground="{DynamicResource PrimaryHueMidBrush}"
                                               VerticalAlignment="Center"
                                               Margin="0,0,10,0"/>
                        <TextBlock Text="الفحص التلقائي للأجهزة"
                                  Style="{StaticResource SubHeaderTextStyle}"/>
                    </StackPanel>

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" Margin="0,0,20,0">
                            <CheckBox Content="تفعيل الفحص التلقائي"
                                     IsChecked="{Binding AutoPingEnabled}"
                                     Margin="{StaticResource ElementMargin}"/>

                            <TextBlock Text="يقوم بفحص حالة الأجهزة تلقائياً حسب الفترة المحددة"
                                      Style="{StaticResource CaptionTextStyle}"
                                      Opacity="0.7"
                                      Margin="{StaticResource ElementMargin}"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1">
                            <ComboBox ItemsSource="{Binding AutoPingIntervalOptions}"
                                     SelectedItem="{Binding AutoPingInterval}"
                                     IsEnabled="{Binding AutoPingEnabled}"
                                     materialDesign:HintAssist.Hint="فترة الفحص (ثانية)"
                                     materialDesign:HintAssist.IsFloating="True"
                                     Margin="{StaticResource ElementMargin}"/>

                            <TextBlock Text="{Binding AutoPingInterval, StringFormat='كل {0} ثانية'}"
                                      Style="{StaticResource CaptionTextStyle}"
                                      Opacity="0.7"
                                      Margin="{StaticResource ElementMargin}"/>
                        </StackPanel>
                    </Grid>
                </StackPanel>
            </materialDesign:Card>



            <!-- Backup Settings -->
            <materialDesign:Card Style="{StaticResource CardStyle}" Margin="{StaticResource ElementMargin}">
                <StackPanel Margin="{StaticResource CardPadding}">
                    <StackPanel Orientation="Horizontal" Margin="{StaticResource SpacingM}">
                        <materialDesign:PackIcon Kind="Archive"
                                               Width="20" Height="20"
                                               Foreground="{DynamicResource PrimaryHueMidBrush}"
                                               VerticalAlignment="Center"
                                               Margin="0,0,10,0"/>
                        <TextBlock Text="النسخ الاحتياطي"
                                  Style="{StaticResource SubHeaderTextStyle}"/>
                    </StackPanel>

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" Margin="0,0,20,0">
                            <TextBlock Text="{Binding LastBackupDateDisplay, StringFormat='آخر نسخة احتياطية: {0}'}"
                                      Style="{StaticResource CaptionTextStyle}"
                                      Margin="{StaticResource ElementMargin}"/>

                            <CheckBox Content="تفعيل النسخ الاحتياطي التلقائي"
                                     IsChecked="{Binding AutoBackupEnabled}"
                                     Margin="{StaticResource ElementMargin}"/>

                            <ComboBox ItemsSource="{Binding AutoBackupOptions}"
                                     SelectedItem="{Binding AutoBackupDays}"
                                     IsEnabled="{Binding AutoBackupEnabled}"
                                     materialDesign:HintAssist.Hint="تكرار النسخ الاحتياطي (أيام)"
                                     materialDesign:HintAssist.IsFloating="True"
                                     Margin="{StaticResource ElementMargin}"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1">
                            <Grid Margin="{StaticResource ElementMargin}">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBox Grid.Column="0"
                                        Text="{Binding BackupLocation}"
                                        IsReadOnly="True"
                                        materialDesign:HintAssist.Hint="مجلد النسخ الاحتياطية"
                                        materialDesign:HintAssist.IsFloating="True"/>
                                <Button Grid.Column="1" Content="تصفح"
                                       Command="{Binding SelectBackupLocationCommand}"
                                       Style="{StaticResource MaterialDesignOutlinedButton}"
                                       Margin="10,0,0,0"/>
                            </Grid>

                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="{StaticResource SpacingM}">
                                <Button Command="{Binding CreateBackupCommand}"
                                       Style="{StaticResource MaterialDesignRaisedButton}"
                                       Margin="0,0,10,0">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Archive" Width="16" Height="16" Margin="0,0,5,0"/>
                                        <TextBlock Text="إنشاء نسخة"/>
                                    </StackPanel>
                                </Button>

                                <Button Command="{Binding RestoreBackupCommand}"
                                       Style="{StaticResource MaterialDesignOutlinedButton}">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Undo" Width="16" Height="16" Margin="0,0,5,0"/>
                                        <TextBlock Text="استعادة"/>
                                    </StackPanel>
                                </Button>
                            </StackPanel>
                        </StackPanel>
                    </Grid>
                </StackPanel>
            </materialDesign:Card>

            <!-- Action Buttons -->
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Command="{Binding SaveSettingsCommand}"
                       Style="{StaticResource MaterialDesignRaisedButton}"
                       Margin="0,0,15,0">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="ContentSave" Width="16" Height="16" Margin="0,0,5,0"/>
                        <TextBlock Text="حفظ الإعدادات"/>
                    </StackPanel>
                </Button>

                <Button Command="{Binding LoadSettingsCommand}"
                       Style="{StaticResource MaterialDesignOutlinedButton}"
                       Margin="0,0,15,0">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Refresh" Width="16" Height="16" Margin="0,0,5,0"/>
                        <TextBlock Text="إعادة تحميل"/>
                    </StackPanel>
                </Button>

                <Button Command="{Binding ResetSettingsCommand}"
                       Style="{StaticResource MaterialDesignOutlinedButton}"
                       Margin="0,0,15,0">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Undo" Width="16" Height="16" Margin="0,0,5,0"/>
                        <TextBlock Text="إعادة تعيين"/>
                    </StackPanel>
                </Button>

                <Button Command="{Binding OpenDatabaseSettingsCommand}"
                       Style="{StaticResource MaterialDesignRaisedButton}"
                       Background="{DynamicResource SecondaryHueMidBrush}">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Database" Width="16" Height="16" Margin="0,0,5,0"/>
                        <TextBlock Text="إعدادات قاعدة البيانات"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </StackPanel>
    </ScrollViewer>
</UserControl>
