using System;
using System.Collections.Generic;
using System.Linq;
using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using NetworkManagement.Models;
using NetworkManagement.Services;
using NetworkManagement.Views;
using NetworkManagement.Helpers;

namespace NetworkManagement.ViewModels
{
    public partial class DevicesViewModel : ObservableObject
    {
        private readonly IDeviceService _deviceService;
        private readonly IPingService _pingService;
        private readonly INetworkService _networkService;
        private readonly IReportExportService _exportService;
        private readonly IAuthService _authService;
        private readonly INotificationService _notificationService;
        private readonly ISettingsService _settingsService;
        private readonly IDeviceInventoryService _deviceInventoryService;

        [ObservableProperty]
        private ObservableCollection<Device> devices = new();

        [ObservableProperty]
        private Device? selectedDevice;

        [ObservableProperty]
        private string searchText = string.Empty;

        [ObservableProperty]
        private bool isLoading = false;

        [ObservableProperty]
        private bool isPinging = false;

        [ObservableProperty]
        private string pingStatus = string.Empty;

        [ObservableProperty]
        private bool autoRefreshEnabled = false;

        [ObservableProperty]
        private string statusFilter = string.Empty;

        [ObservableProperty]
        private string typeFilter = "الكل";

        [ObservableProperty]
        private List<string> availableDeviceTypes = new();

        [ObservableProperty]
        private ObservableCollection<ColumnInfo> columnSettings = new();

        [ObservableProperty]
        private int selectedDevicesCount = 0;

        // فلتر الشبكة المحدد من MainViewModel
        private string? _networkFilter = null;

        // فلاتر نطاقات IP
        [ObservableProperty]
        private ObservableCollection<string> availableIpRanges = new();

        [ObservableProperty]
        private string selectedIpRange = string.Empty;

        // خصائص الصلاحيات
        public bool CanAddDevices => _authService.CanAddData();
        public bool CanEditDevices => _authService.CanEditData();
        public bool CanDeleteDevices => _authService.CanDeleteData();
        public bool CanManageDevices => _authService.CanManageDevices;

        // إشعار تغيير الصلاحيات
        private void NotifyPermissionsChanged()
        {
            OnPropertyChanged(nameof(CanAddDevices));
            OnPropertyChanged(nameof(CanEditDevices));
            OnPropertyChanged(nameof(CanDeleteDevices));
            OnPropertyChanged(nameof(CanManageDevices));

            // تحديث حالة الأوامر
            EditDeviceCommand.NotifyCanExecuteChanged();
            DeleteDeviceCommand.NotifyCanExecuteChanged();
            ImportDevicesCommand.NotifyCanExecuteChanged();
        }



        private System.Threading.Timer? _refreshTimer;
        private System.Threading.CancellationTokenSource? _loadCancellationTokenSource;
        private readonly object _loadLock = new object();
        private readonly System.Threading.SemaphoreSlim _loadSemaphore = new System.Threading.SemaphoreSlim(1, 1);

        public DevicesViewModel(IDeviceService deviceService, IPingService pingService, INetworkService networkService, IReportExportService exportService, IAuthService authService, INotificationService notificationService, ISettingsService settingsService, IDeviceInventoryService deviceInventoryService)
        {
            _deviceService = deviceService;
            _pingService = pingService;
            _networkService = networkService;
            _exportService = exportService;
            _authService = authService;
            _notificationService = notificationService;
            _settingsService = settingsService;
            _deviceInventoryService = deviceInventoryService;

            // Initialize with empty collection - load data when view is ready
            Devices = new ObservableCollection<Device>();

            // تهيئة فلتر نطاق IP
            SelectedIpRange = "جميع النطاقات";

            // الاشتراك في تغيير المستخدم لتحديث الصلاحيات
            _authService.UserChanged += OnUserChanged;

            // تهيئة إعدادات الأعمدة
            InitializeColumnSettings();

            System.Diagnostics.Debug.WriteLine("DevicesViewModel constructor completed");
            System.Diagnostics.Debug.WriteLine($"DevicesViewModel: Initial Devices count = {Devices.Count}");
        }

        // Method to initialize data - called from View's Loaded event
        public async System.Threading.Tasks.Task InitializeAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("DevicesViewModel.InitializeAsync: بدء التهيئة");

                // التأكد من أن التهيئة تتم على UI Thread
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(async () =>
                {
                    // استعادة حالة الفلاتر المحفوظة
                    RestoreFilterState();

                    // تحميل أنواع الأجهزة للفلتر
                    await LoadDeviceTypesAsync();

                    // تحميل الأجهزة مع معالجة أفضل للأخطاء
                    await LoadDevicesAsync();

                    // بدء الفحص التلقائي إذا كان مفعلاً
                    await InitializeAutoRefreshAsync();
                });
            }
            catch (Exception ex)
            {
                // Show user-friendly error message
                MessageHelper.HandleException(ex, "تحميل البيانات", true);
            }
        }

        // دالة تعيين فلتر الشبكة من MainViewModel
        public void SetNetworkFilter(string? networkId)
        {
            _networkFilter = networkId;

            // تشغيل LoadDevicesAsync على UI Thread لتجنب مشاكل Threading
            if (System.Windows.Application.Current?.Dispatcher != null)
            {
                System.Windows.Application.Current.Dispatcher.InvokeAsync(async () =>
                {
                    try
                    {
                        await LoadDevicesAsync();
                    }
                    catch (Exception ex)
                    {
                        MessageHelper.HandleException(ex, "تحديث فلتر الشبكة");
                    }
                });
            }
        }

        // تحديث نطاقات IP المتاحة
        private async System.Threading.Tasks.Task UpdateAvailableIpRangesAsync(IEnumerable<Device> devices)
        {
            try
            {
                // استخراج نطاقات IP بكفاءة أكبر
                var ipRanges = devices
                    .Where(d => !string.IsNullOrWhiteSpace(d.Ip))
                    .Select(d => d.Ip!.Split('.'))
                    .Where(parts => parts.Length >= 3)
                    .Select(parts => $"{parts[0]}.{parts[1]}.{parts[2]}.x")
                    .Distinct()
                    .OrderBy(r => r)
                    .ToList();

                // تحديث القائمة على UI Thread
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    try
                    {
                        var currentSelection = SelectedIpRange;

                        AvailableIpRanges.Clear();
                        AvailableIpRanges.Add("جميع النطاقات"); // خيار افتراضي

                        foreach (var range in ipRanges)
                        {
                            AvailableIpRanges.Add(range);
                        }

                        // إعادة تعيين الفلتر المحدد إذا لم يعد موجوداً
                        if (!string.IsNullOrEmpty(currentSelection) && !AvailableIpRanges.Contains(currentSelection))
                        {
                            SelectedIpRange = "جميع النطاقات";
                        }
                        else if (!string.IsNullOrEmpty(currentSelection))
                        {
                            SelectedIpRange = currentSelection;
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Error updating IP ranges UI: {ex.Message}");
                    }
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating IP ranges: {ex.Message}");
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task FilterByIpRangeAsync()
        {
            await ApplyFiltersAsync();
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task LoadDevicesAsync()
        {
            await LoadDevicesAsync(System.Threading.CancellationToken.None);
        }

        private async System.Threading.Tasks.Task LoadDevicesAsync(System.Threading.CancellationToken cancellationToken)
        {
            // استخدام Semaphore لمنع التحميل المتزامن
            await _loadSemaphore.WaitAsync(cancellationToken).ConfigureAwait(false);

            try
            {
                // تحديث حالة التحميل على UI Thread
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    IsLoading = true;
                });

                // الحصول على البيانات المفلترة (بدون تطبيق فلاتر البحث والحالة)
                var filteredDevices = await GetBaseFilteredDevicesAsync();

                // التحقق من الإلغاء قبل تحديث UI
                cancellationToken.ThrowIfCancellationRequested();

                // تحديث مجموعة الأجهزة
                await UpdateDevicesCollectionAsync(filteredDevices);

                // تحديث نطاقات IP المتاحة
                await UpdateAvailableIpRangesAsync(filteredDevices);
            }
            catch (Exception ex)
            {
                // Show error on UI thread
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    MessageHelper.HandleException(ex, "تحميل الأجهزة");
                });
            }
            finally
            {
                // تحديث حالة التحميل على UI Thread
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    IsLoading = false;
                });

                _loadSemaphore.Release();

                // تحديث حالة الأوامر سيتم تلقائياً عند تغيير SelectedDevice
            }
        }

        /// <summary>
        /// الحصول على الأجهزة مع تطبيق فلاتر الشبكة والصلاحيات فقط (بدون فلاتر البحث)
        /// </summary>
        private async System.Threading.Tasks.Task<IEnumerable<Device>> GetBaseFilteredDevicesAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("GetBaseFilteredDevicesAsync: بدء تحميل الأجهزة من قاعدة البيانات");

                // تطبيق فلتر الشبكة المحدد من MainViewModel
                string? networkFilter = _networkFilter;

                // إذا لم يكن هناك فلتر شبكة محدد، استخدم فلترة الصلاحيات العادية
                if (string.IsNullOrEmpty(networkFilter))
                {
                    networkFilter = PermissionHelper.GetNetworkFilter(_authService);
                }

                System.Diagnostics.Debug.WriteLine($"GetBaseFilteredDevicesAsync: استخدام فلتر الشبكة - {networkFilter ?? "null"}");

                // تحميل الأجهزة مع فلتر الشبكة
                var allDevices = await _deviceService.GetAllAsync(networkFilter);

                System.Diagnostics.Debug.WriteLine($"GetBaseFilteredDevicesAsync: تم تحميل {allDevices.Count()} جهاز من الخدمة");

                // تطبيق فلترة الصلاحيات الإضافية
                var filteredDevices = PermissionHelper.ApplyPermissionFilter(
                    allDevices,
                    _authService,
                    device => device.NetworkId);

                System.Diagnostics.Debug.WriteLine($"GetBaseFilteredDevicesAsync: بعد تطبيق فلتر الصلاحيات - {filteredDevices.Count()} جهاز");

                return filteredDevices;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"GetBaseFilteredDevicesAsync: خطأ - {ex.Message}");
                throw;
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task SearchDevicesAsync()
        {
            await ApplyFiltersAsync();
        }

        [RelayCommand]
        private void AddDevice()
        {
            System.Diagnostics.Debug.WriteLine("AddDevice command executed");
            _ = AddDeviceInternalAsync();
        }

        private async System.Threading.Tasks.Task AddDeviceInternalAsync()
        {
            try
            {
                // التحقق من صلاحية إضافة الأجهزة
                if (!CanAddDevices)
                {
                    PermissionHelper.ShowPermissionDeniedMessage("إضافة", "أجهزة جديدة");
                    return;
                }

                var dialogViewModel = App.GetService<DeviceDialogViewModel>();

                // Initialize data first
                await dialogViewModel.InitializeAsync();

                var dialog = new DeviceDialog(dialogViewModel);
                var result = dialog.ShowDialog();

                if (result == true)
                {
                    // مسح الفلاتر لإظهار الجهاز الجديد
                    SearchText = string.Empty;
                    StatusFilter = string.Empty;
                    TypeFilter = "الكل";

                    // تحديث القائمة
                    await LoadDevicesAsync();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in AddDeviceAsync: {ex}");
                System.Windows.MessageBox.Show($"خطأ في فتح نافذة إضافة الجهاز:\n{ex.Message}", "خطأ");
            }
        }

        [RelayCommand(CanExecute = nameof(CanEditDevice))]
        private async System.Threading.Tasks.Task EditDeviceAsync(Device? device = null)
        {
            System.Diagnostics.Debug.WriteLine("EditDeviceAsync called");
            try
            {
                var deviceToEdit = device ?? SelectedDevice;
                System.Diagnostics.Debug.WriteLine($"Device to edit: {deviceToEdit?.Id}");

                if (deviceToEdit != null)
                {
                    // التحقق من صلاحية تعديل الجهاز
                    if (!PermissionHelper.CanEditItem(_authService, deviceToEdit.NetworkId))
                    {
                        PermissionHelper.ShowPermissionDeniedMessage("تعديل", "هذا الجهاز");
                        return;
                    }

                    System.Diagnostics.Debug.WriteLine("Creating dialog...");
                    var dialogViewModel = App.GetService<DeviceDialogViewModel>();
                    await dialogViewModel.InitializeAsync(deviceToEdit);

                    var dialog = new DeviceDialog(dialogViewModel);

                    // التأكد من أن النافذة تظهر في المقدمة
                    var mainWindow = System.Windows.Application.Current.MainWindow;
                    if (mainWindow != null && mainWindow.IsLoaded && mainWindow != dialog)
                    {
                        dialog.Owner = mainWindow;
                        dialog.WindowStartupLocation = System.Windows.WindowStartupLocation.CenterOwner;
                    }
                    else
                    {
                        dialog.WindowStartupLocation = System.Windows.WindowStartupLocation.CenterScreen;
                    }

                    var result = dialog.ShowDialog();

                    if (result == true)
                    {
                        await LoadDevicesAsync();
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("No device selected for editing");
                    System.Windows.MessageBox.Show("يرجى تحديد جهاز للتعديل", "تنبيه");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in EditDeviceAsync: {ex}");
                System.Windows.MessageBox.Show($"خطأ في فتح نافذة تعديل الجهاز:\n{ex.Message}", "خطأ");
            }
        }

        [RelayCommand(CanExecute = nameof(CanDeleteDevice))]
        private async System.Threading.Tasks.Task DeleteDeviceAsync(Device? device = null)
        {
            var deviceToDelete = device ?? SelectedDevice;
            if (deviceToDelete == null) return;

            // التحقق من صلاحية حذف الجهاز
            if (!PermissionHelper.CanDeleteItem(_authService, deviceToDelete.NetworkId))
            {
                PermissionHelper.ShowPermissionDeniedMessage("حذف", "هذا الجهاز");
                return;
            }

            try
            {
                // تحضير معلومات الجهاز للعرض
                var deviceInfo = MessageHelper.FormatDeviceInfo(
                    deviceToDelete.Responsible, deviceToDelete.Type,
                    deviceToDelete.Location, deviceToDelete.Ip);

                // نافذة تأكيد الحذف
                if (MessageHelper.ShowDeleteConfirmation("الجهاز", deviceInfo))
                {
                    IsLoading = true;

                    // إرجاع المواد إلى المخزون قبل الحذف
                    var networkId = _authService.CurrentUser?.NetworkId;
                    var inventoryResult = await _deviceInventoryService.RestoreInventoryForDeletedDeviceAsync(deviceToDelete, networkId);

                    if (inventoryResult.Success)
                    {
                        System.Diagnostics.Debug.WriteLine($"تم إرجاع المواد إلى المخزون عند حذف الجهاز");
                    }

                    await _deviceService.DeleteAsync(deviceToDelete.Id);
                    await LoadDevicesAsync();

                    // إشعار نجاح الحذف
                    var deviceName = deviceToDelete.Responsible ?? deviceToDelete.Location ?? "الجهاز المحدد";
                    var userName = _authService.CurrentUser?.Name ?? "المستخدم";
                    var userId = _authService.CurrentUser?.Id;
                    await _notificationService.ShowOperationAsync(
                        "تم حذف الجهاز",
                        $"قام {userName} بحذف الجهاز: {deviceName}",
                        NotificationType.Success,
                        userName, userId, "حذف", deviceName, deviceToDelete.NetworkId);
                }
            }
            catch (Exception ex)
            {
                MessageHelper.HandleException(ex, "حذف الجهاز");
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand(CanExecute = nameof(CanPingDevices))]
        private async System.Threading.Tasks.Task PingAllDevicesAsync()
        {
            try
            {
                // الحصول على جميع الأجهزة من قاعدة البيانات (ليس فقط المعروضة)
                var networkFilter = PermissionHelper.GetNetworkFilter(_authService);
                var allDevices = await _deviceService.GetAllAsync(networkFilter);
                var permissionFilteredDevices = PermissionHelper.ApplyPermissionFilter(allDevices, _authService, d => d.NetworkId);
                var devicesWithIp = DeviceFilterHelper.GetDevicesWithValidIp(permissionFilteredDevices).ToList();

                await PingDevicesAsync(devicesWithIp, "جميع الأجهزة", true);
            }
            catch (Exception ex)
            {
                MessageHelper.HandleException(ex, "فحص جميع الأجهزة");
            }
        }

        [RelayCommand(CanExecute = nameof(CanExportDevices))]
        private async System.Threading.Tasks.Task ExportDevicesAsync()
        {
            try
            {
                IsLoading = true;

                var filter = "Excel Files (*.xlsx)|*.xlsx|CSV Files (*.csv)|*.csv";
                var defaultFileName = $"الأجهزة_{DateTime.Now:yyyy-MM-dd}";

                var filePath = await _exportService.GetSaveFilePathAsync(defaultFileName, filter);
                if (string.IsNullOrEmpty(filePath))
                    return;

                string result = "";
                var isExcel = filePath.EndsWith(".xlsx", StringComparison.OrdinalIgnoreCase);

                // تطبيق فلترة الصلاحيات على التصدير
                var filteredDevices = await PermissionHelper.LoadFilteredDataAsync(
                    _authService,
                    _deviceService.GetAllAsync,
                    device => device.NetworkId);

                result = isExcel
                    ? await _exportService.ExportDevicesReportToExcelAsync(filteredDevices, filePath)
                    : await _exportService.ExportDevicesReportToCsvAsync(filteredDevices, filePath);

                if (!string.IsNullOrEmpty(result))
                {
                    MessageHelper.ShowSuccessMessage("تصدير", "بيانات الأجهزة", $"تم الحفظ في:\n{result}");
                }
            }
            catch (Exception ex)
            {
                MessageHelper.HandleException(ex, "تصدير البيانات");
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand(CanExecute = nameof(CanImportDevices))]
        private async System.Threading.Tasks.Task ImportDevicesAsync()
        {
            // التحقق من صلاحية إضافة الأجهزة
            if (!CanAddDevices)
            {
                PermissionHelper.ShowPermissionDeniedMessage("استيراد", "أجهزة جديدة");
                return;
            }

            try
            {
                IsLoading = true;

                var filter = "Excel Files (*.xlsx)|*.xlsx|CSV Files (*.csv)|*.csv";
                var filePath = await _exportService.GetOpenFilePathAsync(filter);
                if (string.IsNullOrEmpty(filePath))
                    return;

                var confirmMessage = "هل تريد استيراد الأجهزة من الملف المحدد؟\n\n" +
                                   "⚠️ تحذير:\n" +
                                   "• سيتم إضافة الأجهزة الجديدة إلى القائمة الحالية\n" +
                                   "• تأكد من صحة تنسيق الملف\n" +
                                   "• يجب أن تحتوي الأعمدة على: الموقع، النوع، عنوان IP، المسؤول\n\n" +
                                   "هل تريد المتابعة؟";

                if (!MessageHelper.ShowConfirmation(confirmMessage, "تأكيد الاستيراد"))
                    return;

                IEnumerable<Device> importedDevices;
                var isExcel = filePath.EndsWith(".xlsx", StringComparison.OrdinalIgnoreCase);

                if (isExcel)
                {
                    importedDevices = await _exportService.ImportDevicesFromExcelAsync(filePath);
                }
                else
                {
                    importedDevices = await _exportService.ImportDevicesFromCsvAsync(filePath);
                }

                var devicesList = importedDevices.ToList();
                if (!devicesList.Any())
                {
                    var noDataMessage = "لم يتم العثور على بيانات صالحة في الملف.\n\n" +
                                      "تأكد من:\n• وجود بيانات في الملف\n• صحة تنسيق الأعمدة\n• وجود سطر العناوين";
                    MessageHelper.ShowWarningMessage(noDataMessage, "لا توجد بيانات");
                    return;
                }

                // Get available networks for validation
                var availableNetworks = await _networkService.GetAllAsync();
                var networkLookup = availableNetworks.ToDictionary(n => n.Name ?? "", n => n.Id, StringComparer.OrdinalIgnoreCase);

                // Save imported devices
                int successCount = 0;
                int errorCount = 0;
                var errors = new List<string>();

                foreach (var device in devicesList)
                {
                    try
                    {
                        // Validate required fields
                        if (string.IsNullOrWhiteSpace(device.Responsible) && string.IsNullOrWhiteSpace(device.Location))
                        {
                            errorCount++;
                            errors.Add($"جهاز بدون مسؤول أو موقع محدد");
                            continue;
                        }

                        // Handle NetworkId - convert network name to ID if needed
                        if (!string.IsNullOrWhiteSpace(device.NetworkId))
                        {
                            // Check if NetworkId is already a valid GUID
                            if (!Guid.TryParse(device.NetworkId, out _))
                            {
                                // Try to find network by name
                                if (networkLookup.TryGetValue(device.NetworkId, out var networkId))
                                {
                                    device.NetworkId = networkId;
                                }
                                else
                                {
                                    // Network not found, set to null to allow creation without network
                                    device.NetworkId = null;
                                }
                            }
                        }

                        // Set default values for required fields
                        device.Id = Guid.NewGuid().ToString();
                        device.CreatedAt = DateTime.Now;
                        device.UpdatedAt = DateTime.Now;

                        // Set default status if empty
                        if (string.IsNullOrWhiteSpace(device.Status))
                        {
                            device.Status = "غير محدد";
                        }

                        // Trim and validate field lengths to prevent database errors
                        device.Responsible = TrimToMaxLength(device.Responsible, 100);
                        device.Type = TrimToMaxLength(device.Type, 50);
                        device.Location = TrimToMaxLength(device.Location, 200);
                        device.Phone = TrimToMaxLength(device.Phone, 20);
                        device.Ip = TrimToMaxLength(device.Ip, 15);
                        device.PowerConnection = TrimToMaxLength(device.PowerConnection, 100);
                        device.AdapterType = TrimToMaxLength(device.AdapterType, 50);
                        device.ConnectionMethod = TrimToMaxLength(device.ConnectionMethod, 50);
                        device.LinkedNetwork = TrimToMaxLength(device.LinkedNetwork, 50);
                        device.BroadcastNetworkName = TrimToMaxLength(device.BroadcastNetworkName, 100);
                        device.Status = TrimToMaxLength(device.Status, 20);
                        device.SiteId = TrimToMaxLength(device.SiteId, 50);

                        await _deviceService.CreateAsync(device);
                        successCount++;
                    }
                    catch (Exception ex)
                    {
                        errorCount++;
                        var deviceName = device.Responsible ?? device.Location ?? "جهاز غير محدد";
                        errors.Add($"خطأ في حفظ جهاز {deviceName}: {ex.Message}");
                    }
                }

                // Refresh the list
                await LoadDevicesAsync();

                // عرض نتائج الاستيراد
                MessageHelper.ShowOperationResults("استيراد", successCount, errorCount, errors.ToArray());
            }
            catch (Exception ex)
            {
                MessageHelper.HandleException(ex, "استيراد البيانات");
            }
            finally
            {
                IsLoading = false;
            }
        }

        // CanExecute methods for commands
        private bool CanEditDevice(Device? device = null)
        {
            var deviceToEdit = device ?? SelectedDevice;
            return deviceToEdit != null && PermissionHelper.CanEditItem(_authService, deviceToEdit.NetworkId);
        }

        private bool CanDeleteDevice(Device? device = null)
        {
            var deviceToDelete = device ?? SelectedDevice;
            return deviceToDelete != null && PermissionHelper.CanDeleteItem(_authService, deviceToDelete.NetworkId);
        }

        private bool CanPingDevices()
        {
            return !IsPinging && !IsLoading;
        }

        private bool CanDeleteSelectedDevices()
        {
            return !IsLoading && DeviceFilterHelper.GetSelectedDevices(Devices).Any();
        }

        private bool CanImportDevices()
        {
            return !IsLoading && CanAddDevices;
        }

        private bool CanExportSelectedDevices()
        {
            return !IsLoading && DeviceFilterHelper.GetSelectedDevices(Devices).Any();
        }

        private bool CanExportDevices()
        {
            return !IsLoading;
        }

        private bool CanClearFilters()
        {
            return !IsLoading && (!string.IsNullOrEmpty(SearchText) || !string.IsNullOrEmpty(StatusFilter) || (!string.IsNullOrEmpty(TypeFilter) && TypeFilter != "الكل") || (!string.IsNullOrEmpty(SelectedIpRange) && SelectedIpRange != "جميع النطاقات"));
        }

        // معالج تغيير الجهاز المحدد
        partial void OnSelectedDeviceChanged(Device? value)
        {
            // تحديث حالة أوامر التعديل والحذف سيتم تلقائياً
            // عبر نظام CommunityToolkit.Mvvm
            EditDeviceCommand.NotifyCanExecuteChanged();
            DeleteDeviceCommand.NotifyCanExecuteChanged();
        }

        // معالج تغيير حالة التحميل والفحص
        partial void OnIsLoadingChanged(bool value)
        {
            PingAllDevicesCommand.NotifyCanExecuteChanged();
            PingSelectedDevicesCommand.NotifyCanExecuteChanged();
            DeleteSelectedDevicesCommand.NotifyCanExecuteChanged();
            ImportDevicesCommand.NotifyCanExecuteChanged();
            ExportSelectedDevicesCommand.NotifyCanExecuteChanged();
            ExportDevicesCommand.NotifyCanExecuteChanged();
            ClearFiltersCommand.NotifyCanExecuteChanged();
        }

        partial void OnIsPingingChanged(bool value)
        {
            PingAllDevicesCommand.NotifyCanExecuteChanged();
            PingSelectedDevicesCommand.NotifyCanExecuteChanged();
        }

        // Helper method to trim strings to maximum length
        private static string? TrimToMaxLength(string? value, int maxLength)
        {
            if (string.IsNullOrEmpty(value))
                return value;

            return value.Length > maxLength ? value.Substring(0, maxLength) : value;
        }

        // معالج تغيير نص البحث للبحث التلقائي
        partial void OnSearchTextChanged(string value)
        {
            // تأخير البحث لتجنب البحث المفرط أثناء الكتابة
            _ = DelayedFilterAsync(value, 500);

            // حفظ حالة الفلتر
            SaveFilterState();

            // تحديث حالة أمر مسح الفلاتر
            ClearFiltersCommand.NotifyCanExecuteChanged();
        }

        private async System.Threading.Tasks.Task InitializeAutoRefreshAsync()
        {
            try
            {
                var settings = await _settingsService.GetSettingsAsync();
                AutoRefreshEnabled = settings.AutoPingEnabled;

                if (AutoRefreshEnabled)
                {
                    StartAutoRefresh();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error initializing auto refresh: {ex.Message}");
            }
        }

        [RelayCommand]
        private void ToggleAutoRefresh()
        {
            AutoRefreshEnabled = !AutoRefreshEnabled;

            if (AutoRefreshEnabled)
            {
                StartAutoRefresh();
            }
            else
            {
                StopAutoRefresh();
            }
        }

        private async void StartAutoRefresh()
        {
            StopAutoRefresh(); // إيقاف أي تحديث سابق

            try
            {
                // الحصول على إعدادات الفحص التلقائي
                var settings = await _settingsService.GetSettingsAsync();

                if (!settings.AutoPingEnabled)
                {
                    AutoRefreshEnabled = false;
                    return;
                }

                var intervalSeconds = Math.Max(settings.AutoPingInterval, 10); // الحد الأدنى 10 ثواني

                _refreshTimer = new System.Threading.Timer(async _ =>
                {
                    if (!IsPinging && !IsLoading)
                    {
                        await System.Windows.Application.Current.Dispatcher.InvokeAsync(async () =>
                        {
                            try
                            {
                                // الحصول على جميع الأجهزة من قاعدة البيانات للفحص التلقائي
                                var networkFilter = PermissionHelper.GetNetworkFilter(_authService);
                                var allDevices = await _deviceService.GetAllAsync(networkFilter);
                                var permissionFilteredDevices = PermissionHelper.ApplyPermissionFilter(allDevices, _authService, d => d.NetworkId);
                                var devicesWithIp = DeviceFilterHelper.GetDevicesWithValidIp(permissionFilteredDevices).ToList();

                                if (devicesWithIp.Any())
                                {
                                    await PingDevicesAsync(devicesWithIp, "جميع الأجهزة", false);
                                }
                            }
                            catch (Exception ex)
                            {
                                System.Diagnostics.Debug.WriteLine($"Error in auto refresh: {ex.Message}");
                            }
                        });
                    }
                }, null, TimeSpan.FromSeconds(intervalSeconds), TimeSpan.FromSeconds(intervalSeconds));
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error starting auto refresh: {ex.Message}");
                AutoRefreshEnabled = false;
            }
        }

        private void StopAutoRefresh()
        {
            _refreshTimer?.Dispose();
            _refreshTimer = null;
        }

        /// <summary>
        /// تحديث حالات الأجهزة في الذاكرة بدلاً من إعادة التحميل الكامل
        /// </summary>
        private async System.Threading.Tasks.Task UpdateDeviceStatusesInMemoryAsync(Dictionary<string, string> deviceStatuses)
        {
            await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
            {
                var updateTime = DateTime.Now;

                foreach (var deviceStatus in deviceStatuses)
                {
                    var device = Devices.FirstOrDefault(d => d.Id == deviceStatus.Key);
                    if (device != null)
                    {
                        device.Status = deviceStatus.Value;
                        device.LastCheck = updateTime;
                        device.UpdatedAt = updateTime;
                    }
                }

                // تحديث الإحصائيات
                UpdateStatistics();
            });
        }

        /// <summary>
        /// تحديث الإحصائيات بناءً على البيانات الحالية في الذاكرة
        /// </summary>
        private void UpdateStatistics()
        {
            try
            {
                var stats = DeviceFilterHelper.GetDeviceStatistics(Devices);
                // تحديث عدد الأجهزة المحددة
                SelectedDevicesCount = stats.SelectedDevices;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating statistics: {ex.Message}");
            }
        }

        [RelayCommand(CanExecute = nameof(CanClearFilters))]
        private async System.Threading.Tasks.Task ClearFiltersAsync()
        {
            SearchText = string.Empty;
            StatusFilter = string.Empty;
            TypeFilter = "الكل";
            SelectedIpRange = "جميع النطاقات";
            await ApplyFiltersAsync();
        }



        /// <summary>
        /// حفظ حالة الفلاتر الحالية
        /// </summary>
        public void SaveFilterState()
        {
            try
            {
                var filterState = new
                {
                    SearchText = SearchText ?? string.Empty,
                    StatusFilter = StatusFilter ?? string.Empty,
                    TypeFilter = TypeFilter ?? "الكل"
                };

                var json = System.Text.Json.JsonSerializer.Serialize(filterState);

                // حفظ في Registry باستخدام نفس آلية SettingsService
                using (var key = Microsoft.Win32.Registry.CurrentUser.CreateSubKey(@"SOFTWARE\NetworkManagement"))
                {
                    key?.SetValue("DevicesFilterState", json);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving filter state: {ex.Message}");
            }
        }

        /// <summary>
        /// استعادة حالة الفلاتر المحفوظة
        /// </summary>
        public void RestoreFilterState()
        {
            try
            {
                // قراءة من Registry
                using (var key = Microsoft.Win32.Registry.CurrentUser.OpenSubKey(@"SOFTWARE\NetworkManagement"))
                {
                    var json = key?.GetValue("DevicesFilterState")?.ToString();
                    if (!string.IsNullOrEmpty(json))
                    {
                        var filterState = System.Text.Json.JsonSerializer.Deserialize<System.Text.Json.JsonElement>(json);

                        // استعادة الفلاتر بدون تشغيل البحث التلقائي
                        if (filterState.TryGetProperty("SearchText", out var searchTextElement))
                        {
                            SearchText = searchTextElement.GetString() ?? string.Empty;
                        }

                        if (filterState.TryGetProperty("StatusFilter", out var statusFilterElement))
                        {
                            StatusFilter = statusFilterElement.GetString() ?? string.Empty;
                        }

                        if (filterState.TryGetProperty("TypeFilter", out var typeFilterElement))
                        {
                            TypeFilter = typeFilterElement.GetString() ?? "الكل";
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error restoring filter state: {ex.Message}");
            }
        }

        // معالج تغيير فلتر الحالة
        partial void OnStatusFilterChanged(string value)
        {
            _ = DelayedFilterAsync(value, 300);
            SaveFilterState();
            ClearFiltersCommand.NotifyCanExecuteChanged();
        }

        // معالج تغيير فلتر النوع
        partial void OnTypeFilterChanged(string value)
        {
            _ = DelayedFilterAsync(value, 300);
            SaveFilterState();
            ClearFiltersCommand.NotifyCanExecuteChanged();
        }

        // معالج تغيير فلتر نطاق IP
        partial void OnSelectedIpRangeChanged(string value)
        {
            _ = DelayedFilterAsync(value, 300);
            ClearFiltersCommand.NotifyCanExecuteChanged();
        }

        private async System.Threading.Tasks.Task DelayedFilterAsync(string? triggerValue = null, int delayMs = 300)
        {
            try
            {
                await System.Threading.Tasks.Task.Delay(delayMs);

                // التأكد أن القيمة لم تتغير (للبحث النصي فقط)
                if (triggerValue != null && SearchText != triggerValue)
                    return;

                await System.Windows.Application.Current.Dispatcher.InvokeAsync(async () =>
                {
                    await ApplyFiltersAsync();
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in delayed filter: {ex.Message}");
            }
        }

        private async System.Threading.Tasks.Task ApplyFiltersAsync()
        {
            try
            {
                IsLoading = true;

                // الحصول على البيانات المفلترة
                var filteredDevices = await GetFilteredDevicesAsync();

                await UpdateDevicesCollectionAsync(filteredDevices);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error applying filters: {ex.Message}");
                MessageHelper.HandleException(ex, "تطبيق الفلاتر");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// الحصول على الأجهزة المفلترة حسب جميع المعايير
        /// </summary>
        private async System.Threading.Tasks.Task<IEnumerable<Device>> GetFilteredDevicesAsync()
        {
            // تطبيق فلتر الشبكة المحدد من MainViewModel
            string? networkFilter = _networkFilter;

            // إذا لم يكن هناك فلتر شبكة محدد، استخدم فلترة الصلاحيات العادية
            if (string.IsNullOrEmpty(networkFilter))
            {
                networkFilter = PermissionHelper.GetNetworkFilter(_authService);
            }

            // تحميل الأجهزة مع فلتر الشبكة
            var allDevices = await _deviceService.GetAllAsync(networkFilter);

            // تطبيق فلترة الصلاحيات الإضافية
            var permissionFilteredDevices = PermissionHelper.ApplyPermissionFilter(
                allDevices,
                _authService,
                device => device.NetworkId);

            // تطبيق جميع الفلاتر باستخدام Helper موحد
            var filteredDevices = DeviceFilterHelper.ApplyAllFilters(
                permissionFilteredDevices,
                SearchText,
                StatusFilter,
                TypeFilter);

            // تطبيق فلتر نطاق IP
            if (!string.IsNullOrEmpty(SelectedIpRange) && SelectedIpRange != "جميع النطاقات")
            {
                var rangePrefix = SelectedIpRange.Replace(".x", "");
                filteredDevices = filteredDevices.Where(d =>
                    !string.IsNullOrWhiteSpace(d.Ip) &&
                    d.Ip.StartsWith(rangePrefix));
            }

            return filteredDevices;
        }

        /// <summary>
        /// تحديث مجموعة الأجهزة في الواجهة
        /// </summary>
        /// <param name="devices">الأجهزة الجديدة</param>
        private async System.Threading.Tasks.Task UpdateDevicesCollectionAsync(IEnumerable<Device> devices)
        {
            await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
            {
                try
                {
                    System.Diagnostics.Debug.WriteLine($"UpdateDevicesCollectionAsync: بدء تحديث مجموعة الأجهزة");

                    // إلغاء الاشتراك في الأجهزة القديمة لمنع تسريب الذاكرة
                    UnsubscribeFromDeviceEvents();

                    // مسح المجموعة الحالية
                    Devices.Clear();

                    // تحويل إلى قائمة لتجنب مشاكل التكرار
                    var devicesList = devices.ToList();
                    System.Diagnostics.Debug.WriteLine($"UpdateDevicesCollectionAsync: سيتم إضافة {devicesList.Count} جهاز");

                    // إضافة الأجهزة الجديدة مع الاشتراك في الأحداث
                    foreach (var device in devicesList)
                    {
                        // الاشتراك في PropertyChanged للتحديد
                        device.PropertyChanged += Device_PropertyChanged;
                        Devices.Add(device);
                    }

                    System.Diagnostics.Debug.WriteLine($"UpdateDevicesCollectionAsync: تم إضافة {Devices.Count} جهاز إلى المجموعة");

                    // تحديث عدد الأجهزة المحددة
                    UpdateSelectedDevicesCount();

                    // إشعار بتغيير الخصائص المرتبطة
                    OnPropertyChanged(nameof(Devices));

                    // تحديث إضافي للتأكد من عرض البيانات
                    System.Windows.Data.BindingOperations.EnableCollectionSynchronization(Devices, new object());
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"UpdateDevicesCollectionAsync: خطأ - {ex.Message}");
                    throw;
                }
            });
        }

        [RelayCommand]
        private static void OpenIpInBrowser(string? ip)
        {
            if (string.IsNullOrWhiteSpace(ip))
                return;

            try
            {
                var url = ip.StartsWith("http") ? ip : $"http://{ip}";
                System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                {
                    FileName = url,
                    UseShellExecute = true
                });
            }
            catch (Exception ex)
            {
                MessageHelper.ShowErrorMessage($"فتح العنوان {ip} في المتصفح", ex.Message);
            }
        }

        [RelayCommand]
        private void SelectAllDevices()
        {
            System.Diagnostics.Debug.WriteLine("SelectAllDevices command executed");
            var allSelected = Devices.All(d => d.IsSelected);

            // تحديث التحديد على UI Thread
            System.Windows.Application.Current?.Dispatcher?.Invoke(() =>
            {
                foreach (var device in Devices)
                {
                    device.IsSelected = !allSelected;
                }
                UpdateSelectedDevicesCount();
            });
        }

        private void UpdateSelectedDevicesCount()
        {
            SelectedDevicesCount = DeviceFilterHelper.GetSelectedDevices(Devices).Count();
            DeleteSelectedDevicesCommand.NotifyCanExecuteChanged();
            ExportSelectedDevicesCommand.NotifyCanExecuteChanged();
        }

        private void Device_PropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(Device.IsSelected))
            {
                // تحديث العداد على UI Thread
                System.Windows.Application.Current?.Dispatcher?.Invoke(() =>
                {
                    UpdateSelectedDevicesCount();
                });
            }
        }

        [RelayCommand(CanExecute = nameof(CanPingDevices))]
        private async System.Threading.Tasks.Task PingSelectedDevicesAsync()
        {
            var selectedDevices = DeviceFilterHelper.GetSelectedDevices(Devices, withValidIp: true).ToList();

            if (!selectedDevices.Any())
            {
                MessageHelper.ShowWarningMessage("يرجى تحديد أجهزة بعناوين IP صالحة للفحص");
                return;
            }

            await PingDevicesAsync(selectedDevices, "الأجهزة المحددة", true);
        }

        /// <summary>
        /// دالة موحدة لفحص الأجهزة
        /// </summary>
        private async System.Threading.Tasks.Task PingDevicesAsync(List<Device> devices, string deviceType, bool showResults = false)
        {
            if (IsPinging)
            {
                MessageHelper.ShowWarningMessage("عملية فحص أخرى قيد التنفيذ، يرجى الانتظار");
                return;
            }

            if (!devices.Any())
            {
                MessageHelper.ShowWarningMessage($"لا توجد أجهزة بعناوين IP للفحص في {deviceType}");
                return;
            }

            try
            {
                IsPinging = true;
                PingStatus = $"جاري فحص {devices.Count} جهاز من {deviceType}...";

                var pingResults = await _pingService.PingMultipleDevicesAsync(devices);
                var deviceStatuses = new Dictionary<string, string>();

                foreach (var device in devices)
                {
                    if (device.CanPing && pingResults.ContainsKey(device.Id))
                    {
                        // الأجهزة التي حالتها "يعمل" - تحديث حالتها حسب نتيجة الـ ping
                        var status = pingResults[device.Id] ? "متصل" : "غير متصل";
                        deviceStatuses[device.Id] = status;
                    }
                    else if (!device.CanPing)
                    {
                        // الأجهزة التي حالتها "عاطل" أو "مسحوب" - تحافظ على حالتها الأصلية
                        deviceStatuses[device.Id] = device.Status ?? "غير محدد";
                    }
                }

                var updatedCount = await _deviceService.UpdateMultipleDeviceStatusesAsync(deviceStatuses);

                // تحديث الحالات في الذاكرة بدلاً من إعادة التحميل الكامل
                await UpdateDeviceStatusesInMemoryAsync(deviceStatuses);

                PingStatus = $"تم فحص {updatedCount} جهاز بنجاح";

                // عرض نتائج الفحص إذا كان مطلوباً
                if (showResults)
                {
                    var connectedCount = deviceStatuses.Count(s => s.Value == "متصل");
                    var disconnectedCount = deviceStatuses.Count(s => s.Value == "غير متصل");
                    var workingDevicesCount = devices.Count(d => d.CanPing);
                    var nonWorkingDevicesCount = devices.Count(d => !d.CanPing);

                    var resultMessage = $"تم فحص {workingDevicesCount} جهاز (حالة: يعمل) من {deviceType}\n" +
                                      $"الأجهزة المتصلة: {connectedCount}\n" +
                                      $"الأجهزة غير المتصلة: {disconnectedCount}\n";

                    if (nonWorkingDevicesCount > 0)
                    {
                        resultMessage += $"الأجهزة المتجاهلة (عاطل/مسحوب): {nonWorkingDevicesCount}";
                    }

                    MessageHelper.ShowInfoMessage(resultMessage, "نتائج الفحص");
                }
            }
            catch (Exception ex)
            {
                MessageHelper.HandleException(ex, $"فحص {deviceType}");
            }
            finally
            {
                IsPinging = false;
                PingStatus = string.Empty;
            }
        }



        [RelayCommand(CanExecute = nameof(CanDeleteSelectedDevices))]
        private async System.Threading.Tasks.Task DeleteSelectedDevicesAsync()
        {
            if (IsLoading)
            {
                MessageHelper.ShowWarningMessage("عملية أخرى قيد التنفيذ، يرجى الانتظار");
                return;
            }

            var selectedDevices = DeviceFilterHelper.GetSelectedDevices(Devices).ToList();

            if (!selectedDevices.Any())
            {
                MessageHelper.ShowWarningMessage("يرجى تحديد أجهزة للحذف");
                return;
            }

            // التحقق من الصلاحيات لكل جهاز
            var unauthorizedDevices = selectedDevices.Where(d => !PermissionHelper.CanDeleteItem(_authService, d.NetworkId)).ToList();
            if (unauthorizedDevices.Any())
            {
                var unauthorizedNames = unauthorizedDevices.Select(d => d.Responsible ?? d.Location ?? "جهاز غير محدد").Take(3);
                var message = $"لا تملك صلاحية حذف بعض الأجهزة المحددة:\n{string.Join(", ", unauthorizedNames)}";
                if (unauthorizedDevices.Count > 3)
                    message += $"\nو {unauthorizedDevices.Count - 3} أجهزة أخرى";

                MessageHelper.ShowWarningMessage(message);
                return;
            }

            // تحضير أسماء الأجهزة للعرض
            var deviceNames = selectedDevices.Select(d => d.Responsible ?? d.Location ?? "جهاز غير محدد").ToArray();

            if (MessageHelper.ShowMultipleDeleteConfirmation("جهاز", selectedDevices.Count, deviceNames))
            {
                try
                {
                    IsLoading = true;
                    int deletedCount = 0;
                    var errors = new List<string>();

                    foreach (var device in selectedDevices)
                    {
                        try
                        {
                            // إرجاع المواد إلى المخزون قبل الحذف
                            var networkId = _authService.CurrentUser?.NetworkId;
                            var inventoryResult = await _deviceInventoryService.RestoreInventoryForDeletedDeviceAsync(device, networkId);

                            if (inventoryResult.Success)
                            {
                                System.Diagnostics.Debug.WriteLine($"تم إرجاع المواد إلى المخزون عند حذف الجهاز: {device.Responsible ?? device.Location}");
                            }

                            await _deviceService.DeleteAsync(device.Id);
                            deletedCount++;
                        }
                        catch (Exception ex)
                        {
                            errors.Add($"فشل حذف {device.Responsible ?? device.Location}: {ex.Message}");
                        }
                    }

                    await LoadDevicesAsync();

                    // إشعار نتائج الحذف المتعدد
                    var userName = _authService.CurrentUser?.Name ?? "المستخدم";
                    var userId = _authService.CurrentUser?.Id;
                    if (errors.Count == 0)
                    {
                        await _notificationService.ShowOperationAsync(
                            "تم حذف الأجهزة",
                            $"قام {userName} بحذف {deletedCount} جهاز بنجاح",
                            NotificationType.Success,
                            userName, userId, "حذف متعدد", $"{deletedCount} جهاز", null);
                    }
                    else
                    {
                        await _notificationService.ShowOperationAsync(
                            "حذف الأجهزة مكتمل جزئياً",
                            $"قام {userName} بحذف {deletedCount} جهاز، فشل في حذف {errors.Count} جهاز",
                            NotificationType.Warning,
                            userName, userId, "حذف متعدد", $"{deletedCount} جهاز", null);
                    }
                }
                catch (Exception ex)
                {
                    MessageHelper.HandleException(ex, "حذف الأجهزة");
                }
                finally
                {
                    IsLoading = false;
                }
            }
        }

        [RelayCommand(CanExecute = nameof(CanExportSelectedDevices))]
        private async System.Threading.Tasks.Task ExportSelectedDevicesAsync()
        {
            var selectedDevices = DeviceFilterHelper.GetSelectedDevices(Devices).ToList();

            if (!selectedDevices.Any())
            {
                MessageHelper.ShowWarningMessage("يرجى تحديد أجهزة للتصدير");
                return;
            }

            try
            {
                IsLoading = true;

                var filter = "Excel Files (*.xlsx)|*.xlsx|CSV Files (*.csv)|*.csv";
                var defaultFileName = $"الأجهزة_المحددة_{DateTime.Now:yyyy-MM-dd}";

                var filePath = await _exportService.GetSaveFilePathAsync(defaultFileName, filter);
                if (string.IsNullOrEmpty(filePath))
                    return;

                string result = "";
                var isExcel = filePath.EndsWith(".xlsx", StringComparison.OrdinalIgnoreCase);

                result = isExcel
                    ? await _exportService.ExportDevicesReportToExcelAsync(selectedDevices, filePath)
                    : await _exportService.ExportDevicesReportToCsvAsync(selectedDevices, filePath);

                if (!string.IsNullOrEmpty(result))
                {
                    MessageHelper.ShowSuccessMessage("تصدير", $"{selectedDevices.Count} جهاز محدد", $"تم الحفظ في:\n{result}");
                }
            }
            catch (Exception ex)
            {
                MessageHelper.HandleException(ex, "تصدير الأجهزة المحددة");
            }
            finally
            {
                IsLoading = false;
            }
        }

        // معالج تغيير المستخدم لتحديث الصلاحيات
        private async void OnUserChanged(object? sender, User? user)
        {
            try
            {
                // إلغاء أي عملية تحميل سابقة بشكل آمن
                CancelCurrentLoadOperation();

                // تحديث خصائص الصلاحيات على UI thread
                await UpdatePermissionPropertiesAsync();

                // تأخير قصير لتجنب التحديثات المتكررة
                await System.Threading.Tasks.Task.Delay(100, _loadCancellationTokenSource?.Token ?? default);

                // إعادة تحميل البيانات حسب الصلاحيات الجديدة
                await LoadDevicesAsync(_loadCancellationTokenSource?.Token ?? default);
            }
            catch (OperationCanceledException)
            {
                // تم إلغاء العملية - هذا طبيعي
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in OnUserChanged: {ex.Message}");
            }
        }

        /// <summary>
        /// إلغاء عملية التحميل الحالية بشكل آمن
        /// </summary>
        private void CancelCurrentLoadOperation()
        {
            lock (_loadLock)
            {
                _loadCancellationTokenSource?.Cancel();
                _loadCancellationTokenSource?.Dispose();
                _loadCancellationTokenSource = new System.Threading.CancellationTokenSource();
            }
        }

        /// <summary>
        /// تحديث خصائص الصلاحيات على UI thread
        /// </summary>
        private async System.Threading.Tasks.Task UpdatePermissionPropertiesAsync()
        {
            await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
            {
                OnPropertyChanged(nameof(CanAddDevices));
                OnPropertyChanged(nameof(CanEditDevices));
                OnPropertyChanged(nameof(CanDeleteDevices));
                OnPropertyChanged(nameof(CanManageDevices));
            });
        }

        // تنظيف الموارد
        public void Dispose()
        {
            try
            {
                StopAutoRefresh();

                // إلغاء الاشتراك في تغيير المستخدم
                _authService.UserChanged -= OnUserChanged;

                // إلغاء أي عمليات تحميل جارية
                CancelCurrentLoadOperation();

                // تنظيف Semaphore
                _loadSemaphore?.Dispose();

                // Unsubscribe from all device events to prevent memory leaks
                UnsubscribeFromDeviceEvents();

                Devices.Clear();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error disposing DevicesViewModel: {ex.Message}");
            }
        }

        /// <summary>
        /// إلغاء الاشتراك في أحداث الأجهزة لمنع تسريب الذاكرة
        /// </summary>
        private void UnsubscribeFromDeviceEvents()
        {
            try
            {
                foreach (var device in Devices)
                {
                    device.PropertyChanged -= Device_PropertyChanged;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error unsubscribing from device events: {ex.Message}");
            }
        }

        /// <summary>
        /// تحميل أنواع الأجهزة للفلتر
        /// </summary>
        private async System.Threading.Tasks.Task LoadDeviceTypesAsync()
        {
            try
            {
                var types = await _deviceService.GetAllDeviceTypesAsync();
                var typesList = new List<string> { "الكل" }; // إضافة خيار "الكل" في البداية
                typesList.AddRange(types);
                AvailableDeviceTypes = typesList;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading device types: {ex.Message}");
                // في حالة الخطأ، استخدام الأنواع الافتراضية
                AvailableDeviceTypes = new List<string> { "الكل", "Router", "Switch", "Access Point", "Firewall", "Server", "أخرى" };
            }
        }

        #region Column Management

        /// <summary>
        /// تهيئة إعدادات الأعمدة الافتراضية
        /// </summary>
        private void InitializeColumnSettings()
        {
            ColumnSettings = new ObservableCollection<ColumnInfo>
            {
                new ColumnInfo { Name = "RowNumber", Header = "#", IsVisible = true, Width = 40, Order = 0, CanHide = false, CanReorder = false },
                new ColumnInfo { Name = "Selection", Header = "تحديد", IsVisible = true, Width = 50, Order = 1, CanHide = true, CanReorder = true },
                new ColumnInfo { Name = "Ip", Header = "عنوان IP", IsVisible = true, Width = 140, Order = 2, CanHide = false, CanReorder = true },
                new ColumnInfo { Name = "Status", Header = "الحالة", IsVisible = true, Width = 100, Order = 3, CanHide = false, CanReorder = true },
                new ColumnInfo { Name = "Name", Header = "اسم الجهاز", IsVisible = true, Width = 150, Order = 4, CanHide = false, CanReorder = true },
                new ColumnInfo { Name = "Type", Header = "النوع", IsVisible = true, Width = 120, Order = 5, CanHide = true, CanReorder = true },
                new ColumnInfo { Name = "Location", Header = "الموقع", IsVisible = true, Width = 120, Order = 6, CanHide = true, CanReorder = true },
                new ColumnInfo { Name = "Network", Header = "الشبكة", IsVisible = true, Width = 120, Order = 7, CanHide = true, CanReorder = true },
                new ColumnInfo { Name = "LastSeen", Header = "آخر ظهور", IsVisible = true, Width = 140, Order = 8, CanHide = true, CanReorder = true },
                new ColumnInfo { Name = "Actions", Header = "الإجراءات", IsVisible = true, Width = 120, Order = 9, CanHide = false, CanReorder = false }
            };
        }

        /// <summary>
        /// فتح نافذة إعدادات الأعمدة
        /// </summary>
        [RelayCommand]
        private void OpenColumnSettings()
        {
            try
            {
                var dialogViewModel = App.GetService<ColumnSettingsDialogViewModel>();
                dialogViewModel.InitializeColumns(ColumnSettings);

                var dialog = new ColumnSettingsDialog(dialogViewModel);

                dialogViewModel.SettingsSaved += (s, newSettings) =>
                {
                    // تطبيق الإعدادات الجديدة
                    ApplyColumnSettings(newSettings);
                };

                dialog.ShowDialog();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error opening column settings: {ex.Message}");
                System.Windows.MessageBox.Show(
                    $"حدث خطأ أثناء فتح إعدادات الأعمدة:\n{ex.Message}",
                    "خطأ",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تطبيق إعدادات الأعمدة الجديدة
        /// </summary>
        private void ApplyColumnSettings(ObservableCollection<ColumnInfo> newSettings)
        {
            try
            {
                // تحديث إعدادات الأعمدة
                ColumnSettings.Clear();
                foreach (var setting in newSettings.OrderBy(c => c.Order))
                {
                    ColumnSettings.Add(setting);
                }

                // إشعار التحديث للواجهة
                OnPropertyChanged(nameof(ColumnSettings));

                System.Windows.MessageBox.Show(
                    "تم تطبيق إعدادات الأعمدة بنجاح!",
                    "نجح التطبيق",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error applying column settings: {ex.Message}");
                System.Windows.MessageBox.Show(
                    $"حدث خطأ أثناء تطبيق إعدادات الأعمدة:\n{ex.Message}",
                    "خطأ",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Error);
            }
        }

        #endregion
    }
}

