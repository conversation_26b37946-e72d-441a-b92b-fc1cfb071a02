using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using NetworkManagement.Models;

namespace NetworkManagement.Services
{
    public class InventoryStatistics
    {
        public int TotalItems { get; set; }
        public int LowStockCount { get; set; }
        public decimal TotalValue { get; set; }
        public int TotalQuantity { get; set; }
        public string TotalValueDisplay => $"{TotalValue:N0} ر.ي";
    }

    public interface IInventoryService
    {
        Task<IEnumerable<Inventory>> GetAllAsync(string? networkFilter = null, CancellationToken cancellationToken = default);
        Task<Inventory?> GetByIdAsync(string id, CancellationToken cancellationToken = default);
        Task<Inventory> CreateAsync(Inventory inventory, CancellationToken cancellationToken = default);
        Task<Inventory> UpdateAsync(Inventory inventory, CancellationToken cancellationToken = default);
        Task<bool> DeleteAsync(string id, CancellationToken cancellationToken = default);
        Task<IEnumerable<Inventory>> SearchAsync(string searchTerm, string? networkFilter = null, CancellationToken cancellationToken = default);
        Task<IEnumerable<Inventory>> GetLowStockItemsAsync(string? networkFilter = null, CancellationToken cancellationToken = default);
        Task<InventoryStatistics> GetStatisticsAsync(string? networkFilter = null, CancellationToken cancellationToken = default);
    }
}

