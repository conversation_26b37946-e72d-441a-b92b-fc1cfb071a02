# برومت مفصل لصفحة تسجيل الدخول - LoginWindow

## وصف الصفحة العام
صفحة تسجيل الدخول هي النقطة الأولى للدخول إلى نظام Shabaka Pro. يجب أن تكون أنيقة، آمنة، وسهلة الاستخدام مع دعم كامل للغة العربية والتصميم المتجاوب.

## المتطلبات التقنية
- **النوع**: Window (نافذة منفصلة)
- **الحجم**: 450x600 بكسل (ثابت، غير قابل لتغيير الحجم)
- **الموضع**: وسط الشاشة
- **الثيم**: دعم الثيم الفاتح والداكن
- **اللغة**: عربية كاملة مع تخطيط RTL

## التصميم المرئي

### الألوان:
- **الخلفية الرئيسية**: أبيض (#FFFFFF) للثيم الفاتح، رمادي داكن (#212121) للثيم الداكن
- **اللون الأساسي**: أزرق Material Design (#2196F3)
- **لون النص**: رمادي داكن (#424242) للثيم الفاتح، أبيض (#FFFFFF) للثيم الداكن
- **لون الخطأ**: أحمر (#F44336)
- **لون النجاح**: أخضر (#4CAF50)

### الخطوط:
- **الخط الأساسي**: Segoe UI أو Tahoma للعربية
- **حجم العنوان الرئيسي**: 24px، وزن Bold
- **حجم النص العادي**: 14px، وزن Normal
- **حجم النص الصغير**: 12px، وزن Normal

## العناصر المطلوبة

### 1. منطقة الشعار والعنوان (Header Section):
- **شعار التطبيق**: 
  - حجم: 80x80 بكسل
  - موضع: وسط أعلى النافذة
  - مسار الصورة: Resources/shabaka-pro.ico
- **عنوان التطبيق**:
  - النص: "نظام إدارة الشبكات المتقدم"
  - النص الفرعي: "Shabaka Pro v2.0.0"
  - محاذاة: وسط
  - لون: اللون الأساسي للتطبيق

### 2. نموذج تسجيل الدخول (Login Form):
#### أ. حقل اسم المستخدم:
- **النوع**: TextBox مع Material Design
- **التسمية**: "اسم المستخدم"
- **Placeholder**: "أدخل اسم المستخدم"
- **الأيقونة**: Person icon من Material Design
- **التحقق**: 
  - مطلوب (Required)
  - الحد الأدنى: 3 أحرف
  - الحد الأقصى: 50 حرف
- **رسالة الخطأ**: "اسم المستخدم مطلوب ويجب أن يكون بين 3-50 حرف"

#### ب. حقل كلمة المرور:
- **النوع**: PasswordBox مع Material Design
- **التسمية**: "كلمة المرور"
- **Placeholder**: "أدخل كلمة المرور"
- **الأيقونة**: Lock icon من Material Design
- **زر إظهار/إخفاء**: Eye/EyeOff icon
- **التحقق**:
  - مطلوب (Required)
  - الحد الأدنى: 6 أحرف
- **رسالة الخطأ**: "كلمة المرور مطلوبة ويجب أن تكون 6 أحرف على الأقل"

#### ج. خيار تذكر بيانات الدخول:
- **النوع**: CheckBox مع Material Design
- **النص**: "تذكر بيانات الدخول"
- **الوظيفة**: حفظ اسم المستخدم في الإعدادات المحلية
- **التخزين**: في ملف إعدادات مشفر

### 3. أزرار العمل (Action Buttons):
#### أ. زر تسجيل الدخول:
- **النوع**: Button مع Material Design Raised Style
- **النص**: "تسجيل الدخول"
- **الأيقونة**: Login icon
- **اللون**: اللون الأساسي للتطبيق
- **الحجم**: عرض كامل، ارتفاع 45px
- **التفعيل**: يتم تفعيله فقط عند ملء الحقول المطلوبة
- **التحميل**: يظهر مؤشر تحميل أثناء المعالجة

#### ب. زر إعدادات قاعدة البيانات:
- **النوع**: Button مع Material Design Outlined Style
- **النص**: "إعدادات قاعدة البيانات"
- **الأيقونة**: Database icon
- **الموضع**: أسفل زر تسجيل الدخول
- **الوظيفة**: فتح نافذة إعدادات الاتصال بقاعدة البيانات

### 4. منطقة الرسائل (Messages Area):
- **رسائل الخطأ**: 
  - لون أحمر (#F44336)
  - أيقونة تحذير
  - تظهر أسفل الحقول مباشرة
- **رسائل النجاح**:
  - لون أخضر (#4CAF50)
  - أيقونة نجاح
- **رسائل التحميل**:
  - لون أزرق
  - مؤشر تحميل دائري

### 5. منطقة المعلومات السفلية (Footer):
- **معلومات الإصدار**: "الإصدار 2.0.0"
- **حقوق الطبع**: "© 2024 Shabaka Pro - جميع الحقوق محفوظة"
- **حجم الخط**: 10px
- **اللون**: رمادي فاتح
- **المحاذاة**: وسط

## الوظائف المطلوبة

### 1. التحقق من صحة البيانات (Validation):
```csharp
- فحص أن اسم المستخدم غير فارغ وبين 3-50 حرف
- فحص أن كلمة المرور غير فارغة وأكثر من 6 أحرف
- عرض رسائل خطأ واضحة بالعربية
- منع إرسال النموذج إذا كانت البيانات غير صحيحة
```

### 2. عملية تسجيل الدخول:
```csharp
- الاتصال بخدمة AuthService
- تشفير كلمة المرور قبل الإرسال
- التحقق من صحة البيانات في قاعدة البيانات
- إنشاء جلسة آمنة للمستخدم
- حفظ بيانات المستخدم الحالي
- إغلاق نافذة تسجيل الدخول
- فتح النافذة الرئيسية
```

### 3. حفظ بيانات الدخول:
```csharp
- حفظ اسم المستخدم في الإعدادات المحلية (إذا تم تفعيل الخيار)
- تشفير البيانات المحفوظة
- استرداد البيانات عند فتح التطبيق مرة أخرى
```

### 4. معالجة الأخطاء:
```csharp
- خطأ في الاتصال بقاعدة البيانات: "تعذر الاتصال بقاعدة البيانات"
- بيانات دخول خاطئة: "اسم المستخدم أو كلمة المرور غير صحيحة"
- حساب غير نشط: "هذا الحساب غير نشط، يرجى التواصل مع المدير"
- خطأ في الشبكة: "تعذر الاتصال بالخادم"
```

## التفاعلات والأحداث

### 1. أحداث لوحة المفاتيح:
- **Enter**: تسجيل الدخول (إذا كانت البيانات صحيحة)
- **Tab**: التنقل بين الحقول
- **Escape**: إغلاق التطبيق

### 2. أحداث الماوس:
- **النقر على زر تسجيل الدخول**: بدء عملية التسجيل
- **النقر على زر إعدادات قاعدة البيانات**: فتح نافذة الإعدادات
- **النقر على أيقونة إظهار كلمة المرور**: تبديل عرض كلمة المرور

### 3. التحقق الفوري:
- التحقق من صحة البيانات أثناء الكتابة
- تفعيل/إلغاء تفعيل زر تسجيل الدخول حسب صحة البيانات
- عرض رسائل الخطأ فوراً

## الأمان المطلوب

### 1. تشفير كلمات المرور:
- استخدام BCrypt أو مشابه لتشفير كلمات المرور
- عدم حفظ كلمات المرور في النص الواضح
- تنظيف الذاكرة بعد الاستخدام

### 2. حماية من الهجمات:
- منع SQL Injection بتنظيف المدخلات
- تحديد عدد محاولات تسجيل الدخول الفاشلة
- تسجيل محاولات الدخول في سجل الأمان

### 3. إدارة الجلسات:
- إنشاء رمز جلسة فريد لكل مستخدم
- تحديد مدة انتهاء الجلسة
- تنظيف بيانات الجلسة عند الخروج

## الاستجابة والتوافق

### 1. دعم الشاشات المختلفة:
- تصميم متجاوب للشاشات عالية الدقة
- دعم DPI مختلفة
- حجم ثابت مناسب لجميع الشاشات

### 2. إمكانية الوصول:
- دعم قارئات الشاشة
- ترتيب Tab منطقي
- ألوان متباينة للوضوح
- اختصارات لوحة المفاتيح

## كود XAML المطلوب

### هيكل النافذة:
```xml
<Window x:Class="NetworkManagement.Views.LoginWindow"
        Title="Shabaka Pro - تسجيل الدخول"
        Width="450" Height="600"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft">
    
    <!-- المحتوى الرئيسي -->
    <Grid Background="{DynamicResource MaterialDesignPaper}">
        <!-- منطقة الشعار -->
        <!-- نموذج تسجيل الدخول -->
        <!-- أزرار العمل -->
        <!-- منطقة المعلومات السفلية -->
    </Grid>
</Window>
```

## ViewModel المطلوب

### خصائص LoginViewModel:
```csharp
- Username (string): اسم المستخدم
- Password (string): كلمة المرور
- RememberMe (bool): تذكر بيانات الدخول
- IsLoading (bool): حالة التحميل
- ErrorMessage (string): رسالة الخطأ
- IsLoginEnabled (bool): تفعيل زر تسجيل الدخول
```

### أوامر LoginViewModel:
```csharp
- LoginCommand: أمر تسجيل الدخول
- OpenDatabaseSettingsCommand: أمر فتح إعدادات قاعدة البيانات
- ExitCommand: أمر الخروج من التطبيق
```

هذا البرومت المفصل يغطي جميع جوانب صفحة تسجيل الدخول بأدق التفاصيل لضمان إنشاء صفحة احترافية وآمنة.
