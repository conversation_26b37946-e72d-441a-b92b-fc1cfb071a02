using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using NetworkManagement.Data;
using NetworkManagement.Models;

namespace NetworkManagement.Services
{
    public class DeviceInventoryService : IDeviceInventoryService
    {
        private readonly IServiceProvider _serviceProvider;

        public DeviceInventoryService(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        public async Task<InventoryCheckResult> CheckInventoryAvailabilityAsync(Device device, string? networkId = null)
        {
            var result = new InventoryCheckResult();
            var scope = _serviceProvider.CreateScope();

            try
            {
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                // التحقق من نوع الجهاز - دعم القيم السالبة
                if (!string.IsNullOrEmpty(device.Type))
                {
                    var deviceItem = await FindInventoryItemByNameAsync(device.Type, networkId);
                    result.RequiredQuantities[device.Type] = 1;
                    result.AvailableQuantities[device.Type] = deviceItem?.Quantity ?? 0;

                    // إضافة معلومات للعرض فقط (لا يمنع الحفظ)
                    if (deviceItem == null)
                    {
                        result.InsufficientItems.Add($"نوع الجهاز: {device.Type} - غير موجود في المخزون");
                    }
                    else if (deviceItem.Quantity < 1)
                    {
                        result.InsufficientItems.Add($"نوع الجهاز: {device.Type} - الكمية المتاحة: {deviceItem.Quantity}");
                    }
                }

                // التحقق من نوع المحول المركب - دعم القيم السالبة
                if (!string.IsNullOrEmpty(device.InstalledAdapterType))
                {
                    var adapterItem = await FindInventoryItemByNameAsync(device.InstalledAdapterType, networkId);
                    result.RequiredQuantities[device.InstalledAdapterType] = 1;
                    result.AvailableQuantities[device.InstalledAdapterType] = adapterItem?.Quantity ?? 0;

                    if (adapterItem == null)
                    {
                        result.InsufficientItems.Add($"نوع المحول المركب: {device.InstalledAdapterType} - غير موجود في المخزون");
                    }
                    else if (adapterItem.Quantity < 1)
                    {
                        result.InsufficientItems.Add($"نوع المحول المركب: {device.InstalledAdapterType} - الكمية المتاحة: {adapterItem.Quantity}");
                    }
                }

                // التحقق من سلك الشبكة - دعم القيم السالبة
                if (!string.IsNullOrEmpty(device.NetworkCableType) && device.NetworkCableLength.HasValue && device.NetworkCableLength > 0)
                {
                    var cableItem = await FindInventoryItemByNameAsync(device.NetworkCableType, networkId);
                    result.RequiredQuantities[device.NetworkCableType] = device.NetworkCableLength.Value;
                    result.AvailableQuantities[device.NetworkCableType] = cableItem?.Quantity ?? 0;

                    if (cableItem == null)
                    {
                        result.InsufficientItems.Add($"سلك الشبكة: {device.NetworkCableType} - غير موجود في المخزون");
                    }
                    else if (cableItem.Quantity < device.NetworkCableLength.Value)
                    {
                        result.InsufficientItems.Add($"سلك الشبكة: {device.NetworkCableType} - المطلوب: {device.NetworkCableLength} متر، المتاح: {cableItem.Quantity} متر");
                    }
                }

                // التحقق من سلك الكهرباء - دعم القيم السالبة
                if (!string.IsNullOrEmpty(device.PowerCableType) && device.PowerCableLength.HasValue && device.PowerCableLength > 0)
                {
                    var powerCableItem = await FindInventoryItemByNameAsync(device.PowerCableType, networkId);
                    result.RequiredQuantities[device.PowerCableType] = device.PowerCableLength.Value;
                    result.AvailableQuantities[device.PowerCableType] = powerCableItem?.Quantity ?? 0;

                    if (powerCableItem == null)
                    {
                        result.InsufficientItems.Add($"سلك الكهرباء: {device.PowerCableType} - غير موجود في المخزون");
                    }
                    else if (powerCableItem.Quantity < device.PowerCableLength.Value)
                    {
                        result.InsufficientItems.Add($"سلك الكهرباء: {device.PowerCableType} - المطلوب: {device.PowerCableLength} متر، المتاح: {powerCableItem.Quantity} متر");
                    }
                }

                // السماح بالحفظ دائماً (دعم القيم السالبة)
                result.HasSufficientStock = true;
                return result;
            }
            finally
            {
                scope.Dispose();
            }
        }

        public async Task<InventoryDeductionResult> DeductInventoryForNewDeviceAsync(Device device, string? networkId = null)
        {
            var result = new InventoryDeductionResult();
            var scope = _serviceProvider.CreateScope();

            try
            {
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                // خصم نوع الجهاز
                if (!string.IsNullOrEmpty(device.Type))
                {
                    var success = await DeductInventoryItemAsync(context, device.Type, 1, networkId);
                    if (success)
                        result.DeductedItems.Add($"نوع الجهاز: {device.Type} - 1 قطعة");
                }

                // خصم نوع المحول المركب
                if (!string.IsNullOrEmpty(device.InstalledAdapterType))
                {
                    var success = await DeductInventoryItemAsync(context, device.InstalledAdapterType, 1, networkId);
                    if (success)
                        result.DeductedItems.Add($"نوع المحول المركب: {device.InstalledAdapterType} - 1 قطعة");
                }

                // خصم سلك الشبكة
                if (!string.IsNullOrEmpty(device.NetworkCableType) && device.NetworkCableLength.HasValue && device.NetworkCableLength > 0)
                {
                    var success = await DeductInventoryItemAsync(context, device.NetworkCableType, device.NetworkCableLength.Value, networkId);
                    if (success)
                        result.DeductedItems.Add($"سلك الشبكة: {device.NetworkCableType} - {device.NetworkCableLength} متر");
                }

                // خصم سلك الكهرباء
                if (!string.IsNullOrEmpty(device.PowerCableType) && device.PowerCableLength.HasValue && device.PowerCableLength > 0)
                {
                    var success = await DeductInventoryItemAsync(context, device.PowerCableType, device.PowerCableLength.Value, networkId);
                    if (success)
                        result.DeductedItems.Add($"سلك الكهرباء: {device.PowerCableType} - {device.PowerCableLength} متر");
                }

                await context.SaveChangesAsync();
                result.Success = true;
                return result;
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = $"خطأ في خصم المخزون: {ex.Message}";
                return result;
            }
            finally
            {
                scope.Dispose();
            }
        }

        public async Task<InventoryDeductionResult> UpdateInventoryForDeviceModificationAsync(Device oldDevice, Device newDevice, string? networkId = null)
        {
            var result = new InventoryDeductionResult();
            var scope = _serviceProvider.CreateScope();
            
            try
            {
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();
                
                // إرجاع الكميات القديمة
                await RestoreInventoryForDeviceAsync(context, oldDevice, networkId);
                
                // خصم الكميات الجديدة
                var deductResult = await DeductInventoryForNewDeviceAsync(newDevice, networkId);
                
                if (!deductResult.Success)
                {
                    // إذا فشل الخصم، نعيد الكميات القديمة
                    await RestoreInventoryForDeviceAsync(context, oldDevice, networkId);
                    return deductResult;
                }

                result.Success = true;
                result.DeductedItems = deductResult.DeductedItems;
                return result;
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = $"خطأ في تحديث المخزون: {ex.Message}";
                return result;
            }
            finally
            {
                scope.Dispose();
            }
        }

        public async Task<InventoryDeductionResult> RestoreInventoryForDeletedDeviceAsync(Device device, string? networkId = null)
        {
            var result = new InventoryDeductionResult();
            var scope = _serviceProvider.CreateScope();
            
            try
            {
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();
                await RestoreInventoryForDeviceAsync(context, device, networkId);
                await context.SaveChangesAsync();
                
                result.Success = true;
                return result;
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = $"خطأ في إرجاع المخزون: {ex.Message}";
                return result;
            }
            finally
            {
                scope.Dispose();
            }
        }

        public async Task<Inventory?> FindInventoryItemByNameAsync(string itemName, string? networkId = null)
        {
            var scope = _serviceProvider.CreateScope();

            try
            {
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                var query = context.Inventory?.AsQueryable();
                if (query == null) return null;

                if (!string.IsNullOrEmpty(networkId))
                {
                    query = query.Where(i => i.NetworkId == networkId);
                }

                return await query
                    .Where(i => i.Name == itemName)
                    .FirstOrDefaultAsync();
            }
            finally
            {
                scope.Dispose();
            }
        }

        public async Task<List<string>> GetAvailableAdapterTypesAsync(string? networkId = null)
        {
            return await GetAvailableItemsByCategory("أدوات", networkId);
        }

        public async Task<List<string>> GetAvailableNetworkCableTypesAsync(string? networkId = null)
        {
            return await GetAvailableItemsByCategory("كابلات", networkId);
        }

        public async Task<List<string>> GetAvailablePowerCableTypesAsync(string? networkId = null)
        {
            return await GetAvailableItemsByCategory("كابلات", networkId);
        }

        public async Task<List<string>> GetAvailableDeviceTypesAsync(string? networkId = null)
        {
            return await GetAvailableItemsByCategory("أجهزة", networkId);
        }

        private async Task<List<string>> GetAvailableItemsByCategory(string category, string? networkId = null)
        {
            var scope = _serviceProvider.CreateScope();

            try
            {
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                var query = context.Inventory?.AsQueryable();
                if (query == null) return new List<string>();

                if (!string.IsNullOrEmpty(networkId))
                {
                    query = query.Where(i => i.NetworkId == networkId);
                }

                // إزالة شرط الكمية لدعم القيم السالبة - عرض جميع العناصر
                return await query
                    .Where(i => i.Category == category)
                    .Select(i => i.Name)
                    .Distinct()
                    .OrderBy(name => name)
                    .ToListAsync();
            }
            finally
            {
                scope.Dispose();
            }
        }

        private async Task<bool> DeductInventoryItemAsync(NetworkDbContext context, string itemName, int quantity, string? networkId = null)
        {
            if (context.Inventory == null) return false;

            var item = await context.Inventory
                .Where(i => i.Name == itemName)
                .Where(i => networkId == null || i.NetworkId == networkId)
                .FirstOrDefaultAsync();

            if (item != null)
            {
                item.Quantity -= quantity;
                item.LastUpdated = DateTime.Now;
                item.UpdatedAt = DateTime.Now;
                return true;
            }

            return false;
        }

        private async System.Threading.Tasks.Task RestoreInventoryForDeviceAsync(NetworkDbContext context, Device device, string? networkId = null)
        {
            // إرجاع نوع الجهاز
            if (!string.IsNullOrEmpty(device.Type))
            {
                await RestoreInventoryItemAsync(context, device.Type, 1, networkId);
            }

            // إرجاع نوع المحول المركب
            if (!string.IsNullOrEmpty(device.InstalledAdapterType))
            {
                await RestoreInventoryItemAsync(context, device.InstalledAdapterType, 1, networkId);
            }

            // إرجاع سلك الشبكة
            if (!string.IsNullOrEmpty(device.NetworkCableType) && device.NetworkCableLength.HasValue && device.NetworkCableLength > 0)
            {
                await RestoreInventoryItemAsync(context, device.NetworkCableType, device.NetworkCableLength.Value, networkId);
            }

            // إرجاع سلك الكهرباء
            if (!string.IsNullOrEmpty(device.PowerCableType) && device.PowerCableLength.HasValue && device.PowerCableLength > 0)
            {
                await RestoreInventoryItemAsync(context, device.PowerCableType, device.PowerCableLength.Value, networkId);
            }
        }



        private async System.Threading.Tasks.Task RestoreInventoryItemAsync(NetworkDbContext context, string itemName, int quantity, string? networkId = null)
        {
            if (context.Inventory == null) return;

            var item = await context.Inventory
                .Where(i => i.Name == itemName)
                .Where(i => networkId == null || i.NetworkId == networkId)
                .FirstOrDefaultAsync();

            if (item != null)
            {
                item.Quantity += quantity;
                item.LastUpdated = DateTime.Now;
                item.UpdatedAt = DateTime.Now;
            }
        }
    }
}
