using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NetworkManagement.Models
{
    /// <summary>
    /// نموذج تقييم المستخدمين اليومي
    /// </summary>
    public class UserEvaluation
    {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        [Required]
        [MaxLength(50)]
        public string UserId { get; set; } = string.Empty;

        [Required]
        [MaxLength(50)]
        public string EvaluatedBy { get; set; } = string.Empty; // Admin who gave the evaluation

        [Required]
        public DateTime EvaluationDate { get; set; } = DateTime.Today;

        [Required]
        [Range(1, 10)]
        public int Score { get; set; } = 5;

        [MaxLength(500)]
        public string? Notes { get; set; }

        // Network relationship
        public string? NetworkId { get; set; }
        public virtual Network? Network { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual User? User { get; set; }
        public virtual User? EvaluatedByUser { get; set; }

        // Display properties
        [NotMapped]
        public string ScoreDisplay => $"{Score}/10";

        [NotMapped]
        public decimal ScorePercentage => (decimal)Score / 10 * 100;

        [NotMapped]
        public string ScorePercentageDisplay => $"{ScorePercentage:F1}%";

        [NotMapped]
        public string EvaluationDateDisplay => EvaluationDate.ToString("dd/MM/yyyy");

        [NotMapped]
        public string UserName => User?.Name ?? "غير محدد";

        [NotMapped]
        public string EvaluatedByName => EvaluatedByUser?.Name ?? "غير محدد";

        [NotMapped]
        public string NetworkName => Network?.Name ?? "غير محدد";

        [NotMapped]
        public string NotesDisplay => Notes ?? "لا توجد ملاحظات";

        [NotMapped]
        public string ScoreColorClass => Score switch
        {
            >= 9 => "HighScore",
            >= 7 => "MediumScore",
            >= 5 => "LowScore",
            _ => "VeryLowScore"
        };

        [NotMapped]
        public string ScoreDescription => Score switch
        {
            10 => "ممتاز",
            9 => "جيد جداً",
            8 => "جيد",
            7 => "مقبول",
            6 => "ضعيف",
            5 => "ضعيف جداً",
            4 => "سيء",
            3 => "سيء جداً",
            2 => "ضعيف للغاية",
            1 => "غير مقبول",
            _ => "غير محدد"
        };
    }

    /// <summary>
    /// إحصائيات تقييم المستخدم
    /// </summary>
    public class UserEvaluationStatistics
    {
        public string UserId { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
        public int TotalEvaluations { get; set; }
        public decimal AverageScore { get; set; }
        public decimal AveragePercentage => AverageScore / 10 * 100;
        public int HighestScore { get; set; }
        public int LowestScore { get; set; }
        public DateTime? LastEvaluationDate { get; set; }
        public DateTime? FirstEvaluationDate { get; set; }
        public string NetworkId { get; set; } = string.Empty;
        public string NetworkName { get; set; } = string.Empty;

        // Display properties
        public string AverageScoreDisplay => $"{AverageScore:F1}/10";
        public string AveragePercentageDisplay => $"{AveragePercentage:F1}%";
        public string ScoreRangeDisplay => $"{LowestScore} - {HighestScore}";
        public string LastEvaluationDisplay => LastEvaluationDate?.ToString("dd/MM/yyyy") ?? "لا يوجد";
        public string PeriodDisplay => FirstEvaluationDate.HasValue && LastEvaluationDate.HasValue
            ? $"{FirstEvaluationDate:dd/MM/yyyy} - {LastEvaluationDate:dd/MM/yyyy}"
            : "غير محدد";
    }
}
