using System;
using System.Windows.Controls;
using NetworkManagement.ViewModels;
using Microsoft.Extensions.DependencyInjection;

namespace NetworkManagement.Views
{
    public partial class DevicesView : UserControl
    {
        private DevicesViewModel? _viewModel;

        public DevicesView()
        {
            InitializeComponent();
            Loaded += DevicesView_Loaded;
        }

        private void DevicesDataGrid_LoadingRow(object sender, DataGridRowEventArgs e)
        {
            e.Row.Header = (e.Row.GetIndex() + 1).ToString();
        }

        private async void DevicesView_Loaded(object sender, System.Windows.RoutedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("DevicesView_Loaded: بدء تحميل صفحة الأجهزة");

                // التأكد من أن العملية تتم على UI Thread
                if (!Dispatcher.CheckAccess())
                {
                    Dispatcher.Invoke(() => DevicesView_Loaded(sender, e));
                    return;
                }

                // الحصول على ViewModel مباشرة من مزود الخدمة الرئيسي
                _viewModel = App.GetService<DevicesViewModel>();

                // التأكد من أن ViewModel ليس null
                if (_viewModel == null)
                {
                    System.Diagnostics.Debug.WriteLine("DevicesView_Loaded: خطأ - ViewModel is null");
                    return;
                }

                DataContext = _viewModel;

                System.Diagnostics.Debug.WriteLine("DevicesView_Loaded: تم الحصول على DevicesViewModel");
                System.Diagnostics.Debug.WriteLine($"DevicesView_Loaded: DataContext = {DataContext}");
                System.Diagnostics.Debug.WriteLine($"DevicesView_Loaded: ViewModel = {_viewModel}");

                // تحقق من وجود فلتر شبكة في Tag
                if (Tag is string networkId && !string.IsNullOrEmpty(networkId))
                {
                    System.Diagnostics.Debug.WriteLine($"DevicesView_Loaded: تطبيق فلتر الشبكة - {networkId}");

                    // تطبيق فلتر الشبكة قبل التهيئة
                    _viewModel.SetNetworkFilter(networkId);

                    // انتظار قصير للتأكد من تطبيق الفلتر
                    await System.Threading.Tasks.Task.Delay(100);
                }

                // تهيئة ViewModel
                await _viewModel.InitializeAsync();

                System.Diagnostics.Debug.WriteLine($"DevicesView_Loaded: تم تحميل الصفحة بنجاح - عدد الأجهزة: {_viewModel.Devices.Count}");

                // فرض تحديث DataGrid
                DevicesDataGrid.Items.Refresh();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"DevicesView_Loaded: خطأ - {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"DevicesView_Loaded: تفاصيل الخطأ - {ex}");

                // عدم إظهار رسالة خطأ إذا كانت مشكلة Threading عادية
                if (!ex.Message.Contains("different thread owns it"))
                {
                    System.Windows.MessageBox.Show(
                        $"حدث خطأ أثناء تحميل الأجهزة:\n{ex.Message}",
                        "خطأ في تحميل الأجهزة",
                        System.Windows.MessageBoxButton.OK,
                        System.Windows.MessageBoxImage.Error);
                }
            }
        }
    }
}
