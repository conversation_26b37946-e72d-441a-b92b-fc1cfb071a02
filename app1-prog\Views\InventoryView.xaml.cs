using System.Windows.Controls;
using NetworkManagement.ViewModels;

namespace NetworkManagement.Views
{
    public partial class InventoryView : UserControl
    {
        public InventoryView()
        {
            InitializeComponent();
            DataContext = App.GetService<InventoryViewModel>();
        }

        private void ExportActionsButton_Click(object sender, System.Windows.RoutedEventArgs e)
        {
            if (sender is Button button && button.ContextMenu != null)
            {
                button.ContextMenu.PlacementTarget = button;
                button.ContextMenu.IsOpen = true;
            }
        }
    }
}
