-- =============================================================================
-- سكريبت إنشاء قاعدة بيانات نظام إدارة الشبكات المتقدم
-- مطابق تماماً لجميع النماذج (Models) في التطبيق
-- تم التحديث والتحسين لضمان الأداء الأمثل
-- =============================================================================

-- إنشاء قاعدة البيانات مع الترميز الصحيح
CREATE DATABASE IF NOT EXISTS NetworkManagementDB
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

USE NetworkManagementDB;

-- =============================================================================
-- جدول الشبكات (Networks) - مطابق تماماً لـ Network.cs
-- =============================================================================
CREATE TABLE IF NOT EXISTS Networks (
    Id VARCHAR(50) PRIMARY KEY,
    Name VARCHAR(100) NOT NULL,
    Description VARCHAR(500),
    Color VARCHAR(20) DEFAULT '#2196F3',
    IsActive BOOLEAN DEFAULT 1,
    CreatedAt DATETIME DEFAULT NOW(),
    UpdatedAt DATETIME DEFAULT NOW(),

    -- فهارس للأداء
    UNIQUE KEY unique_name (Name),
    INDEX idx_active (IsActive),
    INDEX idx_created (CreatedAt),
    INDEX idx_updated (UpdatedAt)
);

-- =============================================================================
-- جدول المستخدمين (Users) - مطابق تماماً لـ User.cs
-- =============================================================================
CREATE TABLE IF NOT EXISTS Users (
    Id VARCHAR(50) PRIMARY KEY,
    Username VARCHAR(50) UNIQUE NOT NULL,
    Password VARCHAR(255) NOT NULL,
    Name VARCHAR(100) NOT NULL,
    Email VARCHAR(100),
    Role VARCHAR(20) NOT NULL,  -- تصحيح: VARCHAR(20) بدلاً من VARCHAR(50)
    NetworkId VARCHAR(50),
    IsActive BOOLEAN DEFAULT 1,
    CreatedAt DATETIME DEFAULT NOW(),
    UpdatedAt DATETIME DEFAULT NOW(),

    -- العلاقات والفهارس
    FOREIGN KEY (NetworkId) REFERENCES Networks(Id) ON DELETE SET NULL,
    INDEX idx_username (Username),
    INDEX idx_role (Role),
    INDEX idx_network (NetworkId),
    INDEX idx_active (IsActive),
    INDEX idx_created (CreatedAt)
);

-- =============================================================================
-- جدول المواقع (Sites) - مطابق تماماً لـ Site.cs
-- =============================================================================
CREATE TABLE IF NOT EXISTS Sites (
    Id VARCHAR(50) PRIMARY KEY,
    Name VARCHAR(100) NOT NULL,
    Address VARCHAR(200),
    Phone VARCHAR(20),
    GpsLat DOUBLE,
    GpsLng DOUBLE,
    PowerSource VARCHAR(100),

    -- نظام البطاريات
    BatteryType VARCHAR(50),
    BatterySize VARCHAR(50),

    -- نظام القواعد
    BaseType VARCHAR(50),
    BaseCount INT,

    -- نظام الصناديق
    BoxType VARCHAR(50),
    BoxCount INT,

    -- نظام أسلاك الشبكة
    NetworkCableType VARCHAR(50),
    NetworkCableLength INT,
    PowerCableType VARCHAR(50),
    PowerCableLength INT,

    -- أنواع الأجهزة الموجودة في الموقع
    DeviceTypes TEXT,  -- JSON string

    -- عناصر إضافية من المخزون
    AdditionalInventoryItems TEXT,  -- JSON string

    NetworkId VARCHAR(50),
    CreatedAt DATETIME DEFAULT NOW(),
    UpdatedAt DATETIME DEFAULT NOW(),

    -- العلاقات والفهارس
    FOREIGN KEY (NetworkId) REFERENCES Networks(Id) ON DELETE SET NULL,
    INDEX idx_name (Name),
    INDEX idx_network (NetworkId),
    INDEX idx_gps (GpsLat, GpsLng),
    INDEX idx_created (CreatedAt),
    INDEX idx_updated (UpdatedAt)
);

-- =============================================================================
-- جدول الأجهزة (Devices) - مطابق تماماً لـ Device.cs
-- =============================================================================
CREATE TABLE IF NOT EXISTS Devices (
    Id VARCHAR(50) PRIMARY KEY,
    Responsible VARCHAR(100),
    Type VARCHAR(50),
    Location VARCHAR(200),
    Phone VARCHAR(20),
    Ip VARCHAR(15),
    InstallDate DATETIME,
    PowerConnection VARCHAR(100),
    AdapterType VARCHAR(50),
    InstalledAdapterType VARCHAR(50),
    NetworkCableType VARCHAR(50),
    PowerCableType VARCHAR(50),
    NetworkCableLength INT,
    PowerCableLength INT,
    ConnectionMethod VARCHAR(50),
    LinkedNetwork VARCHAR(50),
    BroadcastNetworkName VARCHAR(100),
    Channel INT,
    ConnectedDevices INT,
    Status VARCHAR(20) DEFAULT 'active',
    LastCheck DATETIME,
    SiteId VARCHAR(50),
    NetworkId VARCHAR(50),
    CreatedAt DATETIME DEFAULT NOW(),
    UpdatedAt DATETIME DEFAULT NOW(),

    -- العلاقات والفهارس
    FOREIGN KEY (SiteId) REFERENCES Sites(Id) ON DELETE SET NULL,
    FOREIGN KEY (NetworkId) REFERENCES Networks(Id) ON DELETE SET NULL,
    INDEX idx_ip (Ip),
    INDEX idx_status (Status),
    INDEX idx_type (Type),
    INDEX idx_location (Location),
    INDEX idx_site (SiteId),
    INDEX idx_network (NetworkId),
    INDEX idx_install_date (InstallDate),
    INDEX idx_last_check (LastCheck),
    INDEX idx_created (CreatedAt),
    INDEX idx_updated (UpdatedAt)
);

-- =============================================================================
-- جدول المهام (Tasks) - مطابق تماماً لـ Task.cs
-- =============================================================================
CREATE TABLE IF NOT EXISTS Tasks (
    Id VARCHAR(50) PRIMARY KEY,
    UserId VARCHAR(50) NOT NULL,
    Description VARCHAR(500) NOT NULL,  -- تصحيح: VARCHAR(500) بدلاً من VARCHAR(1000)
    RequestDate DATETIME NOT NULL DEFAULT NOW(),
    Status VARCHAR(20) DEFAULT 'pending',  -- تصحيح: VARCHAR(20) بدلاً من VARCHAR(50)
    Notes VARCHAR(1000),  -- تصحيح: VARCHAR(1000) بدلاً من TEXT
    Priority VARCHAR(20) DEFAULT 'medium',  -- تصحيح: VARCHAR(20) بدلاً من VARCHAR(50)
    CompletedAt DATETIME,
    NetworkId VARCHAR(50),

    -- العلاقات والفهارس
    FOREIGN KEY (UserId) REFERENCES Users(Id) ON DELETE CASCADE,
    FOREIGN KEY (NetworkId) REFERENCES Networks(Id) ON DELETE SET NULL,
    INDEX idx_user (UserId),
    INDEX idx_status (Status),
    INDEX idx_priority (Priority),
    INDEX idx_network (NetworkId),
    INDEX idx_request_date (RequestDate),
    INDEX idx_completed_at (CompletedAt),
    INDEX idx_status_priority (Status, Priority),
    INDEX idx_user_status (UserId, Status)
);

-- =============================================================================
-- جدول المشتريات (Purchases) - مطابق تماماً لـ Purchase.cs
-- =============================================================================
CREATE TABLE IF NOT EXISTS Purchases (
    Id VARCHAR(50) PRIMARY KEY,
    ItemType VARCHAR(100) NOT NULL,
    Price DECIMAL(10,2) NOT NULL,  -- تصحيح: مطلوب حسب النموذج
    Date DATETIME NOT NULL DEFAULT NOW(),  -- تصحيح: مطلوب حسب النموذج
    Quantity INT NOT NULL DEFAULT 1,  -- تصحيح: مطلوب حسب النموذج
    Unit VARCHAR(20),  -- تصحيح: VARCHAR(20) بدلاً من VARCHAR(50)
    Supplier VARCHAR(200),
    Description VARCHAR(500),
    InvoiceNumber VARCHAR(50),  -- تصحيح: VARCHAR(50) بدلاً من VARCHAR(100)
    Category VARCHAR(20),  -- تصحيح: VARCHAR(20) بدلاً من VARCHAR(100)
    NetworkId VARCHAR(50),
    CreatedAt DATETIME DEFAULT NOW(),
    UpdatedAt DATETIME DEFAULT NOW(),

    -- العلاقات والفهارس
    FOREIGN KEY (NetworkId) REFERENCES Networks(Id) ON DELETE SET NULL,
    INDEX idx_item_type (ItemType),
    INDEX idx_category (Category),
    INDEX idx_date (Date),
    INDEX idx_network (NetworkId),
    INDEX idx_supplier (Supplier),
    INDEX idx_price (Price),
    INDEX idx_created (CreatedAt),
    INDEX idx_updated (UpdatedAt)
);

-- =============================================================================
-- جدول المخزون (Inventory) - مطابق تماماً لـ Inventory.cs
-- =============================================================================
CREATE TABLE IF NOT EXISTS Inventory (
    Id VARCHAR(50) PRIMARY KEY,
    Name VARCHAR(100) NOT NULL,
    Category VARCHAR(50) NOT NULL,  -- تصحيح: مطلوب حسب النموذج
    Quantity INT NOT NULL DEFAULT 0,  -- تصحيح: مطلوب حسب النموذج
    Unit VARCHAR(20) NOT NULL,  -- تصحيح: مطلوب وVARCHAR(20) حسب النموذج
    LastUpdated DATETIME DEFAULT NOW(),
    Description VARCHAR(500),
    UnitPrice DECIMAL(10,2),
    Location VARCHAR(100),  -- تصحيح: VARCHAR(100) بدلاً من VARCHAR(200)
    MinimumStock INT,
    MaximumStock INT,
    Supplier VARCHAR(50),  -- تصحيح: VARCHAR(50) بدلاً من VARCHAR(200)
    NetworkId VARCHAR(50),
    CreatedAt DATETIME DEFAULT NOW(),
    UpdatedAt DATETIME DEFAULT NOW(),

    -- العلاقات والفهارس
    FOREIGN KEY (NetworkId) REFERENCES Networks(Id) ON DELETE SET NULL,
    INDEX idx_name (Name),
    INDEX idx_category (Category),
    INDEX idx_network (NetworkId),
    INDEX idx_quantity (Quantity),
    INDEX idx_low_stock (MinimumStock, Quantity),
    INDEX idx_location (Location),
    INDEX idx_supplier (Supplier),
    INDEX idx_last_updated (LastUpdated),
    INDEX idx_created (CreatedAt),
    INDEX idx_updated (UpdatedAt)
);

-- =============================================================================
-- جدول تقييم المستخدمين (UserEvaluations) - مطابق تماماً لـ UserEvaluation.cs
-- =============================================================================
CREATE TABLE IF NOT EXISTS UserEvaluations (
    Id VARCHAR(50) PRIMARY KEY,
    UserId VARCHAR(50) NOT NULL,
    EvaluatedBy VARCHAR(50) NOT NULL,
    EvaluationDate DATE NOT NULL,
    Score INT NOT NULL CHECK (Score >= 1 AND Score <= 10),
    Notes VARCHAR(500),
    NetworkId VARCHAR(50),
    CreatedAt DATETIME DEFAULT NOW(),
    UpdatedAt DATETIME DEFAULT NOW(),

    -- العلاقات والفهارس
    FOREIGN KEY (UserId) REFERENCES Users(Id) ON DELETE CASCADE,
    FOREIGN KEY (EvaluatedBy) REFERENCES Users(Id) ON DELETE RESTRICT,
    FOREIGN KEY (NetworkId) REFERENCES Networks(Id) ON DELETE SET NULL,
    UNIQUE KEY unique_user_date (UserId, EvaluationDate),
    INDEX idx_user (UserId),
    INDEX idx_evaluated_by (EvaluatedBy),
    INDEX idx_evaluation_date (EvaluationDate),
    INDEX idx_network (NetworkId),
    INDEX idx_score (Score),
    INDEX idx_created (CreatedAt),
    INDEX idx_updated (UpdatedAt)
);

-- =============================================================================
-- البيانات الأولية والتحسينات
-- =============================================================================

-- إدراج المستخدم المدير العام الافتراضي
INSERT IGNORE INTO Users (Id, Username, Password, Name, Role, NetworkId, IsActive, CreatedAt, UpdatedAt)
VALUES ('admin', 'admin', '$2a$11$N.dlzWpKjJnKrGGqVlq3L.JQJKqKqKqKqKqKqKqKqKqKqKqKqKqKq', 'المدير العام', 'Admin', NULL, 1, NOW(), NOW());

-- تحديث الأدوار الموجودة لتوحيد التسمية
UPDATE Users SET Role = 'Admin' WHERE Role IN ('admin', 'ADMIN');
UPDATE Users SET Role = 'Manager' WHERE Role IN ('manager', 'MANAGER');
UPDATE Users SET Role = 'Technician' WHERE Role IN ('technician', 'TECHNICIAN');
UPDATE Users SET Role = 'User' WHERE Role IN ('user', 'USER');

-- =============================================================================
-- تحسينات الأداء وإعدادات قاعدة البيانات
-- =============================================================================

-- تحسين إعدادات الجداول
ALTER TABLE Networks ENGINE=InnoDB;
ALTER TABLE Users ENGINE=InnoDB;
ALTER TABLE Sites ENGINE=InnoDB;
ALTER TABLE Devices ENGINE=InnoDB;
ALTER TABLE Tasks ENGINE=InnoDB;
ALTER TABLE Purchases ENGINE=InnoDB;
ALTER TABLE Inventory ENGINE=InnoDB;
ALTER TABLE UserEvaluations ENGINE=InnoDB;

-- تحديث إحصائيات الجداول لتحسين الأداء
ANALYZE TABLE Networks;
ANALYZE TABLE Users;
ANALYZE TABLE Sites;
ANALYZE TABLE Devices;
ANALYZE TABLE Tasks;
ANALYZE TABLE Purchases;
ANALYZE TABLE Inventory;
ANALYZE TABLE UserEvaluations;

-- =============================================================================
-- تقرير إنشاء قاعدة البيانات
-- =============================================================================

SELECT 'تم إنشاء قاعدة البيانات بنجاح!' as Status;
SELECT 'جميع الجداول والفهارس تم إنشاؤها بنجاح' as Message;
SELECT COUNT(*) as 'عدد الجداول المنشأة' FROM information_schema.tables WHERE table_schema = 'NetworkManagementDB';
SELECT COUNT(*) as 'عدد الفهارس المنشأة' FROM information_schema.statistics WHERE table_schema = 'NetworkManagementDB';
