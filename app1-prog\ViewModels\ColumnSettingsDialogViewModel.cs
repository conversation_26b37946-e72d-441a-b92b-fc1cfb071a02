using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;

namespace NetworkManagement.ViewModels
{
    public partial class ColumnSettingsDialogViewModel : ObservableObject
    {
        [ObservableProperty]
        private string title = "إعدادات الأعمدة";

        [ObservableProperty]
        private ObservableCollection<ColumnInfo> columns = new();

        [ObservableProperty]
        private ColumnInfo? selectedColumn;

        [ObservableProperty]
        private bool isLoading = false;

        public event EventHandler<ObservableCollection<ColumnInfo>>? SettingsSaved;
        public event EventHandler? DialogClosed;

        public ColumnSettingsDialogViewModel()
        {
        }

        /// <summary>
        /// تهيئة الأعمدة للجدول
        /// </summary>
        public void InitializeColumns(ObservableCollection<ColumnInfo> currentColumns)
        {
            Columns.Clear();
            foreach (var column in currentColumns)
            {
                Columns.Add(new ColumnInfo
                {
                    Name = column.Name,
                    Header = column.Header,
                    IsVisible = column.IsVisible,
                    Width = column.Width,
                    Order = column.Order,
                    CanHide = column.CanHide,
                    CanReorder = column.CanReorder
                });
            }
        }

        [RelayCommand]
        private void MoveUp()
        {
            if (SelectedColumn == null) return;

            var currentIndex = Columns.IndexOf(SelectedColumn);
            if (currentIndex > 0)
            {
                var item = SelectedColumn;
                Columns.RemoveAt(currentIndex);
                Columns.Insert(currentIndex - 1, item);
                
                // تحديث ترتيب الأعمدة
                UpdateColumnOrders();
                SelectedColumn = item;
            }
        }

        [RelayCommand]
        private void MoveDown()
        {
            if (SelectedColumn == null) return;

            var currentIndex = Columns.IndexOf(SelectedColumn);
            if (currentIndex < Columns.Count - 1)
            {
                var item = SelectedColumn;
                Columns.RemoveAt(currentIndex);
                Columns.Insert(currentIndex + 1, item);
                
                // تحديث ترتيب الأعمدة
                UpdateColumnOrders();
                SelectedColumn = item;
            }
        }

        [RelayCommand]
        private void ToggleVisibility()
        {
            if (SelectedColumn == null || !SelectedColumn.CanHide) return;

            SelectedColumn.IsVisible = !SelectedColumn.IsVisible;
        }

        [RelayCommand]
        private void ResetToDefault()
        {
            // إعادة تعيين الأعمدة للإعدادات الافتراضية
            foreach (var column in Columns)
            {
                column.IsVisible = true;
                column.Order = GetDefaultOrder(column.Name);
            }

            // إعادة ترتيب الأعمدة حسب الترتيب الافتراضي
            var sortedColumns = Columns.OrderBy(c => c.Order).ToList();
            Columns.Clear();
            foreach (var column in sortedColumns)
            {
                Columns.Add(column);
            }

            UpdateColumnOrders();
        }

        [RelayCommand]
        private void SaveSettings()
        {
            try
            {
                IsLoading = true;

                // تحديث ترتيب الأعمدة النهائي
                UpdateColumnOrders();

                // إثارة حدث الحفظ
                SettingsSaved?.Invoke(this, Columns);

                // إغلاق النافذة
                DialogClosed?.Invoke(this, EventArgs.Empty);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving column settings: {ex.Message}");
                System.Windows.MessageBox.Show(
                    $"حدث خطأ أثناء حفظ إعدادات الأعمدة:\n{ex.Message}",
                    "خطأ",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private void Cancel()
        {
            DialogClosed?.Invoke(this, EventArgs.Empty);
        }

        private void UpdateColumnOrders()
        {
            for (int i = 0; i < Columns.Count; i++)
            {
                Columns[i].Order = i;
            }
        }

        private int GetDefaultOrder(string columnName)
        {
            // ترتيب افتراضي للأعمدة
            return columnName switch
            {
                "RowNumber" => 0,
                "Selection" => 1,
                "Ip" => 2,
                "Status" => 3,
                "Name" => 4,
                "Type" => 5,
                "Location" => 6,
                "Network" => 7,
                "LastSeen" => 8,
                "Actions" => 9,
                _ => 999
            };
        }
    }

    /// <summary>
    /// معلومات العمود
    /// </summary>
    public partial class ColumnInfo : ObservableObject
    {
        [ObservableProperty]
        private string name = string.Empty;

        [ObservableProperty]
        private string header = string.Empty;

        [ObservableProperty]
        private bool isVisible = true;

        [ObservableProperty]
        private double width = 100;

        [ObservableProperty]
        private int order = 0;

        [ObservableProperty]
        private bool canHide = true;

        [ObservableProperty]
        private bool canReorder = true;
    }
}
