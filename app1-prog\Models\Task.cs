using System;
using System.ComponentModel.DataAnnotations;
using NetworkManagement.Helpers;

namespace NetworkManagement.Models
{
    public class Task
    {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        [Required]
        [MaxLength(50)]
        public string UserId { get; set; } = string.Empty;

        [Required]
        [MaxLength(500)]
        public string Description { get; set; } = string.Empty;

        [Required]
        public DateTime RequestDate { get; set; } = DateTime.Now;

        [Required]
        [MaxLength(20)]
        public string Status { get; set; } = TaskConstants.Status.Pending;

        [MaxLength(1000)]
        public string? Notes { get; set; }

        [MaxLength(20)]
        public string? Priority { get; set; } = TaskConstants.Priority.Medium;

        public DateTime? CompletedAt { get; set; }

        // Network relationship
        public string? NetworkId { get; set; }
        public virtual Network? Network { get; set; }

        // Navigation properties
        public virtual User? User { get; set; }

        // Display properties
        public string StatusDisplay => TaskHelper.GetStatusDisplay(Status);

        public string PriorityDisplay => TaskHelper.GetPriorityDisplay(Priority);

        public string StatusColor => TaskHelper.GetStatusColor(Status);

        public string PriorityColor => TaskHelper.GetPriorityColor(Priority);

        public string RequestDateDisplay => RequestDate.ToString("dd/MM/yyyy");
        public string CompletedAtDisplay => CompletedAt?.ToString("dd/MM/yyyy") ?? "غير مكتمل";

        public bool IsOverdue => TaskHelper.IsTaskOverdue(this);
        public bool IsCompleted => TaskHelper.IsTaskCompleted(this);
        public bool CanComplete => TaskHelper.CanCompleteTask(this);
    }
}
