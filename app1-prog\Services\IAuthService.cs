using System;
using System.Threading.Tasks;
using NetworkManagement.Models;

namespace NetworkManagement.Services
{
    public interface IAuthService
    {
        Task<User?> LoginAsync(string username, string password);
        Task<bool> LogoutAsync();
        User? CurrentUser { get; }
        bool IsLoggedIn { get; }
        event EventHandler<User?>? UserChanged;

        // Permissions and Role Checks
        bool IsAdmin { get; }
        bool IsManager { get; }
        bool IsTechnician { get; }
        bool IsUser { get; }
        bool IsSuperAdmin { get; }
        bool IsNetworkManager { get; }

        bool CanManageNetworks { get; }
        bool CanManageUsers { get; }
        bool CanViewAllNetworks { get; }
        bool CanManageOwnNetwork { get; }
        bool CanViewSettings { get; }
        bool CanManagePurchases { get; }
        bool CanManageInventory { get; }
        bool CanManageTasks { get; }
        bool CanManageSites { get; }
        bool CanManageDevices { get; }
        bool CanViewReports { get; }

        string? CurrentUserNetworkId { get; }
        string? CurrentUserNetworkName { get; }

        bool CanAccessNetwork(string networkId);
        bool CanEditData(string? dataNetworkId = null);
        bool CanAddData(string? targetNetworkId = null);
        bool CanViewData(string? dataNetworkId = null);
        bool CanDeleteData(string? dataNetworkId = null);

        // User-specific permissions
        bool CanManageUsersInNetwork(string? targetNetworkId = null);
        bool CanEditUser(User targetUser);
        bool CanDeleteUser(User targetUser);
    }
}
