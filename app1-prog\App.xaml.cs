using System;
using System.Threading.Tasks;
using System.Windows;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using NetworkManagement.Data;
using NetworkManagement.Services;
using NetworkManagement.ViewModels;
using NetworkManagement.Views;
using Microsoft.EntityFrameworkCore;

namespace NetworkManagement
{
    public partial class App : Application
    {
        private IHost? _host;

        protected override async void OnStartup(StartupEventArgs e)
        {
            _host = Host.CreateDefaultBuilder()
                .ConfigureServices((context, services) =>
                {
                    // Database with connection pooling for better performance
                    services.AddDbContextPool<NetworkDbContext>(options =>
                    {
                        var settings = new SettingsService().LoadSettings();
                        var connectionString = $"Server={settings.MySQLServer};Database={settings.MySQLDatabase};User={settings.MySQLUser};Password={settings.MySQLPassword};Pooling=true;MinimumPoolSize=2;MaximumPoolSize=10;ConnectionTimeout=30;AllowUserVariables=true;UseAffectedRows=false;";
                        options.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString));

                        // تحسينات الأداء والاستقرار
                        options.EnableSensitiveDataLogging(false);
                        options.EnableServiceProviderCaching();
                        options.EnableDetailedErrors(false);
                    }, poolSize: 32);

                    // Memory Cache
                    services.AddMemoryCache();

                    // Services
                    services.AddSingleton<IPingService, PingService>();
                    services.AddSingleton<ISettingsService, SettingsService>();
                    services.AddSingleton<IDatabaseService, DatabaseService>();
                    services.AddSingleton<IThemeService, ThemeService>();
                    services.AddSingleton<ILocalizationService, LocalizationService>();
                    services.AddSingleton<INotificationService>(provider =>
                        new NotificationService(provider.GetService<IAuthService>()));
                    services.AddSingleton<IPasswordHashService, PasswordHashService>();
                    services.AddSingleton<ICacheService, CacheService>();
                    services.AddSingleton<ISyncService, SyncService>();
                    services.AddSingleton<IStartupService, StartupService>();

                    // Auth service as Singleton (يحتاج للبقاء عبر التطبيق)
                    services.AddSingleton<IAuthService, AuthService>();

                    // Data services as Scoped (لتجنب مشاكل DbContext Threading)
                    services.AddScoped<IDeviceService, DeviceService>();
                    services.AddScoped<ISiteService, SiteService>();
                    services.AddScoped<IUserService, UserService>();
                    services.AddScoped<ITaskService, TaskService>();
                    services.AddScoped<IPurchaseService, PurchaseService>();
                    services.AddScoped<IInventoryService, InventoryService>();
                    services.AddScoped<IDeviceInventoryService, DeviceInventoryService>();
                    services.AddScoped<ISiteInventoryService, SiteInventoryService>();
                    services.AddScoped<IReportExportService, ReportExportService>();
            services.AddScoped<IDatabaseRepairService, DatabaseRepairService>();
                    services.AddScoped<IProfessionalReportService, ProfessionalReportService>();
                    services.AddScoped<INetworkService, NetworkService>();
                    services.AddScoped<IUserEvaluationService, UserEvaluationService>();



                    // ViewModels
                    services.AddTransient<LoginViewModel>();
                    services.AddTransient<DatabaseSetupViewModel>();
                    services.AddTransient<MainViewModel>();
                    services.AddScoped<DashboardViewModel>();
                    services.AddTransient<DevicesViewModel>();
                    services.AddTransient<SitesViewModel>();
                    services.AddTransient<UsersViewModel>();
                    services.AddTransient<TasksViewModel>();
                    services.AddTransient<PurchasesViewModel>();
                    services.AddTransient<InventoryViewModel>();
                    services.AddTransient<ReportsViewModel>();
                    services.AddTransient<SettingsViewModel>();
                    services.AddTransient<NetworkManagementViewModel>();
                    services.AddTransient<UserEvaluationViewModel>();
                    services.AddTransient<MyEvaluationViewModel>();

                    // Dialog ViewModels
                    services.AddTransient<DeviceDialogViewModel>();
                    services.AddTransient<SiteDialogViewModel>();
                    services.AddTransient<UserDialogViewModel>();
                    services.AddTransient<PurchaseDialogViewModel>();
                    services.AddTransient<InventoryDialogViewModel>();
                    services.AddTransient<TaskDialogViewModel>();
                    services.AddTransient<ColumnSettingsDialogViewModel>();
                })
                .Build();

            // Fast startup initialization
            try
            {
                var startupService = _host.Services.GetRequiredService<IStartupService>();

                // تهيئة سريعة في الخلفية
                _ = Task.Run(async () => await startupService.InitializeAsync());

                // فحص قاعدة البيانات
                var canConnect = await startupService.IsDatabaseReadyAsync();

                if (!canConnect)
                {
                    // Show database setup window
                    await ShowDatabaseSetupWindowAsync();
                }
                else
                {
                    // Initialize database and show login
                    await InitializeDatabaseAndShowLoginAsync();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة التطبيق: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                Shutdown();
                return;
            }

            base.OnStartup(e);
        }

        protected override void OnExit(ExitEventArgs e)
        {
            _host?.Dispose();
            base.OnExit(e);
        }

        public static T GetService<T>() where T : class
        {
            var app = (App)Current;
            return app._host!.Services.GetRequiredService<T>();
        }

        public static IServiceScope CreateScope()
        {
            var app = (App)Current;
            return app._host!.Services.CreateScope();
        }

        private async Task ShowDatabaseSetupWindowAsync()
        {
            var databaseSetupWindow = new DatabaseSetupWindow();
            var viewModel = _host?.Services.GetRequiredService<DatabaseSetupViewModel>();
            if (viewModel != null)
            {
                databaseSetupWindow.DataContext = viewModel;

                // Handle events
                viewModel.DatabaseSetupCompleted += async () =>
                {
                    databaseSetupWindow.Close();
                    await InitializeDatabaseAndShowLoginAsync();
                };

                viewModel.ExitRequested += () =>
                {
                    databaseSetupWindow.Close();
                    Shutdown();
                };
            }

            databaseSetupWindow.ShowDialog();
            await Task.CompletedTask; // To satisfy async method requirement
        }

        private async Task InitializeDatabaseAndShowLoginAsync()
        {
            try
            {
                // Initialize database
                var context = _host?.Services.GetRequiredService<NetworkDbContext>();
                var passwordHashService = _host?.Services.GetRequiredService<IPasswordHashService>();
                if (context != null)
                {
                    await DatabaseInitializer.InitializeAsync(context, passwordHashService);
                }

                // Update database with new fields (PowerCableType)
                var databaseService = _host?.Services.GetRequiredService<IDatabaseService>();
                if (databaseService != null)
                {
                    await databaseService.UpdateDatabaseWithPowerCableTypeAsync();
                }

                // Show login window
                var loginWindow = new LoginWindow();
                loginWindow.Show();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة قاعدة البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                Shutdown();
            }
        }
    }
}
