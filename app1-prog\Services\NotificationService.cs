using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;
using NetworkManagement.Helpers;

namespace NetworkManagement.Services
{
    public class NotificationService : INotificationService
    {
        private bool _notificationsEnabled = true;
        private readonly DispatcherTimer _cleanupTimer;
        private readonly IAuthService? _authService;

        private readonly ObservableCollection<NotificationItem> _allNotifications = new();
        public ObservableCollection<NotificationItem> Notifications { get; } = new();

        public NotificationService(IAuthService? authService = null)
        {
            _authService = authService;

            // Timer to cleanup old notifications
            _cleanupTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMinutes(1)
            };
            _cleanupTimer.Tick += CleanupOldNotifications;
            _cleanupTimer.Start();

            // الاستماع لتغييرات المستخدم لتطبيق فلترة الصلاحيات
            if (_authService != null)
            {
                _authService.UserChanged += OnUserChanged;
            }
        }

        public async Task ShowInfoAsync(string title, string message)
        {
            await ShowCustomAsync(title, message, NotificationType.Info);
        }

        public async Task ShowWarningAsync(string title, string message)
        {
            await ShowCustomAsync(title, message, NotificationType.Warning);
        }

        public async Task ShowErrorAsync(string title, string message)
        {
            await ShowCustomAsync(title, message, NotificationType.Error);
        }

        public async Task ShowSuccessAsync(string title, string message)
        {
            await ShowCustomAsync(title, message, NotificationType.Success);
        }

        public async Task ShowCustomAsync(string title, string message, NotificationType type, TimeSpan? duration = null)
        {
            await ShowOperationAsync(title, message, type, null, null, null, null, null);
        }

        public async Task ShowOperationAsync(string title, string message, NotificationType type, string? userName = null, string? userId = null, string? operationType = null, string? affectedItem = null, string? networkId = null)
        {
            if (!_notificationsEnabled) return;

            await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                var notification = new NotificationItem
                {
                    Id = Guid.NewGuid(),
                    Title = title,
                    Message = message,
                    Type = type,
                    Timestamp = DateTime.Now,
                    Duration = GetDefaultDuration(type),
                    UserName = userName,
                    UserId = userId,
                    OperationType = operationType,
                    AffectedItem = affectedItem,
                    NetworkId = networkId
                };

                // إضافة الإشعار إلى القائمة الكاملة
                _allNotifications.Insert(0, notification);

                // Keep only last 50 notifications in all notifications
                while (_allNotifications.Count > 50)
                {
                    _allNotifications.RemoveAt(_allNotifications.Count - 1);
                }

                // تطبيق فلترة الصلاحيات وتحديث القائمة المعروضة
                ApplyPermissionFilter();

                // Show system notification if available
                ShowSystemNotification(title, message, type);
            });
        }

        public async Task ShowDeviceStatusAsync(string deviceName, string status, bool isOnline)
        {
            var title = isOnline ? "جهاز متصل" : "جهاز غير متصل";
            var message = $"{deviceName}: {status}";
            var type = isOnline ? NotificationType.Success : NotificationType.Warning;
            
            await ShowCustomAsync(title, message, type);
        }

        public async Task ShowLowStockAsync(string itemName, int currentStock, int minimumStock)
        {
            var title = "تحذير: مخزون منخفض";
            var message = $"{itemName}: الكمية الحالية {currentStock} أقل من الحد الأدنى {minimumStock}";
            
            await ShowCustomAsync(title, message, NotificationType.Warning, TimeSpan.FromMinutes(5));
        }

        public async Task ShowTaskCompletedAsync(string taskTitle)
        {
            var title = "مهمة مكتملة";
            var message = $"تم إكمال المهمة: {taskTitle}";
            
            await ShowCustomAsync(title, message, NotificationType.Success);
        }

        public async Task ShowBackupNotificationAsync(bool success, string message)
        {
            var title = success ? "نسخة احتياطية ناجحة" : "فشل في النسخة الاحتياطية";
            var type = success ? NotificationType.Success : NotificationType.Error;
            
            await ShowCustomAsync(title, message, type, TimeSpan.FromMinutes(3));
        }

        public void ClearAllNotifications()
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                _allNotifications.Clear();
                Notifications.Clear();
            });
        }

        public void SetNotificationsEnabled(bool enabled)
        {
            _notificationsEnabled = enabled;
        }

        private TimeSpan GetDefaultDuration(NotificationType type)
        {
            return type switch
            {
                NotificationType.Error => TimeSpan.FromMinutes(5),
                NotificationType.Warning => TimeSpan.FromMinutes(3),
                NotificationType.Success => TimeSpan.FromMinutes(2),
                NotificationType.Info => TimeSpan.FromMinutes(1),
                _ => TimeSpan.FromMinutes(2)
            };
        }

        private void ShowSystemNotification(string title, string message, NotificationType type)
        {
            try
            {
                // For now, we'll use MessageBox for critical errors only
                if (type == NotificationType.Error)
                {
                    // Could implement Windows Toast notifications here
                    // For now, just log to debug
                    System.Diagnostics.Debug.WriteLine($"[{type}] {title}: {message}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error showing system notification: {ex.Message}");
            }
        }

        private void CleanupOldNotifications(object? sender, EventArgs e)
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                var cutoffTime = DateTime.Now;

                // تنظيف الإشعارات القديمة من القائمة الكاملة
                for (int i = _allNotifications.Count - 1; i >= 0; i--)
                {
                    var notification = _allNotifications[i];
                    if (cutoffTime - notification.Timestamp > notification.Duration)
                    {
                        _allNotifications.RemoveAt(i);
                    }
                }

                // إعادة تطبيق الفلترة بعد التنظيف
                ApplyPermissionFilter();
            });
        }

        /// <summary>
        /// تطبيق فلترة الصلاحيات على الإشعارات
        /// </summary>
        private void ApplyPermissionFilter()
        {
            if (_authService == null)
            {
                // إذا لم يكن هناك خدمة مصادقة، عرض جميع الإشعارات
                Notifications.Clear();
                foreach (var notification in _allNotifications.Take(10))
                {
                    Notifications.Add(notification);
                }
                return;
            }

            // فلترة الإشعارات حسب الصلاحيات
            var filteredNotifications = _allNotifications.Where(n => CanUserViewNotification(n)).Take(10);

            Notifications.Clear();
            foreach (var notification in filteredNotifications)
            {
                Notifications.Add(notification);
            }
        }

        /// <summary>
        /// تحديد ما إذا كان المستخدم يمكنه رؤية الإشعار
        /// </summary>
        private bool CanUserViewNotification(NotificationItem notification)
        {
            if (_authService == null) return true;

            // Super Admin يرى جميع الإشعارات
            if (_authService.IsSuperAdmin) return true;

            // إذا لم يكن للإشعار شبكة محددة، يمكن للجميع رؤيته
            if (string.IsNullOrEmpty(notification.NetworkId)) return true;

            // التحقق من صلاحية الوصول للشبكة
            return _authService.CanAccessNetwork(notification.NetworkId);
        }

        /// <summary>
        /// معالج تغيير المستخدم لإعادة تطبيق الفلترة
        /// </summary>
        private void OnUserChanged(object? sender, Models.User? user)
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                ApplyPermissionFilter();
            });
        }
    }

    public class NotificationItem
    {
        public Guid Id { get; set; }
        public string Title { get; set; } = "";
        public string Message { get; set; } = "";
        public NotificationType Type { get; set; }
        public DateTime Timestamp { get; set; }
        public TimeSpan Duration { get; set; }

        // معلومات المستخدم والعملية
        public string? UserName { get; set; }
        public string? UserId { get; set; }
        public string? OperationType { get; set; } // إضافة، تعديل، حذف
        public string? AffectedItem { get; set; } // اسم العنصر المتأثر
        public string? NetworkId { get; set; } // الشبكة المرتبطة بالعملية
        
        public string TypeIcon => Type switch
        {
            NotificationType.Info => "Information",
            NotificationType.Warning => "Warning",
            NotificationType.Error => "Error",
            NotificationType.Success => "CheckCircle",
            _ => "Information"
        };

        public string TypeColor => Type switch
        {
            NotificationType.Info => "#2196F3",
            NotificationType.Warning => "#FF9800",
            NotificationType.Error => "#F44336",
            NotificationType.Success => "#4CAF50",
            _ => "#2196F3"
        };

        public string TimeAgo
        {
            get
            {
                var timeSpan = DateTime.Now - Timestamp;
                if (timeSpan.TotalMinutes < 1)
                    return "الآن";
                if (timeSpan.TotalMinutes < 60)
                    return $"{(int)timeSpan.TotalMinutes} دقيقة";
                if (timeSpan.TotalHours < 24)
                    return $"{(int)timeSpan.TotalHours} ساعة";
                return $"{(int)timeSpan.TotalDays} يوم";
            }
        }
    }
}
