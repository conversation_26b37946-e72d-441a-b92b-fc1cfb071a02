using System;
using System.Windows;
using Microsoft.Extensions.DependencyInjection;
using NetworkManagement.ViewModels;
using NetworkManagement.Models;

namespace NetworkManagement.Views
{
    public partial class TaskDialogView : Window, IDisposable
    {
        private IServiceScope? _scope;
        private TaskDialogViewModel? _viewModel;

        public TaskDialogView()
        {
            InitializeComponent();
            Loaded += TaskDialogView_Loaded;
        }

        public TaskDialogView(Models.Task? task = null) : this()
        {
            Tag = task; // Store task for later use
        }

        private async void TaskDialogView_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                _scope = App.CreateScope();
                _viewModel = _scope.ServiceProvider.GetRequiredService<TaskDialogViewModel>();
                DataContext = _viewModel;

                _viewModel.RequestClose += () =>
                {
                    DialogResult = _viewModel.DialogResult;
                    Close();
                };

                // Initialize with task if provided
                var task = Tag as Models.Task;
                await _viewModel.InitializeAsync(task);
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"خطأ في تحميل نافذة المهمة:\n{ex.Message}",
                    "خطأ",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
                Close();
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            Dispose();
            base.OnClosed(e);
        }

        public void Dispose()
        {
            try
            {
                if (_viewModel != null)
                {
                    _viewModel.RequestClose -= () => { };
                }
                _scope?.Dispose();
                _viewModel = null;
                _scope = null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error disposing TaskDialogView: {ex.Message}");
            }
            GC.SuppressFinalize(this);
        }
    }
}
