using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Media;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using NetworkManagement.Helpers;
using NetworkManagement.Models;
using NetworkManagement.Services;

namespace NetworkManagement.ViewModels
{
    public partial class MyEvaluationViewModel : ObservableObject
    {
        private readonly IUserEvaluationService _evaluationService;
        private readonly IAuthService _authService;
        private readonly INotificationService _notificationService;

        [ObservableProperty]
        private ObservableCollection<UserEvaluation> myEvaluations = new();

        [ObservableProperty]
        private UserEvaluationStatistics? myStatistics;

        [ObservableProperty]
        private DateTime startDate = DateTime.Today.AddDays(-30);

        [ObservableProperty]
        private DateTime endDate = DateTime.Today;

        [ObservableProperty]
        private bool isLoading = false;

        [ObservableProperty]
        private string selectedPeriod = "آخر 30 يوم";

        [ObservableProperty]
        private decimal currentAveragePercentage = 0;

        [ObservableProperty]
        private int totalEvaluationsCount = 0;

        [ObservableProperty]
        private string performanceLevel = "غير محدد";

        [ObservableProperty]
        private Brush performanceLevelColor = new SolidColorBrush(Color.FromRgb(102, 102, 102));

        public ObservableCollection<string> PeriodOptions { get; } = new()
        {
            "آخر 7 أيام",
            "آخر 30 يوم",
            "آخر 3 أشهر",
            "آخر 6 أشهر",
            "السنة الحالية",
            "جميع الفترات"
        };

        public MyEvaluationViewModel(
            IUserEvaluationService evaluationService,
            IAuthService authService,
            INotificationService notificationService)
        {
            _evaluationService = evaluationService;
            _authService = authService;
            _notificationService = notificationService;
        }

        public async System.Threading.Tasks.Task InitializeAsync()
        {
            try
            {
                IsLoading = true;

                if (_authService.CurrentUser == null)
                {
                    await _notificationService.ShowErrorAsync("خطأ", "لم يتم العثور على بيانات المستخدم الحالي");
                    return;
                }

                await LoadMyEvaluationsAsync();
                await LoadMyStatisticsAsync();
                UpdatePerformanceLevel();
            }
            catch (Exception ex)
            {
                await ErrorHandler.HandleExceptionAsync(ex, "تهيئة صفحة تقييماتي", "MyEvaluationViewModel");
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task LoadMyEvaluationsAsync()
        {
            try
            {
                if (_authService.CurrentUser == null)
                    return;

                IsLoading = true;

                var evaluations = await _evaluationService.GetByUserIdAsync(
                    _authService.CurrentUser.Id, StartDate, EndDate);

                MyEvaluations.Clear();
                foreach (var evaluation in evaluations.OrderByDescending(e => e.EvaluationDate))
                {
                    MyEvaluations.Add(evaluation);
                }

                TotalEvaluationsCount = MyEvaluations.Count;
            }
            catch (Exception ex)
            {
                await ErrorHandler.HandleExceptionAsync(ex, "تحميل تقييماتي", "MyEvaluationViewModel");
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task LoadMyStatisticsAsync()
        {
            try
            {
                if (_authService.CurrentUser == null)
                    return;

                MyStatistics = await _evaluationService.GetUserStatisticsAsync(
                    _authService.CurrentUser.Id, StartDate, EndDate);

                CurrentAveragePercentage = MyStatistics?.AveragePercentage ?? 0;
            }
            catch (Exception ex)
            {
                await ErrorHandler.HandleExceptionAsync(ex, "تحميل إحصائياتي", "MyEvaluationViewModel");
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task ChangePeriodAsync()
        {
            try
            {
                var today = DateTime.Today;
                
                (StartDate, EndDate) = SelectedPeriod switch
                {
                    "آخر 7 أيام" => (today.AddDays(-7), today),
                    "آخر 30 يوم" => (today.AddDays(-30), today),
                    "آخر 3 أشهر" => (today.AddMonths(-3), today),
                    "آخر 6 أشهر" => (today.AddMonths(-6), today),
                    "السنة الحالية" => (new DateTime(today.Year, 1, 1), today),
                    "جميع الفترات" => (DateTime.MinValue, today),
                    _ => (today.AddDays(-30), today)
                };

                await LoadMyEvaluationsAsync();
                await LoadMyStatisticsAsync();
                UpdatePerformanceLevel();
            }
            catch (Exception ex)
            {
                await ErrorHandler.HandleExceptionAsync(ex, "تغيير الفترة", "MyEvaluationViewModel");
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task RefreshDataAsync()
        {
            await InitializeAsync();
        }

        private void UpdatePerformanceLevel()
        {
            var percentage = CurrentAveragePercentage;

            (PerformanceLevel, PerformanceLevelColor) = percentage switch
            {
                >= 90 => ("ممتاز", new SolidColorBrush(Color.FromRgb(76, 175, 80))),
                >= 80 => ("جيد جداً", new SolidColorBrush(Color.FromRgb(139, 195, 74))),
                >= 70 => ("جيد", new SolidColorBrush(Color.FromRgb(255, 193, 7))),
                >= 60 => ("مقبول", new SolidColorBrush(Color.FromRgb(255, 152, 0))),
                >= 50 => ("ضعيف", new SolidColorBrush(Color.FromRgb(255, 87, 34))),
                _ => ("يحتاج تحسين", new SolidColorBrush(Color.FromRgb(244, 67, 54)))
            };
        }

        partial void OnStartDateChanged(DateTime value)
        {
            _ = System.Threading.Tasks.Task.Run(async () =>
            {
                await LoadMyEvaluationsAsync();
                await LoadMyStatisticsAsync();
                UpdatePerformanceLevel();
            });
        }

        partial void OnEndDateChanged(DateTime value)
        {
            _ = System.Threading.Tasks.Task.Run(async () =>
            {
                await LoadMyEvaluationsAsync();
                await LoadMyStatisticsAsync();
                UpdatePerformanceLevel();
            });
        }

        // خصائص إضافية للعرض
        public string WelcomeMessage => _authService.CurrentUser != null 
            ? $"مرحباً {_authService.CurrentUser.Name}" 
            : "مرحباً";

        public string CurrentUserRole => _authService.CurrentUser?.RoleDisplay ?? "غير محدد";

        public string NetworkName => _authService.CurrentUser?.NetworkName ?? "غير محدد";

        public bool HasEvaluations => MyEvaluations.Any();

        public string NoEvaluationsMessage => TotalEvaluationsCount == 0 
            ? "لا توجد تقييمات في الفترة المحددة" 
            : string.Empty;

        public string AverageScoreText => MyStatistics != null 
            ? $"متوسط النقاط: {MyStatistics.AverageScoreDisplay}" 
            : "لا توجد بيانات";

        public string ScoreRangeText => MyStatistics != null && MyStatistics.TotalEvaluations > 0
            ? $"النطاق: {MyStatistics.ScoreRangeDisplay}" 
            : "لا توجد بيانات";

        public string LastEvaluationText => MyStatistics?.LastEvaluationDisplay != "لا يوجد"
            ? $"آخر تقييم: {MyStatistics?.LastEvaluationDisplay}"
            : "لم يتم التقييم بعد";

        public string TotalEvaluationsText => $"إجمالي التقييمات: {TotalEvaluationsCount}";

        public string PerformanceMessage => CurrentAveragePercentage > 0
            ? $"أداؤك {PerformanceLevel} بنسبة {CurrentAveragePercentage:F1}%"
            : "لا توجد تقييمات لحساب الأداء";

        public string MotivationalMessage => CurrentAveragePercentage switch
        {
            >= 90 => "أداء رائع! استمر على هذا المستوى المتميز",
            >= 80 => "أداء جيد جداً! يمكنك الوصول للامتياز",
            >= 70 => "أداء جيد، حاول تحسينه أكثر",
            >= 60 => "أداء مقبول، يحتاج لمزيد من الجهد",
            >= 50 => "أداء ضعيف، يجب العمل على التحسين",
            _ => CurrentAveragePercentage > 0 ? "يحتاج لتحسين كبير، لا تستسلم!" : "ابدأ رحلتك نحو التميز"
        };
    }
}
