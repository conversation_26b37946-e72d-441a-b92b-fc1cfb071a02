using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using NetworkManagement.Services;
using NetworkManagement.Models;
using NetworkManagement.Views;
using NetworkManagement.Helpers;
using Microsoft.Extensions.DependencyInjection;
using System.Threading;
using System.Threading.Tasks;

namespace NetworkManagement.ViewModels
{
    public partial class TasksViewModel : ObservableObject, IDisposable
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IAuthService _authService;
        private readonly IReportExportService _exportService;
        private readonly INotificationService _notificationService;

        [ObservableProperty]
        private ObservableCollection<Models.Task> tasks = new();

        [ObservableProperty]
        private ObservableCollection<Models.Task> filteredTasks = new();

        [ObservableProperty]
        private Models.Task? selectedTask;

        [ObservableProperty]
        private bool isLoading = false;

        [ObservableProperty]
        private string statusFilter = TaskConstants.StatusDisplay.All;

        [ObservableProperty]
        private string priorityFilter = TaskConstants.PriorityDisplay.All;

        [ObservableProperty]
        private string sortBy = "RequestDate";

        [ObservableProperty]
        private bool sortDescending = true;

        // خيارات الترتيب
        public List<string> SortOptions { get; } = new()
        {
            "RequestDate",
            "CompletedAt",
            "Priority",
            "Status"
        };

        public List<string> SortOptionsDisplay { get; } = new()
        {
            "تاريخ الطلب",
            "تاريخ الإكمال",
            "الأولوية",
            "الحالة"
        };

        public int SelectedSortIndex
        {
            get => SortOptions.IndexOf(SortBy);
            set
            {
                if (value >= 0 && value < SortOptions.Count)
                {
                    var newSortBy = SortOptions[value];
                    if (SortBy != newSortBy)
                    {
                        SortBy = newSortBy;
                        SortDescending = true; // ابدأ بالتنازلي للترتيب الجديد
                        ApplyFilters();
                    }
                }
            }
        }

        [ObservableProperty]
        private string searchText = string.Empty;

        // خصائص فلتر الشبكة
        [ObservableProperty]
        private ObservableCollection<Network> availableNetworks = new();

        [ObservableProperty]
        private Network? selectedNetwork;

        [ObservableProperty]
        private string networkFilter = "الكل";

        // خصائص إضافية للتحكم في التصدير
        public bool CanViewAllNetworks => _authService?.CanViewAllNetworks ?? false;

        [ObservableProperty]
        private int pendingTasksCount = 0;

        [ObservableProperty]
        private int inProgressTasksCount = 0;

        [ObservableProperty]
        private int completedTasksCount = 0;

        // خصائص الصلاحيات
        public bool CanAddTasks => _authService.CanAddData();
        public bool CanEditTasks => _authService.CanEditData();
        public bool CanDeleteTasks => _authService.CanDeleteData();
        public bool CanManageTasks => _authService.CanManageTasks;

        // Threading control
        private readonly System.Threading.SemaphoreSlim _loadSemaphore = new(1, 1);
        private System.Threading.CancellationTokenSource? _loadCancellationTokenSource;
        private readonly object _loadLock = new();
        private readonly TimeSpan _semaphoreTimeout = TimeSpan.FromSeconds(30);
        private bool _isDisposed = false;

        public TasksViewModel(IServiceProvider serviceProvider, IAuthService authService, IReportExportService exportService, INotificationService notificationService)
        {
            _serviceProvider = serviceProvider;
            _authService = authService;
            _exportService = exportService;
            _notificationService = notificationService;

            // الاشتراك في تغيير المستخدم لتحديث الصلاحيات
            _authService.UserChanged += OnUserChanged;
        }

        public async System.Threading.Tasks.Task InitializeAsync()
        {
            try
            {
                await LoadNetworksAsync();
                await LoadTasksAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error initializing tasks: {ex.Message}");
                MessageBox.Show(
                    $"حدث خطأ أثناء تحميل المهام:\n{ex.Message}",
                    "خطأ في التحميل",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }

        private async System.Threading.Tasks.Task LoadNetworksAsync()
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var networkService = scope.ServiceProvider.GetRequiredService<INetworkService>();

                var networks = await networkService.GetAllAsync();

                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    AvailableNetworks.Clear();

                    // إضافة خيار "الكل" للمستخدمين الذين يمكنهم رؤية جميع الشبكات
                    if (_authService.CanViewAllNetworks)
                    {
                        AvailableNetworks.Add(new Network { Id = "all", Name = "الكل" });
                    }

                    foreach (var network in networks)
                    {
                        // إضافة الشبكات التي يمكن للمستخدم الوصول إليها
                        if (_authService.CanViewAllNetworks || _authService.CanAccessNetwork(network.Id))
                        {
                            AvailableNetworks.Add(network);
                        }
                    }

                    // تعيين الشبكة الافتراضية
                    if (_authService.CanViewAllNetworks)
                    {
                        SelectedNetwork = AvailableNetworks.FirstOrDefault(n => n.Id == "all");
                    }
                    else
                    {
                        SelectedNetwork = AvailableNetworks.FirstOrDefault(n => n.Id == _authService.CurrentUserNetworkId);
                    }
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading networks: {ex.Message}");
            }
        }

        private async System.Threading.Tasks.Task LoadTasksAsync()
        {
            // التحقق من التخلص من الكائن
            if (_isDisposed) return;

            // منع التداخل في العمليات مع timeout محسن
            if (!await _loadSemaphore.WaitAsync(_semaphoreTimeout))
            {
                ErrorHandler.LogWarning("انتهت مهلة انتظار تحميل المهام", "TasksViewModel");
                return;
            }

            try
            {
                // إلغاء أي عملية سابقة
                lock (_loadLock)
                {
                    _loadCancellationTokenSource?.Cancel();
                    _loadCancellationTokenSource = new System.Threading.CancellationTokenSource();
                }

                IsLoading = true;
                var cancellationToken = _loadCancellationTokenSource.Token;

                // إنشاء scope جديد للحصول على TaskService
                using var scope = _serviceProvider.CreateScope();
                var taskService = scope.ServiceProvider.GetRequiredService<ITaskService>();

                // الحصول على الشبكات المسموحة للمستخدم مع مراعاة الفلتر المحدد
                var allowedNetworkIds = GetFilteredNetworkIds();

                // استخدام الفلترة المحسنة على مستوى قاعدة البيانات
                var filteredTasks = await taskService.GetFilteredAsync(
                    networkIds: allowedNetworkIds,
                    status: StatusFilter != TaskConstants.StatusDisplay.All ? TaskHelper.GetStatusKey(StatusFilter) : null,
                    priority: PriorityFilter != TaskConstants.PriorityDisplay.All ? TaskHelper.GetPriorityKey(PriorityFilter) : null,
                    searchText: !string.IsNullOrWhiteSpace(SearchText) ? SearchText : null
                );

                // التحقق من الإلغاء مرة أخرى
                cancellationToken.ThrowIfCancellationRequested();

                // تحديث UI على الـ UI thread
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    Tasks.Clear();
                    foreach (var task in filteredTasks)
                    {
                        Tasks.Add(task);
                    }
                    ApplyFilters();
                    UpdateTaskCounts();
                }, System.Windows.Threading.DispatcherPriority.Normal, cancellationToken);
            }
            catch (OperationCanceledException)
            {
                System.Diagnostics.Debug.WriteLine("LoadTasksAsync: تم إلغاء العملية");
            }
            catch (Exception ex)
            {
                ErrorHandler.HandleException(ex, "تحميل المهام", "TasksViewModel", true);
            }
            finally
            {
                IsLoading = false;
                _loadSemaphore.Release();
            }
        }

        private IEnumerable<string>? GetFilteredNetworkIds()
        {
            // إذا كان المستخدم Super Admin ولم يحدد شبكة معينة
            if (_authService.CanViewAllNetworks && (SelectedNetwork == null || SelectedNetwork.Id == "all"))
            {
                return null; // عرض جميع الشبكات
            }

            // إذا تم تحديد شبكة معينة
            if (SelectedNetwork != null && SelectedNetwork.Id != "all")
            {
                return new List<string> { SelectedNetwork.Id };
            }

            // للمستخدمين العاديين، عرض شبكتهم فقط
            return PermissionHelper.GetAllowedNetworkIds(_authService);
        }

        private void ApplyFilters()
        {
            // لا نحتاج فلترة إضافية هنا لأن البيانات مفلترة من قاعدة البيانات
            // هذه الدالة تطبق الترتيب المحدد
            FilteredTasks.Clear();

            var sortedTasks = ApplySorting(Tasks);

            foreach (var task in sortedTasks)
            {
                FilteredTasks.Add(task);
            }
        }

        private IEnumerable<Models.Task> ApplySorting(IEnumerable<Models.Task> tasks)
        {
            return SortBy switch
            {
                "RequestDate" => SortDescending
                    ? tasks.OrderByDescending(t => t.RequestDate)
                    : tasks.OrderBy(t => t.RequestDate),

                "CompletedAt" => SortDescending
                    ? tasks.OrderByDescending(t => t.CompletedAt ?? DateTime.MinValue)
                    : tasks.OrderBy(t => t.CompletedAt ?? DateTime.MaxValue),

                "Priority" => SortDescending
                    ? tasks.OrderByDescending(t => GetPriorityOrder(t.Priority))
                    : tasks.OrderBy(t => GetPriorityOrder(t.Priority)),

                "Status" => SortDescending
                    ? tasks.OrderByDescending(t => GetStatusOrder(t.Status))
                    : tasks.OrderBy(t => GetStatusOrder(t.Status)),

                _ => SortDescending
                    ? tasks.OrderByDescending(t => t.RequestDate)
                    : tasks.OrderBy(t => t.RequestDate)
            };
        }

        private int GetPriorityOrder(string? priority)
        {
            return priority switch
            {
                TaskConstants.Priority.High => 3,
                TaskConstants.Priority.Medium => 2,
                TaskConstants.Priority.Low => 1,
                _ => 0
            };
        }

        private int GetStatusOrder(string? status)
        {
            return status switch
            {
                TaskConstants.Status.InProgress => 3,
                TaskConstants.Status.Pending => 2,
                TaskConstants.Status.Completed => 1,
                _ => 0
            };
        }

        [RelayCommand]
        private void ChangeSortBy(string newSortBy)
        {
            if (SortBy == newSortBy)
            {
                // إذا كان نفس الترتيب، عكس الاتجاه
                SortDescending = !SortDescending;
            }
            else
            {
                // ترتيب جديد، ابدأ بالتنازلي
                SortBy = newSortBy;
                SortDescending = true;
                OnPropertyChanged(nameof(SelectedSortIndex));
            }

            ApplyFilters();
        }

        [RelayCommand]
        private void ToggleSortDirection()
        {
            SortDescending = !SortDescending;
            ApplyFilters();
        }

        private void UpdateTaskCounts()
        {
            PendingTasksCount = Tasks.Count(t => t.Status == TaskConstants.Status.Pending);
            InProgressTasksCount = Tasks.Count(t => t.Status == TaskConstants.Status.InProgress);
            CompletedTasksCount = Tasks.Count(t => t.Status == TaskConstants.Status.Completed);
        }



        // Property Changed Handlers
        partial void OnStatusFilterChanged(string value)
        {
            _ = LoadTasksAsync();
        }

        partial void OnPriorityFilterChanged(string value)
        {
            _ = LoadTasksAsync();
        }

        partial void OnSearchTextChanged(string value)
        {
            _ = LoadTasksAsync();
        }

        partial void OnSelectedNetworkChanged(Network? value)
        {
            NetworkFilter = value?.Name ?? "الكل";
            _ = LoadTasksAsync();
        }

        partial void OnNetworkFilterChanged(string value)
        {
            // تحديث الشبكة المحددة عند تغيير النص
            if (value == "الكل")
            {
                SelectedNetwork = AvailableNetworks.FirstOrDefault(n => n.Id == "all");
            }
            else
            {
                SelectedNetwork = AvailableNetworks.FirstOrDefault(n => n.Name == value);
            }
        }

        // Commands
        [RelayCommand]
        private async System.Threading.Tasks.Task RefreshAsync()
        {
            await LoadTasksAsync();
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task AddTaskAsync()
        {
            // التحقق من صلاحية إضافة المهام
            if (!CanAddTasks)
            {
                PermissionHelper.ShowPermissionDeniedMessage("إضافة", "مهام جديدة");
                return;
            }

            await ErrorHandler.ExecuteWithErrorHandlingAsync(async () =>
            {
                var dialog = new TaskDialogView();

                if (dialog.ShowDialog() == true)
                {
                    await LoadTasksAsync();
                }
            }, "فتح نافذة إضافة المهمة", "TasksViewModel");
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task EditTaskAsync(Models.Task? task = null)
        {
            var taskToEdit = task ?? SelectedTask;
            if (taskToEdit == null) return;

            // التحقق من صلاحية تعديل المهمة
            if (!PermissionHelper.CanEditItem(_authService, taskToEdit.NetworkId))
            {
                PermissionHelper.ShowPermissionDeniedMessage("تعديل", "هذه المهمة");
                return;
            }

            try
            {
                var dialog = new TaskDialogView(taskToEdit);

                if (dialog.ShowDialog() == true)
                {
                    await LoadTasksAsync();
                }
            }
            catch (Exception ex)
            {
                ShowErrorMessage("فتح نافذة تحرير المهمة", ex.Message);
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task CompleteTaskAsync(Models.Task? task = null)
        {
            var taskToComplete = task ?? SelectedTask;
            if (taskToComplete != null && TaskHelper.CanCompleteTask(taskToComplete))
            {
                // التحقق من صلاحية تعديل المهمة
                if (!PermissionHelper.CanEditItem(_authService, taskToComplete.NetworkId))
                {
                    PermissionHelper.ShowPermissionDeniedMessage("تعديل", "هذه المهمة");
                    return;
                }

                await ErrorHandler.ExecuteWithErrorHandlingAsync(async () =>
                {
                    taskToComplete.Status = TaskConstants.Status.Completed;
                    taskToComplete.CompletedAt = DateTime.Now;

                    await UpdateTaskAndRefreshAsync(taskToComplete);

                    // إشعار إكمال المهمة
                    var userName = _authService.CurrentUser?.Name ?? "المستخدم";
                    var userId = _authService.CurrentUser?.Id;
                    await _notificationService.ShowOperationAsync(
                        "تم إكمال المهمة",
                        $"قام {userName} بإكمال المهمة: {taskToComplete.Description}",
                        NotificationType.Success,
                        userName, userId, "إكمال", taskToComplete.Description, taskToComplete.NetworkId);
                }, "إكمال المهمة", "TasksViewModel");
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task DeleteTaskAsync(Models.Task? task = null)
        {
            var taskToDelete = task ?? SelectedTask;
            if (taskToDelete == null) return;

            // التحقق من صلاحية حذف المهمة
            if (!PermissionHelper.CanDeleteItem(_authService, taskToDelete.NetworkId))
            {
                PermissionHelper.ShowPermissionDeniedMessage("حذف", "هذه المهمة");
                return;
            }

            var result = MessageBox.Show(
                $"هل أنت متأكد من حذف المهمة:\n{taskToDelete.Description}؟\n\nلا يمكن التراجع عن هذا الإجراء.",
                "تأكيد الحذف",
                MessageBoxButton.YesNo,
                MessageBoxImage.Warning);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    await DeleteTaskAndRefreshAsync(taskToDelete.Id);

                    // إشعار حذف المهمة
                    var userName = _authService.CurrentUser?.Name ?? "المستخدم";
                    var userId = _authService.CurrentUser?.Id;
                    await _notificationService.ShowOperationAsync(
                        "تم حذف المهمة",
                        $"قام {userName} بحذف المهمة: {taskToDelete.Description}",
                        NotificationType.Success,
                        userName, userId, "حذف", taskToDelete.Description, taskToDelete.NetworkId);
                }
                catch (Exception ex)
                {
                    await ErrorHandler.HandleExceptionWithNotificationAsync(ex, "حذف المهمة", "TasksViewModel", taskToDelete.NetworkId);
                }
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task ExportTasksAsync()
        {
            try
            {
                IsLoading = true;

                var filter = "Excel Files (*.xlsx)|*.xlsx|CSV Files (*.csv)|*.csv";

                // تحسين اسم الملف ليتضمن معلومات الفلترة
                var networkName = SelectedNetwork?.Name ?? "الكل";
                var defaultFileName = $"المهام_{networkName}_{DateTime.Now:yyyy-MM-dd}";

                var filePath = await _exportService.GetSaveFilePathAsync(defaultFileName, filter);
                if (string.IsNullOrEmpty(filePath))
                    return;

                string result = "";
                var isExcel = filePath.EndsWith(".xlsx", StringComparison.OrdinalIgnoreCase);

                // استخدام المهام المفلترة الحالية (التي تتضمن فلتر الشبكة)
                var tasksToExport = FilteredTasks.ToList();
                result = isExcel
                    ? await _exportService.ExportTasksReportToExcelAsync(tasksToExport, filePath)
                    : await _exportService.ExportTasksReportToCsvAsync(tasksToExport, filePath);

                if (!string.IsNullOrEmpty(result))
                {
                    var networkInfo = networkName != "الكل" ? $" من شبكة {networkName}" : "";
                    MessageBox.Show(
                        $"تم تصدير {tasksToExport.Count} مهمة{networkInfo} بنجاح إلى:\n{result}",
                        "تم التصدير",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"حدث خطأ أثناء تصدير المهام:\n{ex.Message}",
                    "خطأ في التصدير",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task ExportAllTasksAsync()
        {
            try
            {
                IsLoading = true;

                // التحقق من الصلاحيات
                if (!CanViewAllNetworks)
                {
                    MessageBox.Show(
                        "ليس لديك صلاحية لتصدير جميع المهام",
                        "صلاحيات غير كافية",
                        MessageBoxButton.OK,
                        MessageBoxImage.Warning);
                    return;
                }

                var filter = "Excel Files (*.xlsx)|*.xlsx|CSV Files (*.csv)|*.csv";
                var defaultFileName = $"جميع_المهام_{DateTime.Now:yyyy-MM-dd}";

                var filePath = await _exportService.GetSaveFilePathAsync(defaultFileName, filter);
                if (string.IsNullOrEmpty(filePath))
                    return;

                string result = "";
                var isExcel = filePath.EndsWith(".xlsx", StringComparison.OrdinalIgnoreCase);

                // الحصول على جميع المهام من جميع الشبكات
                using var scope = _serviceProvider.CreateScope();
                var taskService = scope.ServiceProvider.GetRequiredService<ITaskService>();

                var allTasks = await taskService.GetFilteredAsync(
                    networkIds: null, // جميع الشبكات
                    status: null,
                    priority: null,
                    searchText: null
                );

                result = isExcel
                    ? await _exportService.ExportTasksReportToExcelAsync(allTasks, filePath)
                    : await _exportService.ExportTasksReportToCsvAsync(allTasks, filePath);

                if (!string.IsNullOrEmpty(result))
                {
                    MessageBox.Show(
                        $"تم تصدير {allTasks.Count()} مهمة من جميع الشبكات بنجاح إلى:\n{result}",
                        "تم التصدير",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"حدث خطأ أثناء تصدير جميع المهام:\n{ex.Message}",
                    "خطأ في التصدير",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task ImportTasksAsync()
        {
            try
            {
                IsLoading = true;
                var filter = "Excel Files (*.xlsx)|*.xlsx|CSV Files (*.csv)|*.csv";
                var filePath = await _exportService.GetOpenFilePathAsync(filter);
                if (string.IsNullOrEmpty(filePath))
                    return;

                var isExcel = filePath.EndsWith(".xlsx", StringComparison.OrdinalIgnoreCase);
                var imported = isExcel
                    ? await _exportService.ImportTasksFromExcelAsync(filePath)
                    : await _exportService.ImportTasksFromCsvAsync(filePath);

                var tasksList = imported?.ToList() ?? new List<Models.Task>();
                if (!tasksList.Any())
                {
                    MessageBox.Show("لم يتم العثور على مهام صالحة للاستيراد في الملف المحدد.", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // جلب المستخدمين والشبكات المتاحة للتحقق
                using var scope = _serviceProvider.CreateScope();
                var userService = scope.ServiceProvider.GetRequiredService<IUserService>();
                var networkService = scope.ServiceProvider.GetRequiredService<INetworkService>();
                var taskService = scope.ServiceProvider.GetRequiredService<ITaskService>();

                var availableUsers = (await userService.GetAllAsync()).ToList();
                var userLookup = availableUsers.ToDictionary(u => u.Name ?? "", u => u.Id, StringComparer.OrdinalIgnoreCase);
                var availableNetworks = (await networkService.GetAllAsync()).ToList();
                var networkLookup = availableNetworks.ToDictionary(n => n.Name ?? "", n => n.Id, StringComparer.OrdinalIgnoreCase);

                int successCount = 0;
                int errorCount = 0;
                var errors = new System.Collections.Generic.List<string>();

                foreach (var task in tasksList)
                {
                    try
                    {
                        // التحقق من الحقول المطلوبة
                        if (string.IsNullOrWhiteSpace(task.Description))
                        {
                            errorCount++;
                            errors.Add($"مهمة بدون وصف");
                            continue;
                        }

                        // معالجة UserId: إذا كان اسم المستخدم موجودًا، تحويله إلى المعرف
                        if (!string.IsNullOrWhiteSpace(task.UserId))
                        {
                            if (!userLookup.ContainsValue(task.UserId))
                            {
                                if (userLookup.TryGetValue(task.UserId, out var userId))
                                    task.UserId = userId;
                                else
                                    task.UserId = availableUsers.FirstOrDefault()?.Id ?? string.Empty;
                            }
                        }
                        else
                        {
                            // تعيين أول مستخدم متاح إذا لم يوجد
                            task.UserId = availableUsers.FirstOrDefault()?.Id ?? string.Empty;
                        }

                        // معالجة NetworkId بشكل محسن
                        task.NetworkId = ResolveNetworkId(task.NetworkId, networkLookup, _authService);

                        // التحقق من صلاحية إضافة مهمة في الشبكة المحددة
                        if (!_authService.CanAddData(task.NetworkId))
                        {
                            errorCount++;
                            var networkName = availableNetworks.FirstOrDefault(n => n.Id == task.NetworkId)?.Name ?? "غير محددة";
                            errors.Add($"ليس لديك صلاحية إضافة مهام في الشبكة: {networkName}");
                            continue;
                        }

                        // تعيين القيم الافتراضية للحقول المطلوبة
                        task.Id = Guid.NewGuid().ToString();
                        if (string.IsNullOrWhiteSpace(task.Status))
                            task.Status = TaskConstants.Status.Pending;
                        if (string.IsNullOrWhiteSpace(task.Priority))
                            task.Priority = TaskConstants.Priority.Medium;
                        if (task.RequestDate == default)
                            task.RequestDate = DateTime.Now;

                        // قص الحقول حسب الطول الأقصى
                        task.Description = task.Description.Length > 500 ? task.Description[..500] : task.Description;
                        task.Status = task.Status.Length > 20 ? task.Status[..20] : task.Status;
                        task.Priority = task.Priority != null && task.Priority.Length > 20 ? task.Priority[..20] : task.Priority;
                        task.Notes = task.Notes != null && task.Notes.Length > 1000 ? task.Notes[..1000] : task.Notes;

                        await taskService.CreateAsync(task);
                        successCount++;
                    }
                    catch (Exception ex)
                    {
                        errorCount++;
                        var taskDesc = task.Description ?? "مهمة غير محددة";
                        var errorMessage = $"خطأ في حفظ مهمة {taskDesc}: {ex.Message}";
                        errors.Add(errorMessage);
                        LoggingHelper.LogError($"فشل في استيراد المهمة: {taskDesc}", ex, "TasksViewModel");

                        // إيقاف العملية إذا كان هناك أخطاء كثيرة
                        if (errorCount > 10)
                        {
                            errors.Add("تم إيقاف العملية بسبب كثرة الأخطاء");
                            break;
                        }
                    }
                }

                await LoadTasksAsync();

                // عرض نتائج الاستيراد
                var resultMsg = $"تم استيراد {successCount} مهمة بنجاح.";
                if (errorCount > 0)
                    resultMsg += $"\nعدد المهام التي لم تُستورد: {errorCount}\n{string.Join("\n", errors)}";
                MessageBox.Show(resultMsg, "نتيجة الاستيراد", MessageBoxButton.OK, errorCount > 0 ? MessageBoxImage.Warning : MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء استيراد المهام:\n{ex.Message}", "خطأ في الاستيراد", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        // Helper methods to reduce code duplication
        private string? ResolveNetworkId(string? networkInput, Dictionary<string, string> networkLookup, IAuthService authService)
        {
            if (string.IsNullOrWhiteSpace(networkInput))
            {
                // إذا لم يتم تحديد شبكة، استخدم شبكة المستخدم الحالي
                return authService.CurrentUserNetworkId;
            }

            // محاولة البحث بالاسم أولاً
            if (networkLookup.TryGetValue(networkInput, out var networkId))
            {
                return networkId;
            }

            // محاولة البحث بالمعرف
            if (networkLookup.ContainsValue(networkInput))
            {
                return networkInput;
            }

            // إذا لم توجد الشبكة، استخدم شبكة المستخدم الحالي
            return authService.CurrentUserNetworkId;
        }

        private async System.Threading.Tasks.Task UpdateTaskAndRefreshAsync(Models.Task task)
        {
            using var scope = _serviceProvider.CreateScope();
            var taskService = scope.ServiceProvider.GetRequiredService<ITaskService>();

            await taskService.UpdateAsync(task);
            await LoadTasksAsync();
        }

        private async System.Threading.Tasks.Task DeleteTaskAndRefreshAsync(string taskId)
        {
            using var scope = _serviceProvider.CreateScope();
            var taskService = scope.ServiceProvider.GetRequiredService<ITaskService>();

            await taskService.DeleteAsync(taskId);
            await LoadTasksAsync();
        }

        private void ShowErrorMessage(string operation, string message)
        {
            MessageBox.Show(
                $"خطأ في {operation}:\n{message}",
                "خطأ",
                MessageBoxButton.OK,
                MessageBoxImage.Error);
        }

        private void ShowSuccessMessage(string operation, string message)
        {
            MessageBox.Show(
                message,
                operation,
                MessageBoxButton.OK,
                MessageBoxImage.Information);
        }

        private async void OnUserChanged(object? sender, User? user)
        {
            try
            {
                // تحديث خصائص الصلاحيات على UI thread
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    OnPropertyChanged(nameof(CanAddTasks));
                    OnPropertyChanged(nameof(CanEditTasks));
                    OnPropertyChanged(nameof(CanDeleteTasks));
                    OnPropertyChanged(nameof(CanManageTasks));
                    OnPropertyChanged(nameof(CanViewAllNetworks));
                });

                // تأخير قصير لتجنب التحديثات المتكررة
                await System.Threading.Tasks.Task.Delay(100);

                // إعادة تحميل الشبكات والبيانات حسب الصلاحيات الجديدة
                await LoadNetworksAsync();
                await LoadTasksAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in OnUserChanged: {ex.Message}");
            }
        }

        public void Dispose()
        {
            lock (_loadLock)
            {
                if (_isDisposed) return;
                _isDisposed = true;
            }

            try
            {
                LoggingHelper.LogInfo("بدء تنظيف موارد TasksViewModel", "TasksViewModel");

                // إلغاء الاشتراك في الأحداث
                _authService.UserChanged -= OnUserChanged;

                // إلغاء أي عمليات جارية
                _loadCancellationTokenSource?.Cancel();
                _loadCancellationTokenSource?.Dispose();
                _loadCancellationTokenSource = null;

                // تنظيف الموارد
                _loadSemaphore?.Dispose();

                // تنظيف المجموعات
                Tasks.Clear();
                FilteredTasks.Clear();

                LoggingHelper.LogInfo("تم تنظيف موارد TasksViewModel بنجاح", "TasksViewModel");
            }
            catch (Exception ex)
            {
                ErrorHandler.HandleException(ex, "تنظيف موارد TasksViewModel", "TasksViewModel", false);
            }

            GC.SuppressFinalize(this);
        }
    }
}
