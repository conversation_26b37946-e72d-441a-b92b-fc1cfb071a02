using System;
using System.Threading.Tasks;

namespace NetworkManagement.Services
{
    /// <summary>
    /// خدمة المزامنة التلقائية بين الأجهزة المتعددة
    /// </summary>
    public interface ISyncService
    {
        /// <summary>
        /// بدء خدمة المزامنة التلقائية
        /// </summary>
        Task StartSyncAsync();

        /// <summary>
        /// إيقاف خدمة المزامنة
        /// </summary>
        Task StopSyncAsync();

        /// <summary>
        /// فرض مزامنة فورية
        /// </summary>
        Task ForceSyncAsync();

        /// <summary>
        /// التحقق من وجود تحديثات جديدة
        /// </summary>
        Task<bool> HasUpdatesAsync();

        /// <summary>
        /// الحصول على آخر وقت مزامنة
        /// </summary>
        DateTime LastSyncTime { get; }

        /// <summary>
        /// حالة المزامنة
        /// </summary>
        bool IsSyncing { get; }

        /// <summary>
        /// حدث عند وجود تحديثات جديدة
        /// </summary>
        event EventHandler<SyncEventArgs>? DataUpdated;

        /// <summary>
        /// حدث عند تغيير حالة المزامنة
        /// </summary>
        event EventHandler<bool>? SyncStatusChanged;
    }

    /// <summary>
    /// معلومات حدث المزامنة
    /// </summary>
    public class SyncEventArgs : EventArgs
    {
        public string TableName { get; set; } = string.Empty;
        public string Action { get; set; } = string.Empty; // INSERT, UPDATE, DELETE
        public int AffectedRows { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }
}
