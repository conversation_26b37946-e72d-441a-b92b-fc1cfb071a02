-- تحسين قاعدة البيانات بإضافة فهارس محسنة للأداء الأفضل
-- يجب تشغيل هذا السكريبت بعد إنشاء قاعدة البيانات

USE NetworkManagementDB;

-- فهار<PERSON> محسنة للأجهزة
CREATE INDEX IF NOT EXISTS idx_devices_status_network ON Devices(Status, NetworkId);
CREATE INDEX IF NOT EXISTS idx_devices_type_location ON Devices(Type, Location);
CREATE INDEX IF NOT EXISTS idx_devices_ip ON Devices(Ip);
CREATE INDEX IF NOT EXISTS idx_devices_updated_at ON Devices(UpdatedAt);
CREATE INDEX IF NOT EXISTS idx_devices_site_network ON Devices(SiteId, NetworkId);

-- فهارس محسنة للمواقع
CREATE INDEX IF NOT EXISTS idx_sites_name_network ON Sites(Name, NetworkId);
CREATE INDEX IF NOT EXISTS idx_sites_gps ON Sites(GpsLat, GpsLng);
CREATE INDEX IF NOT EXISTS idx_sites_updated_at ON Sites(UpdatedAt);

-- فهارس محسنة للمهام
CREATE INDEX IF NOT EXISTS idx_tasks_status_priority ON Tasks(Status, Priority);
CREATE INDEX IF NOT EXISTS idx_tasks_user_status ON Tasks(UserId, Status);
CREATE INDEX IF NOT EXISTS idx_tasks_network_status ON Tasks(NetworkId, Status);
CREATE INDEX IF NOT EXISTS idx_tasks_request_completed ON Tasks(RequestDate, CompletedAt);
CREATE INDEX IF NOT EXISTS idx_tasks_status_date ON Tasks(Status, RequestDate);

-- فهارس محسنة للمشتريات
CREATE INDEX IF NOT EXISTS idx_purchases_date_network ON Purchases(Date, NetworkId);
CREATE INDEX IF NOT EXISTS idx_purchases_category_date ON Purchases(Category, Date);
CREATE INDEX IF NOT EXISTS idx_purchases_supplier ON Purchases(Supplier);
CREATE INDEX IF NOT EXISTS idx_purchases_item_type ON Purchases(ItemType);
CREATE INDEX IF NOT EXISTS idx_purchases_updated_at ON Purchases(UpdatedAt);

-- فهارس محسنة للمخزون
CREATE INDEX IF NOT EXISTS idx_inventory_category_network ON Inventory(Category, NetworkId);
CREATE INDEX IF NOT EXISTS idx_inventory_name_category ON Inventory(Name, Category);
CREATE INDEX IF NOT EXISTS idx_inventory_quantity ON Inventory(Quantity);
CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON Inventory(MinimumStock, Quantity);
CREATE INDEX IF NOT EXISTS idx_inventory_updated_at ON Inventory(UpdatedAt);

-- فهارس محسنة للمستخدمين
CREATE INDEX IF NOT EXISTS idx_users_role_network ON Users(Role, NetworkId);
CREATE INDEX IF NOT EXISTS idx_users_active ON Users(IsActive);
CREATE INDEX IF NOT EXISTS idx_users_updated_at ON Users(UpdatedAt);

-- فهارس محسنة للشبكات
CREATE INDEX IF NOT EXISTS idx_networks_active ON Networks(IsActive);
CREATE INDEX IF NOT EXISTS idx_networks_updated_at ON Networks(UpdatedAt);

-- فهارس مركبة للاستعلامات الشائعة
CREATE INDEX IF NOT EXISTS idx_devices_network_status_type ON Devices(NetworkId, Status, Type);
CREATE INDEX IF NOT EXISTS idx_tasks_network_user_status ON Tasks(NetworkId, UserId, Status);
CREATE INDEX IF NOT EXISTS idx_purchases_network_date_category ON Purchases(NetworkId, Date, Category);
CREATE INDEX IF NOT EXISTS idx_inventory_network_category_stock ON Inventory(NetworkId, Category, Quantity);

-- تحسين جداول MyISAM إلى InnoDB للأداء الأفضل (إذا لم تكن كذلك)
-- ALTER TABLE Devices ENGINE=InnoDB;
-- ALTER TABLE Sites ENGINE=InnoDB;
-- ALTER TABLE Tasks ENGINE=InnoDB;
-- ALTER TABLE Purchases ENGINE=InnoDB;
-- ALTER TABLE Inventory ENGINE=InnoDB;
-- ALTER TABLE Users ENGINE=InnoDB;
-- ALTER TABLE Networks ENGINE=InnoDB;

-- تحليل الجداول لتحسين الفهارس
ANALYZE TABLE Devices;
ANALYZE TABLE Sites;
ANALYZE TABLE Tasks;
ANALYZE TABLE Purchases;
ANALYZE TABLE Inventory;
ANALYZE TABLE Users;
ANALYZE TABLE Networks;

-- إعدادات تحسين MySQL
-- يمكن إضافتها في ملف my.cnf أو my.ini

-- # تحسين الذاكرة
-- innodb_buffer_pool_size = 256M
-- key_buffer_size = 64M
-- query_cache_size = 32M
-- query_cache_limit = 2M

-- # تحسين الاتصالات
-- max_connections = 100
-- thread_cache_size = 8

-- # تحسين الفهارس
-- innodb_log_file_size = 64M
-- innodb_log_buffer_size = 8M

-- عرض معلومات الفهارس للتحقق
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    SEQ_IN_INDEX,
    CARDINALITY
FROM 
    INFORMATION_SCHEMA.STATISTICS 
WHERE 
    TABLE_SCHEMA = 'NetworkManagementDB'
    AND TABLE_NAME IN ('Devices', 'Sites', 'Tasks', 'Purchases', 'Inventory', 'Users', 'Networks')
ORDER BY 
    TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;
