using System;
using System.Threading.Tasks;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;

namespace NetworkManagement.ViewModels
{
    public partial class MapSelectionDialogViewModel : ObservableObject
    {
        [ObservableProperty]
        private double? selectedLatitude;

        [ObservableProperty]
        private double? selectedLongitude;

        [ObservableProperty]
        private string searchQuery = string.Empty;

        [ObservableProperty]
        private bool isMapLoading = true;



        public event EventHandler<(double Latitude, double Longitude)>? LocationSelected;
        public event EventHandler? DialogClosed;

        public MapSelectionDialogViewModel(double? initialLat = null, double? initialLng = null)
        {
            SelectedLatitude = initialLat;
            SelectedLongitude = initialLng;
        }



        [RelayCommand]
        private async Task SearchLocationAsync()
        {
            // TODO: تطبيق البحث عن الموقع باستخدام API خارجي
            await Task.CompletedTask;
        }

        [RelayCommand]
        private void ConfirmSelection()
        {
            if (SelectedLatitude.HasValue && SelectedLongitude.HasValue)
            {
                LocationSelected?.Invoke(this, (SelectedLatitude.Value, SelectedLongitude.Value));
            }
        }

        [RelayCommand]
        private void Cancel()
        {
            DialogClosed?.Invoke(this, EventArgs.Empty);
        }


    }
}
