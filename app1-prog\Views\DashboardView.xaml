<UserControl x:Class="NetworkManagement.Views.DashboardView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft">

    <StackPanel Margin="20">
        <!-- Page Header -->
        <TextBlock Text="لوحة التحكم" Style="{StaticResource PageHeaderStyle}" Margin="0,0,0,20"/>

        <!-- Statistics Cards -->
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Total Devices Card -->
            <materialDesign:Card Grid.Row="0" Grid.Column="0" Style="{StaticResource CardStyle}">
                <StackPanel>
                    <TextBlock Text="إجمالي الأجهزة" Style="{StaticResource SubHeaderTextStyle}" Margin="0,0,0,10"/>
                    <TextBlock Text="{Binding TotalDevices}" Style="{StaticResource StatisticNumberStyle}" Foreground="{StaticResource StatisticsBlueBrush}"/>
                    <TextBlock Text="{Binding ActiveDevices, StringFormat='منها {0} نشط'}" Style="{StaticResource CaptionTextStyle}"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- Total Sites Card -->
            <materialDesign:Card Grid.Row="0" Grid.Column="1" Style="{StaticResource CardStyle}">
                <StackPanel>
                    <TextBlock Text="إجمالي المواقع" Style="{StaticResource SubHeaderTextStyle}" Margin="0,0,0,10"/>
                    <TextBlock Text="{Binding TotalSites}" Style="{StaticResource StatisticNumberStyle}" Foreground="{StaticResource StatisticsGreenBrush}"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- Pending Tasks Card -->
            <materialDesign:Card Grid.Row="1" Grid.Column="0" Style="{StaticResource CardStyle}">
                <StackPanel>
                    <TextBlock Text="المهام المعلقة" Style="{StaticResource SubHeaderTextStyle}" Margin="0,0,0,10"/>
                    <TextBlock Text="{Binding PendingTasks}" Style="{StaticResource StatisticNumberStyle}" Foreground="{StaticResource StatisticsOrangeBrush}"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- Monthly Spending Card -->
            <materialDesign:Card Grid.Row="1" Grid.Column="1" Style="{StaticResource CardStyle}">
                <StackPanel>
                    <TextBlock Text="إنفاق الشهر" Style="{StaticResource SubHeaderTextStyle}" Margin="0,0,0,10"/>
                    <TextBlock Text="{Binding MonthlySpendingDisplay}" Style="{StaticResource StatisticNumberStyle}" Foreground="{StaticResource StatisticsRedBrush}"/>
                </StackPanel>
            </materialDesign:Card>
        </Grid>
    </StackPanel>
</UserControl>
