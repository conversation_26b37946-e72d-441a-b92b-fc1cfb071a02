using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using NetworkManagement.Models;

namespace NetworkManagement.Services
{
    public class InventoryDeductionResult
    {
        public bool Success { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public List<string> DeductedItems { get; set; } = new();
        public List<string> InsufficientItems { get; set; } = new();
    }

    public class InventoryCheckResult
    {
        public bool HasSufficientStock { get; set; }
        public List<string> InsufficientItems { get; set; } = new();
        public Dictionary<string, int> RequiredQuantities { get; set; } = new();
        public Dictionary<string, int> AvailableQuantities { get; set; } = new();
    }

    public interface IDeviceInventoryService
    {
        /// <summary>
        /// التحقق من توفر الكمية المطلوبة في المخزون لجهاز معين
        /// </summary>
        Task<InventoryCheckResult> CheckInventoryAvailabilityAsync(Device device, string? networkId = null);

        /// <summary>
        /// خصم الكميات المطلوبة من المخزون عند إضافة جهاز جديد
        /// </summary>
        Task<InventoryDeductionResult> DeductInventoryForNewDeviceAsync(Device device, string? networkId = null);

        /// <summary>
        /// تحديث المخزون عند تعديل جهاز موجود
        /// </summary>
        Task<InventoryDeductionResult> UpdateInventoryForDeviceModificationAsync(Device oldDevice, Device newDevice, string? networkId = null);

        /// <summary>
        /// إرجاع الكميات إلى المخزون عند حذف جهاز
        /// </summary>
        Task<InventoryDeductionResult> RestoreInventoryForDeletedDeviceAsync(Device device, string? networkId = null);

        /// <summary>
        /// البحث عن عنصر في المخزون بالاسم
        /// </summary>
        Task<Inventory?> FindInventoryItemByNameAsync(string itemName, string? networkId = null);

        /// <summary>
        /// الحصول على قائمة بأنواع المحولات المتاحة في المخزون
        /// </summary>
        Task<List<string>> GetAvailableAdapterTypesAsync(string? networkId = null);

        /// <summary>
        /// الحصول على قائمة بأنواع أسلاك الشبكة المتاحة في المخزون
        /// </summary>
        Task<List<string>> GetAvailableNetworkCableTypesAsync(string? networkId = null);

        /// <summary>
        /// الحصول على قائمة بأنواع أسلاك الكهرباء المتاحة في المخزون
        /// </summary>
        Task<List<string>> GetAvailablePowerCableTypesAsync(string? networkId = null);

        /// <summary>
        /// الحصول على قائمة بأنواع الأجهزة المتاحة في المخزون
        /// </summary>
        Task<List<string>> GetAvailableDeviceTypesAsync(string? networkId = null);
    }
}
