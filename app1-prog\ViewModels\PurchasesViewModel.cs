using System;
using System.Collections.ObjectModel;
using NetworkManagement.Views;
using System.Linq;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using NetworkManagement.Models;
using NetworkManagement.Services;
using NetworkManagement.Helpers;
using Microsoft.Extensions.DependencyInjection;

namespace NetworkManagement.ViewModels
{
    public partial class PurchasesViewModel : ObservableObject, IDisposable
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IAuthService _authService;
        private readonly IReportExportService _exportService;
        private readonly INotificationService _notificationService;

        [ObservableProperty]
        private ObservableCollection<Purchase> purchases = new();

        [ObservableProperty]
        private Purchase? selectedPurchase;

        [ObservableProperty]
        private string searchText = string.Empty;

        [ObservableProperty]
        private string selectedCategory = "الكل";

        [ObservableProperty]
        private DateTime? startDate;

        [ObservableProperty]
        private DateTime? endDate;

        [ObservableProperty]
        private bool isLoading = false;
        partial void OnIsLoadingChanged(bool value)
        {
            OnPropertyChanged(nameof(CanExportPurchases));
            OnPropertyChanged(nameof(CanImportPurchases));
            ExportPurchasesCommand.NotifyCanExecuteChanged();
            ImportPurchasesCommand.NotifyCanExecuteChanged();
        }

        // Statistics
        [ObservableProperty]
        private decimal totalSpending;

        [ObservableProperty]
        private decimal monthlySpending;

        [ObservableProperty]
        private decimal yearlySpending;

        [ObservableProperty]
        private int totalPurchases;

        // Display properties for currency formatting
        public string TotalSpendingDisplay => $"{TotalSpending:N0} ر.ي";
        public string MonthlySpendingDisplay => $"{MonthlySpending:N0} ر.ي";
        public string YearlySpendingDisplay => $"{YearlySpending:N0} ر.ي";

        partial void OnTotalSpendingChanged(decimal value)
        {
            OnPropertyChanged(nameof(TotalSpendingDisplay));
        }

        partial void OnMonthlySpendingChanged(decimal value)
        {
            OnPropertyChanged(nameof(MonthlySpendingDisplay));
        }

        partial void OnYearlySpendingChanged(decimal value)
        {
            OnPropertyChanged(nameof(YearlySpendingDisplay));
        }

        // خصائص فلتر الشبكة
        [ObservableProperty]
        private ObservableCollection<Network> availableNetworks = new();

        [ObservableProperty]
        private Network? selectedNetwork;

        [ObservableProperty]
        private string networkFilter = "الكل";

        // خصائص الصلاحيات
        public bool CanAddPurchases => _authService.CanAddData();
        public bool CanEditPurchases => _authService.CanEditData();
        public bool CanDeletePurchases => _authService.CanDeleteData();
        public bool CanManagePurchases => _authService.CanManagePurchases;
        public bool CanExportPurchases => !IsLoading;
        public bool CanImportPurchases => !IsLoading && CanAddPurchases;
        public bool CanViewAllNetworks => _authService?.CanViewAllNetworks ?? false;

        // Threading control
        private readonly System.Threading.SemaphoreSlim _loadSemaphore = new(1, 1);
        private System.Threading.CancellationTokenSource? _loadCancellationTokenSource;
        private readonly object _loadLock = new();

        public string[] Categories { get; } = { "الكل", "أجهزة", "كابلات", "أدوات", "صيانة", "أخرى" };

        public PurchasesViewModel(IServiceProvider serviceProvider, IAuthService authService, INotificationService notificationService)
        {
            _serviceProvider = serviceProvider;
            _authService = authService;
            _exportService = serviceProvider.GetRequiredService<IReportExportService>();
            _notificationService = notificationService;

            // الاشتراك في تغيير المستخدم لتحديث الصلاحيات
            _authService.UserChanged += OnUserChanged;

            // Set default date range (current month)
            var now = DateTime.Now;
            StartDate = new DateTime(now.Year, now.Month, 1);
            EndDate = StartDate.Value.AddMonths(1).AddDays(-1);

            // تحميل الشبكات
            _ = LoadNetworksAsync();
        }

        private async System.Threading.Tasks.Task LoadNetworksAsync()
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var networkService = scope.ServiceProvider.GetRequiredService<INetworkService>();

                // استخدام NetworkHelper لتحميل الشبكات مع تطبيق الصلاحيات
                var (networks, defaultNetworkId) = await NetworkHelper.LoadNetworksForSelectionAsync(
                    _authService,
                    networkService);

                AvailableNetworks.Clear();

                // إضافة خيار "الكل" للأدمن فقط
                if (_authService.CanViewAllNetworks)
                {
                    AvailableNetworks.Add(new Network { Id = "", Name = "الكل" });
                }

                foreach (var network in networks)
                {
                    AvailableNetworks.Add(network);
                }

                // تعيين الشبكة الافتراضية
                if (!string.IsNullOrEmpty(defaultNetworkId))
                {
                    SelectedNetwork = AvailableNetworks.FirstOrDefault(n => n.Id == defaultNetworkId);
                }
                else if (_authService.CanViewAllNetworks)
                {
                    SelectedNetwork = AvailableNetworks.FirstOrDefault(); // "الكل"
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading networks: {ex.Message}");
            }
        }

        private string? GetEffectiveNetworkFilter()
        {
            // إذا كان المستخدم لا يستطيع رؤية جميع الشبكات، استخدم شبكته فقط
            if (!_authService.CanViewAllNetworks)
            {
                return _authService.CurrentUserNetworkId;
            }

            // إذا كان يستطيع رؤية جميع الشبكات، استخدم الفلتر المختار
            if (SelectedNetwork == null || SelectedNetwork.Id == "" || NetworkFilter == "الكل")
            {
                return null; // عرض جميع الشبكات
            }

            return SelectedNetwork.Id;
        }
        [RelayCommand(CanExecute = nameof(CanExportPurchases))]
        private async System.Threading.Tasks.Task ExportPurchasesAsync()
        {
            try
            {
                IsLoading = true;
                var filter = "Excel Files (*.xlsx)|*.xlsx|CSV Files (*.csv)|*.csv";

                // تحسين اسم الملف ليتضمن معلومات الفلترة
                var networkName = SelectedNetwork?.Name ?? "الكل";
                var categoryName = SelectedCategory != "الكل" ? SelectedCategory : "الكل";
                var defaultFileName = $"المشتريات_{networkName}_{categoryName}_{DateTime.Now:yyyy-MM-dd}";

                var filePath = await _exportService.GetSaveFilePathAsync(defaultFileName, filter);
                if (string.IsNullOrEmpty(filePath))
                    return;

                string result = "";
                var isExcel = filePath.EndsWith(".xlsx", StringComparison.OrdinalIgnoreCase);

                // تصدير المشتريات المعروضة فقط (المفلترة)
                var exportList = Purchases.ToList();
                result = isExcel
                    ? await _exportService.ExportPurchasesReportToExcelAsync(exportList, filePath)
                    : await _exportService.ExportPurchasesReportToCsvAsync(exportList, filePath);

                if (!string.IsNullOrEmpty(result))
                {
                    var filterInfo = networkName != "الكل" ? $" من شبكة {networkName}" : "";
                    if (categoryName != "الكل") filterInfo += $" فئة {categoryName}";

                    MessageBox.Show(
                        $"تم تصدير {exportList.Count} مشترى{filterInfo} بنجاح إلى:\n{result}",
                        "تم التصدير",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"حدث خطأ أثناء تصدير المشتريات:\n{ex.Message}",
                    "خطأ في التصدير",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand(CanExecute = nameof(CanImportPurchases))]
        private async System.Threading.Tasks.Task ImportPurchasesAsync()
        {
            if (!CanAddPurchases)
            {
                PermissionHelper.ShowPermissionDeniedMessage("استيراد", "مشتريات جديدة");
                return;
            }
            try
            {
                IsLoading = true;
                var filter = "Excel Files (*.xlsx)|*.xlsx|CSV Files (*.csv)|*.csv";
                var filePath = await _exportService.GetOpenFilePathAsync(filter);
                if (string.IsNullOrEmpty(filePath))
                    return;

                var confirmMessage = "هل تريد استيراد المشتريات من الملف المحدد؟\n\n" +
                    "⚠️ تحذير:\n" +
                    "• سيتم إضافة المشتريات الجديدة إلى القائمة الحالية\n" +
                    "• تأكد من صحة تنسيق الملف\n" +
                    "• يجب أن تحتوي الأعمدة على: نوع الصنف، الكمية، الوحدة، السعر، التاريخ، المورد، الشبكة، الوصف، رقم الفاتورة، الفئة\n\n" +
                    "هل تريد المتابعة؟";
                if (!MessageHelper.ShowConfirmation(confirmMessage, "تأكيد الاستيراد"))
                    return;

                IEnumerable<Purchase> importedPurchases;
                var isExcel = filePath.EndsWith(".xlsx", StringComparison.OrdinalIgnoreCase);
                if (isExcel)
                    importedPurchases = await _exportService.ImportPurchasesFromExcelAsync(filePath);
                else
                    importedPurchases = await _exportService.ImportPurchasesFromCsvAsync(filePath);

                var purchasesList = importedPurchases.ToList();
                if (!purchasesList.Any())
                {
                    var noDataMessage = "لم يتم العثور على بيانات صالحة في الملف.\n\n" +
                        "تأكد من:\n• وجود بيانات في الملف\n• صحة تنسيق الأعمدة\n• وجود سطر العناوين";
                    MessageHelper.ShowWarningMessage(noDataMessage, "لا توجد بيانات");
                    return;
                }

                // حفظ المشتريات المستوردة
                int successCount = 0;
                int errorCount = 0;
                var errors = new List<string>();
                using var scope = _serviceProvider.CreateScope();
                var purchaseService = scope.ServiceProvider.GetRequiredService<IPurchaseService>();
                foreach (var purchase in purchasesList)
                {
                    try
                    {
                        purchase.Id = Guid.NewGuid().ToString();
                        await purchaseService.CreateAsync(purchase);
                        successCount++;
                    }
                    catch (Exception ex)
                    {
                        errorCount++;
                        var itemName = purchase.ItemType ?? "مشتريات غير محددة";
                        errors.Add($"خطأ في حفظ مشتريات {itemName}: {ex.Message}");
                    }
                }

                // تحديث القائمة
                await LoadPurchasesAsync();
                MessageHelper.ShowOperationResults("استيراد", successCount, errorCount, errors.ToArray());
            }
            catch (Exception ex)
            {
                MessageHelper.HandleException(ex, "استيراد بيانات المشتريات");
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task LoadPurchasesAsync()
        {
            // منع التداخل في العمليات
            if (!await _loadSemaphore.WaitAsync(100))
            {
                System.Diagnostics.Debug.WriteLine("LoadPurchasesAsync: عملية أخرى قيد التنفيذ، تم التجاهل");
                return;
            }

            try
            {
                // إلغاء أي عملية سابقة
                lock (_loadLock)
                {
                    _loadCancellationTokenSource?.Cancel();
                    _loadCancellationTokenSource = new System.Threading.CancellationTokenSource();
                }

                IsLoading = true;
                var cancellationToken = _loadCancellationTokenSource.Token;

                // إنشاء scope جديد للحصول على PurchaseService
                using var scope = _serviceProvider.CreateScope();
                var purchaseService = scope.ServiceProvider.GetRequiredService<IPurchaseService>();

                // تطبيق فلتر الشبكة حسب صلاحيات المستخدم والفلتر المختار
                var networkFilter = GetEffectiveNetworkFilter();

                // استخدام الفلترة المحسنة في قاعدة البيانات
                var purchaseList = await purchaseService.GetFilteredAsync(
                    networkFilter: networkFilter,
                    searchText: string.IsNullOrEmpty(SearchText) ? null : SearchText,
                    category: SelectedCategory,
                    startDate: StartDate,
                    endDate: EndDate);

                // التحقق من الإلغاء
                cancellationToken.ThrowIfCancellationRequested();

                // فلترة إضافية للصلاحيات (للأمان الإضافي)
                if (!_authService.CanViewAllNetworks)
                {
                    purchaseList = purchaseList.Where(purchase => _authService.CanViewData(purchase.NetworkId));
                }

                // التحقق من الإلغاء مرة أخرى
                cancellationToken.ThrowIfCancellationRequested();

                // تحديث UI على الـ UI thread
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    Purchases.Clear();
                    foreach (var purchase in purchaseList.OrderByDescending(p => p.Date))
                    {
                        Purchases.Add(purchase);
                    }
                }, System.Windows.Threading.DispatcherPriority.Normal, cancellationToken);

                await LoadStatisticsAsync(cancellationToken);
            }
            catch (OperationCanceledException)
            {
                System.Diagnostics.Debug.WriteLine("LoadPurchasesAsync: تم إلغاء العملية");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading purchases: {ex.Message}");

                // عرض رسالة الخطأ على UI thread
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    ErrorHandler.HandleException(ex, "تحميل المشتريات", "PurchasesViewModel");
                });
            }
            finally
            {
                IsLoading = false;
                _loadSemaphore.Release();
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task SearchPurchasesAsync()
        {
            await LoadPurchasesAsync();
        }

        [RelayCommand]
        private void AddPurchase()
        {
            // التحقق من صلاحية إضافة المشتريات
            if (!CanAddPurchases)
            {
                PermissionHelper.ShowPermissionDeniedMessage("إضافة", "مشتريات جديدة");
                return;
            }

            var dialogViewModel = App.GetService<PurchaseDialogViewModel>();
            var dialog = new PurchaseDialog(dialogViewModel);

            dialogViewModel.PurchaseSaved += async (s, purchase) =>
            {
                await LoadPurchasesAsync();
            };

            dialog.ShowDialog();
        }

        [RelayCommand]
        private void EditPurchase(Purchase? purchase = null)
        {
            var purchaseToEdit = purchase ?? SelectedPurchase;
            if (purchaseToEdit == null) return;

            // التحقق من صلاحية تعديل المشترى
            if (!PermissionHelper.CanEditItem(_authService, purchaseToEdit.NetworkId))
            {
                PermissionHelper.ShowPermissionDeniedMessage("تعديل", "هذا المشترى");
                return;
            }

            var dialogViewModel = App.GetService<PurchaseDialogViewModel>();
            dialogViewModel.SetEditPurchase(purchaseToEdit);
            var dialog = new PurchaseDialog(dialogViewModel);

            dialogViewModel.PurchaseSaved += async (s, savedPurchase) =>
            {
                await LoadPurchasesAsync();
            };

            dialog.ShowDialog();
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task DeletePurchaseAsync(Purchase? purchase = null)
        {
            var purchaseToDelete = purchase ?? SelectedPurchase;
            if (purchaseToDelete == null) return;

            // التحقق من صلاحية حذف المشترى
            if (!PermissionHelper.CanDeleteItem(_authService, purchaseToDelete.NetworkId))
            {
                PermissionHelper.ShowPermissionDeniedMessage("حذف", "هذا المشترى");
                return;
            }

            var purchaseInfo = $"{purchaseToDelete.ItemType} - {purchaseToDelete.Date:yyyy/MM/dd}";
            var result = System.Windows.MessageBox.Show(
                $"هل أنت متأكد من حذف المشترى:\n{purchaseInfo}؟\n\nلا يمكن التراجع عن هذا الإجراء.",
                "تأكيد الحذف",
                System.Windows.MessageBoxButton.YesNo,
                System.Windows.MessageBoxImage.Warning);

            if (result == System.Windows.MessageBoxResult.Yes)
            {
                try
                {
                    using var scope = _serviceProvider.CreateScope();
                    var purchaseService = scope.ServiceProvider.GetRequiredService<IPurchaseService>();

                    await purchaseService.DeleteAsync(purchaseToDelete.Id);
                    await LoadPurchasesAsync();

                    MessageHelper.ShowSuccessMessage("حذف", "المشترى", $"تم حذف المشترى '{purchaseInfo}' بنجاح");
                }
                catch (Exception ex)
                {
                    ErrorHandler.HandleException(ex, "حذف المشترى", "PurchasesViewModel");
                }
            }
        }

        private async System.Threading.Tasks.Task LoadStatisticsAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                // حساب الإحصائيات من البيانات المعروضة (المفلترة) - متسق مع العرض
                TotalSpending = Purchases.Sum(p => p.TotalPrice);
                TotalPurchases = Purchases.Count;

                // التحقق من الإلغاء
                cancellationToken.ThrowIfCancellationRequested();

                // تحديث الإحصائيات المتقدمة
                await UpdateAdvancedStatisticsAsync(cancellationToken);
            }
            catch (OperationCanceledException)
            {
                // تم إلغاء العملية - لا نحتاج لمعالجة خاصة
                System.Diagnostics.Debug.WriteLine("Statistics loading was cancelled");
            }
            catch (Exception ex)
            {
                ErrorHandler.HandleException(ex, "تحميل الإحصائيات", "PurchasesViewModel", false);
                System.Diagnostics.Debug.WriteLine($"Error loading statistics: {ex.Message}");
            }
        }

        partial void OnSelectedCategoryChanged(string value)
        {
            _ = LoadPurchasesAsync();
        }

        partial void OnStartDateChanged(DateTime? value)
        {
            _ = LoadPurchasesAsync();
        }

        partial void OnEndDateChanged(DateTime? value)
        {
            _ = LoadPurchasesAsync();
        }

        partial void OnSelectedNetworkChanged(Network? value)
        {
            NetworkFilter = value?.Name ?? "الكل";
            _ = LoadPurchasesAsync();
        }

        partial void OnNetworkFilterChanged(string value)
        {
            _ = LoadPurchasesAsync();
        }

        // تحديث الإحصائيات عند تغيير البيانات المعروضة
        partial void OnPurchasesChanged(ObservableCollection<Purchase> value)
        {
            // تحديث الإحصائيات الأساسية فوراً (بدون استدعاء async)
            TotalSpending = Purchases.Sum(p => p.TotalPrice);
            TotalPurchases = Purchases.Count;

            // تحديث الإحصائيات المتقدمة بشكل async
            _ = UpdateAdvancedStatisticsAsync();
        }

        private async System.Threading.Tasks.Task UpdateAdvancedStatisticsAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var purchaseService = scope.ServiceProvider.GetRequiredService<IPurchaseService>();
                var networkFilter = GetEffectiveNetworkFilter();

                // حساب الإحصائيات بطريقة موحدة
                var (monthlySpending, yearlySpending) = await CalculateTimeBasedStatisticsAsync(
                    purchaseService, networkFilter, cancellationToken);

                MonthlySpending = monthlySpending;
                YearlySpending = yearlySpending;
            }
            catch (OperationCanceledException)
            {
                System.Diagnostics.Debug.WriteLine("Advanced statistics update was cancelled");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating advanced statistics: {ex.Message}");
            }
        }

        private async Task<(decimal monthlySpending, decimal yearlySpending)> CalculateTimeBasedStatisticsAsync(
            IPurchaseService purchaseService,
            string? networkFilter,
            CancellationToken cancellationToken = default)
        {
            // Monthly spending
            var monthStart = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
            var monthEnd = monthStart.AddMonths(1).AddDays(-1);
            var monthlyPurchases = await purchaseService.GetFilteredAsync(
                networkFilter: networkFilter,
                searchText: string.IsNullOrEmpty(SearchText) ? null : SearchText,
                category: SelectedCategory,
                startDate: monthStart,
                endDate: monthEnd);

            cancellationToken.ThrowIfCancellationRequested();
            var monthlySpending = monthlyPurchases.Sum(p => p.TotalPrice);

            // Yearly spending
            var yearStart = new DateTime(DateTime.Now.Year, 1, 1);
            var yearEnd = new DateTime(DateTime.Now.Year, 12, 31);
            var yearlyPurchases = await purchaseService.GetFilteredAsync(
                networkFilter: networkFilter,
                searchText: string.IsNullOrEmpty(SearchText) ? null : SearchText,
                category: SelectedCategory,
                startDate: yearStart,
                endDate: yearEnd);

            cancellationToken.ThrowIfCancellationRequested();
            var yearlySpending = yearlyPurchases.Sum(p => p.TotalPrice);

            return (monthlySpending, yearlySpending);
        }

        private async void OnUserChanged(object? sender, User? user)
        {
            try
            {
                // تحديث خصائص الصلاحيات على UI thread
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    OnPropertyChanged(nameof(CanAddPurchases));
                    OnPropertyChanged(nameof(CanEditPurchases));
                    OnPropertyChanged(nameof(CanDeletePurchases));
                    OnPropertyChanged(nameof(CanManagePurchases));
                    OnPropertyChanged(nameof(CanExportPurchases));
                    OnPropertyChanged(nameof(CanImportPurchases));
                    ExportPurchasesCommand.NotifyCanExecuteChanged();
                    ImportPurchasesCommand.NotifyCanExecuteChanged();
                });

                // تأخير قصير لتجنب التحديثات المتكررة
                await System.Threading.Tasks.Task.Delay(100);

                // إعادة تحميل البيانات حسب الصلاحيات الجديدة
                await LoadPurchasesAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in OnUserChanged: {ex.Message}");
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (disposing)
            {
                // إلغاء الاشتراك في تغيير المستخدم
                _authService.UserChanged -= OnUserChanged;

                // إلغاء أي عمليات تحميل جارية
                lock (_loadLock)
                {
                    _loadCancellationTokenSource?.Cancel();
                    _loadCancellationTokenSource?.Dispose();
                }

                // تنظيف SemaphoreSlim
                _loadSemaphore?.Dispose();

                // تنظيف الموارد
                Purchases.Clear();
            }
        }
    }
}
