<UserControl x:Class="NetworkManagement.Views.ReportsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Page Header -->
        <TextBlock Grid.Row="0" Text="التقارير والإحصائيات" Style="{StaticResource PageHeaderStyle}" Margin="15,10,15,5"/>

        <!-- Report Controls - مضغوطة -->
        <materialDesign:Card Grid.Row="1" Margin="15,5,15,10" Padding="15">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- First Row: Quick Actions and Date Range -->
                <Grid Grid.Row="0" Margin="0,0,0,10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Quick Date Filters -->
                    <StackPanel Grid.Column="0" Orientation="Horizontal">
                        <Button Content="اليوم" Command="{Binding SetTodayCommand}" Height="32" Width="60" FontSize="11"
                               Style="{StaticResource MaterialDesignOutlinedButton}" Margin="0,0,5,0"/>
                        <Button Content="الأسبوع" Command="{Binding SetThisWeekCommand}" Height="32" Width="70" FontSize="11"
                               Style="{StaticResource MaterialDesignOutlinedButton}" Margin="0,0,5,0"/>
                        <Button Content="الشهر" Command="{Binding SetThisMonthCommand}" Height="32" Width="60" FontSize="11"
                               Style="{StaticResource MaterialDesignOutlinedButton}" Margin="0,0,5,0"/>
                        <Button Content="السنة" Command="{Binding SetThisYearCommand}" Height="32" Width="60" FontSize="11"
                               Style="{StaticResource MaterialDesignOutlinedButton}" Margin="0,0,15,0"/>
                    </StackPanel>

                    <!-- Date Range -->
                    <DatePicker Grid.Column="1" SelectedDate="{Binding StartDate}" Height="32" Width="120"
                               materialDesign:HintAssist.Hint="من" Margin="0,0,10,0"/>
                    <DatePicker Grid.Column="2" SelectedDate="{Binding EndDate}" Height="32" Width="120"
                               materialDesign:HintAssist.Hint="إلى" Margin="0,0,10,0"/>

                    <!-- Network Filter -->
                    <ComboBox Grid.Column="3" Height="32" Width="140" Margin="0,0,10,0"
                             ItemsSource="{Binding AvailableNetworks}"
                             SelectedItem="{Binding SelectedNetwork}"
                             DisplayMemberPath="Name"
                             materialDesign:HintAssist.Hint="الشبكة"
                             IsEditable="True"
                             Text="{Binding NetworkFilter, UpdateSourceTrigger=PropertyChanged}"
                             FontSize="11"/>

                    <!-- Generate Button -->
                    <Button Grid.Column="4" Content="تحديث" Command="{Binding GenerateReportCommand}" Height="32" Width="80"
                           Style="{StaticResource MaterialDesignRaisedButton}" FontSize="11"/>
                </Grid>

                <!-- Second Row: Report Categories -->
                <Grid Grid.Row="1">
                    <ScrollViewer HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Disabled">
                        <StackPanel Orientation="Horizontal">
                            <!-- تقارير الأجهزة -->
                            <materialDesign:Card Width="180" Height="100" Margin="0,0,10,0" Cursor="Hand"
                                               MouseLeftButtonUp="ReportCard_Click" Tag="تقرير الأجهزة">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                    </Grid.RowDefinitions>
                                    <materialDesign:PackIcon Grid.Row="0" Kind="Router" Width="24" Height="24"
                                                           Foreground="#2196F3" HorizontalAlignment="Center" Margin="0,8,0,5"/>
                                    <StackPanel Grid.Row="1" VerticalAlignment="Center">
                                        <TextBlock Text="تقارير الأجهزة" FontSize="12" FontWeight="Medium"
                                                  HorizontalAlignment="Center"/>
                                        <TextBlock Text="حالة وإحصائيات الأجهزة" FontSize="9"
                                                  Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                  HorizontalAlignment="Center" Margin="5,2,5,8" TextWrapping="Wrap"/>
                                    </StackPanel>
                                </Grid>
                            </materialDesign:Card>

                            <!-- تقارير المالية -->
                            <materialDesign:Card Width="180" Height="100" Margin="0,0,10,0" Cursor="Hand"
                                               MouseLeftButtonUp="ReportCard_Click" Tag="تقرير المشتريات">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                    </Grid.RowDefinitions>
                                    <materialDesign:PackIcon Grid.Row="0" Kind="CurrencyUsd" Width="24" Height="24"
                                                           Foreground="#4CAF50" HorizontalAlignment="Center" Margin="0,8,0,5"/>
                                    <StackPanel Grid.Row="1" VerticalAlignment="Center">
                                        <TextBlock Text="التقارير المالية" FontSize="12" FontWeight="Medium"
                                                  HorizontalAlignment="Center"/>
                                        <TextBlock Text="المشتريات والمصروفات" FontSize="9"
                                                  Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                  HorizontalAlignment="Center" Margin="5,2,5,8" TextWrapping="Wrap"/>
                                    </StackPanel>
                                </Grid>
                            </materialDesign:Card>

                            <!-- تقارير المخزون -->
                            <materialDesign:Card Width="180" Height="100" Margin="0,0,10,0" Cursor="Hand"
                                               MouseLeftButtonUp="ReportCard_Click" Tag="تقرير المخزون">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                    </Grid.RowDefinitions>
                                    <materialDesign:PackIcon Grid.Row="0" Kind="Package" Width="24" Height="24"
                                                           Foreground="#FF9800" HorizontalAlignment="Center" Margin="0,8,0,5"/>
                                    <StackPanel Grid.Row="1" VerticalAlignment="Center">
                                        <TextBlock Text="تقارير المخزون" FontSize="12" FontWeight="Medium"
                                                  HorizontalAlignment="Center"/>
                                        <TextBlock Text="مستويات وحركة المخزون" FontSize="9"
                                                  Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                  HorizontalAlignment="Center" Margin="5,2,5,8" TextWrapping="Wrap"/>
                                    </StackPanel>
                                </Grid>
                            </materialDesign:Card>

                            <!-- تقارير المهام -->
                            <materialDesign:Card Width="180" Height="100" Margin="0,0,10,0" Cursor="Hand"
                                               MouseLeftButtonUp="ReportCard_Click" Tag="تقرير المهام">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                    </Grid.RowDefinitions>
                                    <materialDesign:PackIcon Grid.Row="0" Kind="ClipboardList" Width="24" Height="24"
                                                           Foreground="#9C27B0" HorizontalAlignment="Center" Margin="0,8,0,5"/>
                                    <StackPanel Grid.Row="1" VerticalAlignment="Center">
                                        <TextBlock Text="تقارير المهام" FontSize="12" FontWeight="Medium"
                                                  HorizontalAlignment="Center"/>
                                        <TextBlock Text="إنجاز وأداء المهام" FontSize="9"
                                                  Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                  HorizontalAlignment="Center" Margin="5,2,5,8" TextWrapping="Wrap"/>
                                    </StackPanel>
                                </Grid>
                            </materialDesign:Card>

                            <!-- تقارير المواقع -->
                            <materialDesign:Card Width="180" Height="100" Margin="0,0,10,0" Cursor="Hand"
                                               MouseLeftButtonUp="ReportCard_Click" Tag="تقرير المواقع">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                    </Grid.RowDefinitions>
                                    <materialDesign:PackIcon Grid.Row="0" Kind="MapMarker" Width="24" Height="24"
                                                           Foreground="#F44336" HorizontalAlignment="Center" Margin="0,8,0,5"/>
                                    <StackPanel Grid.Row="1" VerticalAlignment="Center">
                                        <TextBlock Text="تقارير المواقع" FontSize="12" FontWeight="Medium"
                                                  HorizontalAlignment="Center"/>
                                        <TextBlock Text="مواقع وتوزيع الأجهزة" FontSize="9"
                                                  Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                  HorizontalAlignment="Center" Margin="5,2,5,8" TextWrapping="Wrap"/>
                                    </StackPanel>
                                </Grid>
                            </materialDesign:Card>

                            <!-- تقارير المستخدمين -->
                            <materialDesign:Card Width="180" Height="100" Margin="0,0,10,0" Cursor="Hand"
                                               MouseLeftButtonUp="ReportCard_Click" Tag="تقرير المستخدمين">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                    </Grid.RowDefinitions>
                                    <materialDesign:PackIcon Grid.Row="0" Kind="AccountGroup" Width="24" Height="24"
                                                           Foreground="#607D8B" HorizontalAlignment="Center" Margin="0,8,0,5"/>
                                    <StackPanel Grid.Row="1" VerticalAlignment="Center">
                                        <TextBlock Text="تقارير المستخدمين" FontSize="12" FontWeight="Medium"
                                                  HorizontalAlignment="Center"/>
                                        <TextBlock Text="المستخدمين والصلاحيات" FontSize="9"
                                                  Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                  HorizontalAlignment="Center" Margin="5,2,5,8" TextWrapping="Wrap"/>
                                    </StackPanel>
                                </Grid>
                            </materialDesign:Card>

                            <!-- ملخص عام -->
                            <materialDesign:Card Width="180" Height="100" Margin="0,0,0,0" Cursor="Hand"
                                               MouseLeftButtonUp="ReportCard_Click" Tag="ملخص عام">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                    </Grid.RowDefinitions>
                                    <materialDesign:PackIcon Grid.Row="0" Kind="ChartLine" Width="24" Height="24"
                                                           Foreground="#795548" HorizontalAlignment="Center" Margin="0,8,0,5"/>
                                    <StackPanel Grid.Row="1" VerticalAlignment="Center">
                                        <TextBlock Text="الملخص العام" FontSize="12" FontWeight="Medium"
                                                  HorizontalAlignment="Center"/>
                                        <TextBlock Text="إحصائيات شاملة" FontSize="9"
                                                  Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                  HorizontalAlignment="Center" Margin="5,2,5,8" TextWrapping="Wrap"/>
                                    </StackPanel>
                                </Grid>
                            </materialDesign:Card>
                        </StackPanel>
                    </ScrollViewer>
                </Grid>
            </Grid>
        </materialDesign:Card>



        <!-- Report Content Area -->
        <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto" Margin="15,0,15,15">
            <StackPanel>
                <!-- Loading Indicator -->
                <ProgressBar IsIndeterminate="True"
                            Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                            Margin="0,0,0,10" Height="3"/>

                <!-- Compact Statistics Cards - في الأعلى -->
                <materialDesign:Card Margin="0,0,0,15" Padding="10">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- الصف الأول: الأجهزة والمواقع والمستخدمين -->
                        <Grid Grid.Row="0" Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- Devices Card - مصغر -->
                            <Border Grid.Column="0" Background="{DynamicResource MaterialDesignCardBackground}"
                                   CornerRadius="4" Padding="10" Margin="0,0,5,0">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Router" Width="16" Height="16"
                                                           Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                           VerticalAlignment="Center" Margin="0,0,8,0"/>
                                    <StackPanel>
                                        <TextBlock Text="الأجهزة" FontSize="11" FontWeight="Medium"/>
                                        <TextBlock Text="{Binding TotalDevices}" FontSize="16" FontWeight="Bold"
                                                  Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                                    </StackPanel>
                                </StackPanel>
                            </Border>

                            <!-- Sites Card - مصغر -->
                            <Border Grid.Column="1" Background="{DynamicResource MaterialDesignCardBackground}"
                                   CornerRadius="4" Padding="10" Margin="2.5,0,2.5,0">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="MapMarker" Width="16" Height="16"
                                                           Foreground="#4CAF50" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                    <StackPanel>
                                        <TextBlock Text="المواقع" FontSize="11" FontWeight="Medium"/>
                                        <TextBlock Text="{Binding TotalSites}" FontSize="16" FontWeight="Bold"
                                                  Foreground="#4CAF50"/>
                                    </StackPanel>
                                </StackPanel>
                            </Border>

                            <!-- Users Card - مصغر -->
                            <Border Grid.Column="2" Background="{DynamicResource MaterialDesignCardBackground}"
                                   CornerRadius="4" Padding="10" Margin="5,0,0,0">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="AccountGroup" Width="16" Height="16"
                                                           Foreground="#FF9800" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                    <StackPanel>
                                        <TextBlock Text="المستخدمين" FontSize="11" FontWeight="Medium"/>
                                        <TextBlock Text="{Binding TotalUsers}" FontSize="16" FontWeight="Bold"
                                                  Foreground="#FF9800"/>
                                    </StackPanel>
                                </StackPanel>
                            </Border>
                        </Grid>

                        <!-- الصف الثاني: المشتريات والمخزون -->
                        <Grid Grid.Row="1">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- Purchases Card - مصغر -->
                            <Border Grid.Column="0" Background="{DynamicResource MaterialDesignCardBackground}"
                                   CornerRadius="4" Padding="10" Margin="0,0,5,0">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="CurrencyUsd" Width="16" Height="16"
                                                           Foreground="#9C27B0" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                    <StackPanel>
                                        <TextBlock Text="المشتريات" FontSize="11" FontWeight="Medium"/>
                                        <TextBlock Text="{Binding TotalPurchasesDisplay}" FontSize="16" FontWeight="Bold"
                                                  Foreground="#9C27B0"/>
                                    </StackPanel>
                                </StackPanel>
                            </Border>

                            <!-- Inventory Card - مصغر -->
                            <Border Grid.Column="1" Background="{DynamicResource MaterialDesignCardBackground}"
                                   CornerRadius="4" Padding="10" Margin="2.5,0,2.5,0">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Package" Width="16" Height="16"
                                                           Foreground="#607D8B" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                    <StackPanel>
                                        <TextBlock Text="المخزون" FontSize="11" FontWeight="Medium"/>
                                        <TextBlock Text="{Binding TotalInventoryItems}" FontSize="16" FontWeight="Bold"
                                                  Foreground="#607D8B"/>
                                    </StackPanel>
                                </StackPanel>
                            </Border>

                            <!-- Export Actions - في نفس الصف -->
                            <StackPanel Grid.Column="2" Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center"
                                       Visibility="{Binding CanViewReports, Converter={StaticResource BooleanToVisibilityConverter}}">
                                <Button Content="تصدير Excel" Height="28" FontSize="10" Width="80"
                                       Style="{StaticResource MaterialDesignRaisedButton}"
                                       Command="{Binding ExportProfessionalExcelCommand}" Margin="0,0,5,0"/>
                                <Button Content="تصدير PDF" Height="28" FontSize="10" Width="75"
                                       Style="{StaticResource MaterialDesignOutlinedButton}"
                                       Command="{Binding ExportProfessionalPdfCommand}"/>
                            </StackPanel>
                        </Grid>
                    </Grid>
                </materialDesign:Card>

                <!-- Enhanced Report Data Display Area - العرض الكامل -->
                <materialDesign:Card Margin="0,0,0,10">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- Header with Professional Export Options -->
                        <Border Grid.Row="0" Background="{DynamicResource PrimaryHueMidBrush}" Padding="15,10">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0" Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="FileDocument" Width="20" Height="20"
                                                           Foreground="White" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                    <TextBlock Text="بيانات التقرير المفصلة" FontSize="16" FontWeight="Medium"
                                              Foreground="White" VerticalAlignment="Center"/>
                                    <TextBlock Text="{Binding SelectedReportType, StringFormat=' - {0}'}" FontSize="14"
                                              Foreground="White" VerticalAlignment="Center" Margin="5,0,0,0"/>
                                </StackPanel>

                                <StackPanel Grid.Column="1" Orientation="Horizontal"
                                           Visibility="{Binding CanViewReports, Converter={StaticResource BooleanToVisibilityConverter}}">
                                    <Button Content="تصدير CSV" Height="30" FontSize="11" Width="80"
                                           Style="{StaticResource MaterialDesignOutlinedButton}"
                                           Command="{Binding ExportReportCommand}" Margin="0,0,5,0"
                                           Foreground="White" BorderBrush="White"/>
                                    <Button Content="معاينة وطباعة" Height="30" FontSize="11" Width="100"
                                           Style="{StaticResource MaterialDesignOutlinedButton}"
                                           Command="{Binding PreviewReportCommand}"
                                           Foreground="White" BorderBrush="White"/>
                                </StackPanel>
                            </Grid>
                        </Border>

                        <!-- Content Area with Tabs for Different Data Types -->
                        <TabControl Grid.Row="1" Style="{StaticResource MaterialDesignTabControl}" Margin="0">
                            <!-- General Statistics Tab -->
                            <TabItem Header="الإحصائيات العامة">
                                <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="15">
                                    <StackPanel>
                                        <!-- Network Statistics -->
                                        <StackPanel Margin="0,0,0,20">
                                            <TextBlock Text="إحصائيات الشبكات" FontSize="14" FontWeight="Medium" Margin="0,0,0,10"/>
                                            <DataGrid ItemsSource="{Binding NetworkStatistics}" AutoGenerateColumns="False"
                                                     CanUserAddRows="False" CanUserDeleteRows="False" IsReadOnly="True"
                                                     GridLinesVisibility="Horizontal" HeadersVisibility="Column" MinHeight="150">
                                                <DataGrid.Columns>
                                                    <DataGridTextColumn Header="الشبكة" Binding="{Binding NetworkName}" Width="*"/>
                                                    <DataGridTextColumn Header="الأجهزة" Binding="{Binding DeviceCount}" Width="80"/>
                                                    <DataGridTextColumn Header="نشط" Binding="{Binding ActiveCount}" Width="60"/>
                                                    <DataGridTextColumn Header="غير نشط" Binding="{Binding InactiveCount}" Width="80"/>
                                                </DataGrid.Columns>
                                            </DataGrid>
                                        </StackPanel>

                                        <!-- Device Status Distribution -->
                                        <StackPanel Margin="0,0,0,20">
                                            <TextBlock Text="توزيع حالة الأجهزة" FontSize="14" FontWeight="Medium" Margin="0,0,0,10"/>
                                            <DataGrid ItemsSource="{Binding DeviceStatusCounts}" AutoGenerateColumns="False"
                                                     CanUserAddRows="False" CanUserDeleteRows="False" IsReadOnly="True"
                                                     GridLinesVisibility="Horizontal" HeadersVisibility="Column" MinHeight="150">
                                                <DataGrid.Columns>
                                                    <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="*"/>
                                                    <DataGridTextColumn Header="العدد" Binding="{Binding Count}" Width="80"/>
                                                    <DataGridTextColumn Header="النسبة %" Binding="{Binding Percentage}" Width="80"/>
                                                </DataGrid.Columns>
                                            </DataGrid>
                                        </StackPanel>

                                        <!-- Monthly Purchases Trend -->
                                        <StackPanel>
                                            <TextBlock Text="اتجاه المشتريات الشهرية (آخر 12 شهر)" FontSize="14" FontWeight="Medium" Margin="0,0,0,10"/>
                                            <DataGrid ItemsSource="{Binding MonthlyPurchases}" AutoGenerateColumns="False"
                                                     CanUserAddRows="False" CanUserDeleteRows="False" IsReadOnly="True"
                                                     GridLinesVisibility="Horizontal" HeadersVisibility="Column" MinHeight="200">
                                                <DataGrid.Columns>
                                                    <DataGridTextColumn Header="الشهر" Binding="{Binding Month}" Width="120"/>
                                                    <DataGridTextColumn Header="المبلغ (ر.ي)" Binding="{Binding Amount, StringFormat=N0}" Width="150"/>
                                                    <DataGridTextColumn Header="عدد المشتريات" Binding="{Binding Count}" Width="120"/>
                                                </DataGrid.Columns>
                                            </DataGrid>
                                        </StackPanel>
                                    </StackPanel>
                                </ScrollViewer>
                            </TabItem>

                            <!-- Detailed Reports Tab -->
                            <TabItem Header="التقارير التفصيلية">
                                <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="15">
                                    <StackPanel>
                                        <!-- Users Report Data -->
                                        <StackPanel Visibility="{Binding IsUsersReportVisible, Converter={StaticResource BooleanToVisibilityConverter}}"
                                                   Margin="0,0,0,20">
                                            <TextBlock Text="تقرير المستخدمين" FontSize="14" FontWeight="Medium" Margin="0,0,0,10"/>
                                            <DataGrid ItemsSource="{Binding ReportUsers}" AutoGenerateColumns="False"
                                                     CanUserAddRows="False" CanUserDeleteRows="False" IsReadOnly="True"
                                                     GridLinesVisibility="Horizontal" HeadersVisibility="Column" MinHeight="300">
                                                <DataGrid.Columns>
                                                    <DataGridTextColumn Header="اسم المستخدم" Binding="{Binding Username}" Width="120"/>
                                                    <DataGridTextColumn Header="الاسم الكامل" Binding="{Binding Name}" Width="150"/>
                                                    <DataGridTextColumn Header="الدور" Binding="{Binding Role}" Width="100"/>
                                                    <DataGridTextColumn Header="الشبكة" Binding="{Binding Network.Name}" Width="120"/>
                                                    <DataGridTextColumn Header="تاريخ الإنشاء" Binding="{Binding CreatedAt, StringFormat=yyyy/MM/dd}" Width="100"/>
                                                </DataGrid.Columns>
                                            </DataGrid>

                                            <!-- Pagination Controls -->
                                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,15,0,0">
                                                <Button Content="الأولى" Command="{Binding GoToFirstPageCommand}"
                                                       IsEnabled="{Binding CanGoToPreviousPage}" Margin="5,0"/>
                                                <Button Content="السابق" Command="{Binding GoToPreviousPageCommand}"
                                                       IsEnabled="{Binding CanGoToPreviousPage}" Margin="5,0"/>
                                                <TextBlock Text="{Binding PaginationInfo}" VerticalAlignment="Center"
                                                          Margin="15,0" FontWeight="Medium"/>
                                                <Button Content="التالي" Command="{Binding GoToNextPageCommand}"
                                                       IsEnabled="{Binding CanGoToNextPage}" Margin="5,0"/>
                                                <Button Content="الأخيرة" Command="{Binding GoToLastPageCommand}"
                                                       IsEnabled="{Binding CanGoToNextPage}" Margin="5,0"/>
                                            </StackPanel>
                                        </StackPanel>

                                        <!-- Devices Report Data -->
                                        <StackPanel Visibility="{Binding IsDevicesReportVisible, Converter={StaticResource BooleanToVisibilityConverter}}"
                                                   Margin="0,0,0,20">
                                            <TextBlock Text="تقرير الأجهزة" FontSize="16" FontWeight="Medium" Margin="0,0,0,15"
                                                      Foreground="{DynamicResource MaterialDesignBody}"/>
                                            <DataGrid ItemsSource="{Binding ReportDevices}" AutoGenerateColumns="False"
                                                     CanUserAddRows="False" CanUserDeleteRows="False" IsReadOnly="True"
                                                     AlternatingRowBackground="Transparent"
                                                     GridLinesVisibility="Horizontal" HeadersVisibility="Column"
                                                     MinHeight="300" RowHeight="45" ColumnHeaderHeight="40" FontSize="12"
                                                     EnableRowVirtualization="True" EnableColumnVirtualization="True"
                                                     VirtualizingPanel.VirtualizationMode="Recycling"
                                                     VirtualizingPanel.IsVirtualizing="True"
                                                     ScrollViewer.CanContentScroll="True"
                                                     SelectionMode="Single" SelectionUnit="FullRow"
                                                     CanUserSortColumns="True" CanUserReorderColumns="False" CanUserResizeColumns="True">
                                                <DataGrid.Columns>
                                                    <DataGridTextColumn Header="الموقع" Binding="{Binding Location}" Width="180"/>
                                                    <DataGridTextColumn Header="النوع" Binding="{Binding Type}" Width="120"/>
                                                    <DataGridTextColumn Header="الحالة" Binding="{Binding StatusDisplay}" Width="100"/>
                                                    <DataGridTextColumn Header="الشبكة" Binding="{Binding Network.Name}" Width="120"/>
                                                    <DataGridTextColumn Header="المسؤول" Binding="{Binding Responsible}" Width="120"/>
                                                    <DataGridTextColumn Header="تاريخ الإنشاء" Binding="{Binding CreatedAt, StringFormat=yyyy/MM/dd}" Width="120"/>
                                                </DataGrid.Columns>
                                            </DataGrid>

                                            <!-- Pagination Controls -->
                                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,15,0,0">
                                                <Button Content="الأولى" Command="{Binding GoToFirstPageCommand}"
                                                       IsEnabled="{Binding CanGoToPreviousPage}" Margin="5,0"/>
                                                <Button Content="السابق" Command="{Binding GoToPreviousPageCommand}"
                                                       IsEnabled="{Binding CanGoToPreviousPage}" Margin="5,0"/>
                                                <TextBlock Text="{Binding PaginationInfo}" VerticalAlignment="Center"
                                                          Margin="15,0" FontWeight="Medium"/>
                                                <Button Content="التالي" Command="{Binding GoToNextPageCommand}"
                                                       IsEnabled="{Binding CanGoToNextPage}" Margin="5,0"/>
                                                <Button Content="الأخيرة" Command="{Binding GoToLastPageCommand}"
                                                       IsEnabled="{Binding CanGoToNextPage}" Margin="5,0"/>
                                            </StackPanel>
                                        </StackPanel>

                                        <!-- Sites Report Data -->
                                        <StackPanel Visibility="{Binding IsSitesReportVisible, Converter={StaticResource BooleanToVisibilityConverter}}"
                                                   Margin="0,0,0,20">
                                            <TextBlock Text="تقرير المواقع" FontSize="16" FontWeight="Medium" Margin="0,0,0,15"
                                                      Foreground="{DynamicResource MaterialDesignBody}"/>
                                            <DataGrid ItemsSource="{Binding ReportSites}" AutoGenerateColumns="False"
                                                     CanUserAddRows="False" CanUserDeleteRows="False" IsReadOnly="True"
                                                     AlternatingRowBackground="Transparent"
                                                     GridLinesVisibility="Horizontal" HeadersVisibility="Column"
                                                     MinHeight="300" RowHeight="55" ColumnHeaderHeight="50" FontSize="13"
                                                     EnableRowVirtualization="True" EnableColumnVirtualization="True"
                                                     VirtualizingPanel.VirtualizationMode="Recycling"
                                                     VirtualizingPanel.IsVirtualizing="True"
                                                     ScrollViewer.CanContentScroll="True"
                                                     SelectionMode="Extended" SelectionUnit="FullRow"
                                                     CanUserSortColumns="True" CanUserReorderColumns="True" CanUserResizeColumns="True">
                                                <DataGrid.RowStyle>
                                                    <Style TargetType="DataGridRow">
                                                        <Setter Property="Background" Value="Transparent"/>
                                                        <Setter Property="BorderThickness" Value="0"/>
                                                        <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
                                                        <Setter Property="Margin" Value="0,2"/>
                                                    </Style>
                                                </DataGrid.RowStyle>

                                                <DataGrid.Columns>
                                                    <!-- اسم الموقع -->
                                                    <DataGridTemplateColumn Header="اسم الموقع" Width="200" SortMemberPath="Name">
                                                        <DataGridTemplateColumn.CellTemplate>
                                                            <DataTemplate>
                                                                <TextBlock Text="{Binding Name}" FontSize="14" FontWeight="Medium"
                                                                          VerticalAlignment="Center" Margin="8,0"/>
                                                            </DataTemplate>
                                                        </DataGridTemplateColumn.CellTemplate>
                                                    </DataGridTemplateColumn>

                                                    <!-- العنوان -->
                                                    <DataGridTemplateColumn Header="العنوان" Width="250" SortMemberPath="Address">
                                                        <DataGridTemplateColumn.CellTemplate>
                                                            <DataTemplate>
                                                                <TextBlock Text="{Binding Address}" FontSize="14"
                                                                          VerticalAlignment="Center" Margin="8,0"
                                                                          TextWrapping="Wrap"/>
                                                            </DataTemplate>
                                                        </DataGridTemplateColumn.CellTemplate>
                                                    </DataGridTemplateColumn>

                                                    <!-- الهاتف -->
                                                    <DataGridTemplateColumn Header="الهاتف" Width="140" SortMemberPath="Phone">
                                                        <DataGridTemplateColumn.CellTemplate>
                                                            <DataTemplate>
                                                                <TextBlock Text="{Binding Phone}" FontSize="14" FontFamily="Consolas"
                                                                          VerticalAlignment="Center" Margin="8,0"
                                                                          Foreground="{StaticResource StatisticsBlueBrush}"/>
                                                            </DataTemplate>
                                                        </DataGridTemplateColumn.CellTemplate>
                                                    </DataGridTemplateColumn>

                                                    <!-- الشبكة -->
                                                    <DataGridTemplateColumn Header="الشبكة" Width="140" SortMemberPath="Network.Name">
                                                        <DataGridTemplateColumn.CellTemplate>
                                                            <DataTemplate>
                                                                <TextBlock Text="{Binding Network.Name}" FontSize="14"
                                                                          VerticalAlignment="Center" Margin="8,0"/>
                                                            </DataTemplate>
                                                        </DataGridTemplateColumn.CellTemplate>
                                                    </DataGridTemplateColumn>

                                                    <!-- تاريخ الإنشاء -->
                                                    <DataGridTemplateColumn Header="تاريخ الإنشاء" Width="140" SortMemberPath="CreatedAt">
                                                        <DataGridTemplateColumn.CellTemplate>
                                                            <DataTemplate>
                                                                <TextBlock Text="{Binding CreatedAt, StringFormat=yyyy/MM/dd}" FontSize="14"
                                                                          VerticalAlignment="Center" Margin="8,0"/>
                                                            </DataTemplate>
                                                        </DataGridTemplateColumn.CellTemplate>
                                                    </DataGridTemplateColumn>
                                                </DataGrid.Columns>
                                            </DataGrid>

                                            <!-- Pagination Controls -->
                                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,15,0,0">
                                                <Button Content="الأولى" Command="{Binding GoToFirstPageCommand}"
                                                       IsEnabled="{Binding CanGoToPreviousPage}" Margin="5,0"/>
                                                <Button Content="السابق" Command="{Binding GoToPreviousPageCommand}"
                                                       IsEnabled="{Binding CanGoToPreviousPage}" Margin="5,0"/>
                                                <TextBlock Text="{Binding PaginationInfo}" VerticalAlignment="Center"
                                                          Margin="15,0" FontWeight="Medium"/>
                                                <Button Content="التالي" Command="{Binding GoToNextPageCommand}"
                                                       IsEnabled="{Binding CanGoToNextPage}" Margin="5,0"/>
                                                <Button Content="الأخيرة" Command="{Binding GoToLastPageCommand}"
                                                       IsEnabled="{Binding CanGoToNextPage}" Margin="5,0"/>
                                            </StackPanel>
                                        </StackPanel>

                                        <!-- Tasks Report Data -->
                                        <StackPanel Visibility="{Binding IsTasksReportVisible, Converter={StaticResource BooleanToVisibilityConverter}}"
                                                   Margin="0,0,0,20">
                                            <TextBlock Text="تقرير المهام" FontSize="14" FontWeight="Medium" Margin="0,0,0,10"/>
                                            <DataGrid ItemsSource="{Binding ReportTasks}" AutoGenerateColumns="False"
                                                     CanUserAddRows="False" CanUserDeleteRows="False" IsReadOnly="True"
                                                     GridLinesVisibility="Horizontal" HeadersVisibility="Column" MinHeight="300">
                                                <DataGrid.Columns>
                                                    <DataGridTextColumn Header="الوصف" Binding="{Binding Description}" Width="200"/>
                                                    <DataGridTextColumn Header="الحالة" Binding="{Binding StatusDisplay}" Width="100"/>
                                                    <DataGridTextColumn Header="الأولوية" Binding="{Binding PriorityDisplay}" Width="100"/>
                                                    <DataGridTextColumn Header="المستخدم" Binding="{Binding User.Name}" Width="120"/>
                                                    <DataGridTextColumn Header="الشبكة" Binding="{Binding Network.Name}" Width="120"/>
                                                    <DataGridTextColumn Header="تاريخ الطلب" Binding="{Binding RequestDate, StringFormat=yyyy/MM/dd}" Width="100"/>
                                                </DataGrid.Columns>
                                            </DataGrid>

                                            <!-- Pagination Controls -->
                                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,15,0,0">
                                                <Button Content="الأولى" Command="{Binding GoToFirstPageCommand}"
                                                       IsEnabled="{Binding CanGoToPreviousPage}" Margin="5,0"/>
                                                <Button Content="السابق" Command="{Binding GoToPreviousPageCommand}"
                                                       IsEnabled="{Binding CanGoToPreviousPage}" Margin="5,0"/>
                                                <TextBlock Text="{Binding PaginationInfo}" VerticalAlignment="Center"
                                                          Margin="15,0" FontWeight="Medium"/>
                                                <Button Content="التالي" Command="{Binding GoToNextPageCommand}"
                                                       IsEnabled="{Binding CanGoToNextPage}" Margin="5,0"/>
                                                <Button Content="الأخيرة" Command="{Binding GoToLastPageCommand}"
                                                       IsEnabled="{Binding CanGoToNextPage}" Margin="5,0"/>
                                            </StackPanel>
                                        </StackPanel>

                                        <!-- Purchases Report Data -->
                                        <StackPanel Visibility="{Binding IsPurchasesReportVisible, Converter={StaticResource BooleanToVisibilityConverter}}"
                                                   Margin="0,0,0,20">
                                            <TextBlock Text="تقرير المشتريات" FontSize="16" FontWeight="Medium" Margin="0,0,0,15"
                                                      Foreground="{DynamicResource MaterialDesignBody}"/>
                                            <DataGrid ItemsSource="{Binding ReportPurchases}" AutoGenerateColumns="False"
                                                     CanUserAddRows="False" CanUserDeleteRows="False" IsReadOnly="True"
                                                     AlternatingRowBackground="Transparent"
                                                     GridLinesVisibility="Horizontal" HeadersVisibility="Column"
                                                     MinHeight="300" RowHeight="55" ColumnHeaderHeight="50" FontSize="13"
                                                     EnableRowVirtualization="True" EnableColumnVirtualization="True"
                                                     VirtualizingPanel.VirtualizationMode="Recycling"
                                                     VirtualizingPanel.IsVirtualizing="True"
                                                     ScrollViewer.CanContentScroll="True"
                                                     SelectionMode="Extended" SelectionUnit="FullRow"
                                                     CanUserSortColumns="True" CanUserReorderColumns="True" CanUserResizeColumns="True">
                                                <DataGrid.RowStyle>
                                                    <Style TargetType="DataGridRow">
                                                        <Setter Property="Background" Value="Transparent"/>
                                                        <Setter Property="BorderThickness" Value="0"/>
                                                        <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
                                                        <Setter Property="Margin" Value="0,2"/>
                                                    </Style>
                                                </DataGrid.RowStyle>

                                                <DataGrid.Columns>
                                                    <!-- نوع الصنف -->
                                                    <DataGridTemplateColumn Header="نوع الصنف" Width="180" SortMemberPath="ItemType">
                                                        <DataGridTemplateColumn.CellTemplate>
                                                            <DataTemplate>
                                                                <TextBlock Text="{Binding ItemType}" FontSize="14" FontWeight="Medium"
                                                                          VerticalAlignment="Center" Margin="8,0"/>
                                                            </DataTemplate>
                                                        </DataGridTemplateColumn.CellTemplate>
                                                    </DataGridTemplateColumn>

                                                    <!-- الكمية -->
                                                    <DataGridTemplateColumn Header="الكمية" Width="120" SortMemberPath="QuantityDisplay">
                                                        <DataGridTemplateColumn.CellTemplate>
                                                            <DataTemplate>
                                                                <TextBlock Text="{Binding QuantityDisplay}" FontSize="14"
                                                                          VerticalAlignment="Center" Margin="8,0"/>
                                                            </DataTemplate>
                                                        </DataGridTemplateColumn.CellTemplate>
                                                    </DataGridTemplateColumn>

                                                    <!-- السعر -->
                                                    <DataGridTemplateColumn Header="السعر (ر.ي)" Width="140" SortMemberPath="PriceDisplay">
                                                        <DataGridTemplateColumn.CellTemplate>
                                                            <DataTemplate>
                                                                <TextBlock Text="{Binding PriceDisplay}" FontSize="14" FontWeight="Medium"
                                                                          VerticalAlignment="Center" Margin="8,0"
                                                                          Foreground="{StaticResource StatisticsBlueBrush}"/>
                                                            </DataTemplate>
                                                        </DataGridTemplateColumn.CellTemplate>
                                                    </DataGridTemplateColumn>

                                                    <!-- التاريخ -->
                                                    <DataGridTemplateColumn Header="التاريخ" Width="140" SortMemberPath="DateDisplay">
                                                        <DataGridTemplateColumn.CellTemplate>
                                                            <DataTemplate>
                                                                <TextBlock Text="{Binding DateDisplay}" FontSize="14"
                                                                          VerticalAlignment="Center" Margin="8,0"/>
                                                            </DataTemplate>
                                                        </DataGridTemplateColumn.CellTemplate>
                                                    </DataGridTemplateColumn>

                                                    <!-- الشبكة -->
                                                    <DataGridTemplateColumn Header="الشبكة" Width="140" SortMemberPath="NetworkName">
                                                        <DataGridTemplateColumn.CellTemplate>
                                                            <DataTemplate>
                                                                <TextBlock Text="{Binding NetworkName}" FontSize="14"
                                                                          VerticalAlignment="Center" Margin="8,0"/>
                                                            </DataTemplate>
                                                        </DataGridTemplateColumn.CellTemplate>
                                                    </DataGridTemplateColumn>

                                                    <!-- المورد -->
                                                    <DataGridTemplateColumn Header="المورد" Width="140" SortMemberPath="Supplier">
                                                        <DataGridTemplateColumn.CellTemplate>
                                                            <DataTemplate>
                                                                <TextBlock Text="{Binding Supplier}" FontSize="14"
                                                                          VerticalAlignment="Center" Margin="8,0"/>
                                                            </DataTemplate>
                                                        </DataGridTemplateColumn.CellTemplate>
                                                    </DataGridTemplateColumn>
                                                </DataGrid.Columns>
                                            </DataGrid>

                                            <!-- Pagination Controls -->
                                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,15,0,0">
                                                <Button Content="الأولى" Command="{Binding GoToFirstPageCommand}"
                                                       IsEnabled="{Binding CanGoToPreviousPage}" Margin="5,0"/>
                                                <Button Content="السابق" Command="{Binding GoToPreviousPageCommand}"
                                                       IsEnabled="{Binding CanGoToPreviousPage}" Margin="5,0"/>
                                                <TextBlock Text="{Binding PaginationInfo}" VerticalAlignment="Center"
                                                          Margin="15,0" FontWeight="Medium"/>
                                                <Button Content="التالي" Command="{Binding GoToNextPageCommand}"
                                                       IsEnabled="{Binding CanGoToNextPage}" Margin="5,0"/>
                                                <Button Content="الأخيرة" Command="{Binding GoToLastPageCommand}"
                                                       IsEnabled="{Binding CanGoToNextPage}" Margin="5,0"/>
                                            </StackPanel>
                                        </StackPanel>

                                        <!-- Inventory Report Data -->
                                        <StackPanel Visibility="{Binding IsInventoryReportVisible, Converter={StaticResource BooleanToVisibilityConverter}}">
                                            <TextBlock Text="تقرير المخزون" FontSize="14" FontWeight="Medium" Margin="0,0,0,10"/>
                                            <DataGrid ItemsSource="{Binding ReportInventory}" AutoGenerateColumns="False"
                                                     CanUserAddRows="False" CanUserDeleteRows="False" IsReadOnly="True"
                                                     GridLinesVisibility="Horizontal" HeadersVisibility="Column" MinHeight="300">
                                                <DataGrid.Columns>
                                                    <DataGridTextColumn Header="اسم الصنف" Binding="{Binding Name}" Width="150"/>
                                                    <DataGridTextColumn Header="الكمية" Binding="{Binding QuantityDisplay}" Width="80"/>
                                                    <DataGridTextColumn Header="الفئة" Binding="{Binding CategoryDisplay}" Width="120"/>
                                                    <DataGridTextColumn Header="الوحدة" Binding="{Binding Unit}" Width="80"/>
                                                    <DataGridTextColumn Header="الشبكة" Binding="{Binding NetworkName}" Width="120"/>
                                                    <DataGridTextColumn Header="تاريخ الإنشاء" Binding="{Binding CreatedAt, StringFormat=yyyy/MM/dd}" Width="100"/>
                                                </DataGrid.Columns>
                                            </DataGrid>

                                            <!-- Pagination Controls -->
                                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,15,0,0">
                                                <Button Content="الأولى" Command="{Binding GoToFirstPageCommand}"
                                                       IsEnabled="{Binding CanGoToPreviousPage}" Margin="5,0"/>
                                                <Button Content="السابق" Command="{Binding GoToPreviousPageCommand}"
                                                       IsEnabled="{Binding CanGoToPreviousPage}" Margin="5,0"/>
                                                <TextBlock Text="{Binding PaginationInfo}" VerticalAlignment="Center"
                                                          Margin="15,0" FontWeight="Medium"/>
                                                <Button Content="التالي" Command="{Binding GoToNextPageCommand}"
                                                       IsEnabled="{Binding CanGoToNextPage}" Margin="5,0"/>
                                                <Button Content="الأخيرة" Command="{Binding GoToLastPageCommand}"
                                                       IsEnabled="{Binding CanGoToNextPage}" Margin="5,0"/>
                                            </StackPanel>
                                        </StackPanel>
                                    </StackPanel>
                                </ScrollViewer>
                            </TabItem>
                        </TabControl>
                    </Grid>
                </materialDesign:Card>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</UserControl>
