<Window x:Class="NetworkManagement.Views.SiteDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="{Binding Title}"
        Height="550" Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft"
        Background="{DynamicResource MaterialDesignPaper}"
        Foreground="{DynamicResource MaterialDesignBody}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Main Content -->
        <ScrollViewer Grid.Row="0" VerticalScrollBarVisibility="Auto" Margin="20">
            <StackPanel>
                <!-- Site Name -->
                <TextBox materialDesign:HintAssist.Hint="اسم الموقع *"
                         materialDesign:HintAssist.IsFloating="True"
                         Text="{Binding Name, UpdateSourceTrigger=PropertyChanged}"
                         Margin="0,0,0,20"/>

                <!-- Address -->
                <TextBox materialDesign:HintAssist.Hint="العنوان"
                         materialDesign:HintAssist.IsFloating="True"
                         Text="{Binding Address, UpdateSourceTrigger=PropertyChanged}"
                         Margin="0,0,0,20"/>

                <!-- Phone -->
                <TextBox materialDesign:HintAssist.Hint="رقم الهاتف"
                         materialDesign:HintAssist.IsFloating="True"
                         Text="{Binding Phone, UpdateSourceTrigger=PropertyChanged}"
                         Margin="0,0,0,20"/>

                <!-- GPS Coordinates -->
                <Grid Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBox Grid.Column="0"
                             materialDesign:HintAssist.Hint="خط العرض (GPS)"
                             materialDesign:HintAssist.IsFloating="True"
                             Text="{Binding GpsLat, UpdateSourceTrigger=PropertyChanged}"
                             Margin="0,0,5,0"/>

                    <TextBox Grid.Column="1"
                             materialDesign:HintAssist.Hint="خط الطول (GPS)"
                             materialDesign:HintAssist.IsFloating="True"
                             Text="{Binding GpsLng, UpdateSourceTrigger=PropertyChanged}"
                             Margin="5,0,5,0"/>

                    <Button Grid.Column="2"
                            Content="📍"
                            ToolTip="اختيار من الخريطة"
                            Command="{Binding SelectFromMapCommand}"
                            Style="{StaticResource MaterialDesignIconButton}"
                            Margin="5,0,0,0"/>
                </Grid>

                <!-- Power Source -->
                <TextBox materialDesign:HintAssist.Hint="مصدر الطاقة"
                         materialDesign:HintAssist.IsFloating="True"
                         Text="{Binding PowerSource, UpdateSourceTrigger=PropertyChanged}"
                         Margin="0,0,0,20"/>

                <!-- نظام البطاريات -->
                <Grid Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <ComboBox Grid.Column="0"
                              materialDesign:HintAssist.Hint="نوع البطارية"
                              materialDesign:HintAssist.IsFloating="True"
                              ItemsSource="{Binding BatteryTypes}"
                              Text="{Binding BatteryType, UpdateSourceTrigger=PropertyChanged}"
                              IsEditable="True"
                              IsTextSearchEnabled="True"
                              StaysOpenOnEdit="True"
                              Margin="0,0,10,0">
                        <ComboBox.ToolTip>
                            <ToolTip Content="يمكنك كتابة نوع بطارية جديد أو اختيار من المخزون المتاح"/>
                        </ComboBox.ToolTip>
                    </ComboBox>

                    <TextBox Grid.Column="1"
                             materialDesign:HintAssist.Hint="حجم البطارية"
                             materialDesign:HintAssist.IsFloating="True"
                             Text="{Binding BatterySize, UpdateSourceTrigger=PropertyChanged}"
                             Margin="10,0,0,0"/>
                </Grid>

                <!-- نظام القواعد -->
                <Grid Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <ComboBox Grid.Column="0"
                              materialDesign:HintAssist.Hint="نوع القواعد المستخدمة"
                              materialDesign:HintAssist.IsFloating="True"
                              ItemsSource="{Binding BaseTypes}"
                              Text="{Binding BaseType, UpdateSourceTrigger=PropertyChanged}"
                              IsEditable="True"
                              IsTextSearchEnabled="True"
                              StaysOpenOnEdit="True"
                              Margin="0,0,10,0">
                        <ComboBox.ToolTip>
                            <ToolTip Content="يمكنك كتابة نوع قاعدة جديد أو اختيار من المخزون المتاح"/>
                        </ComboBox.ToolTip>
                    </ComboBox>

                    <TextBox Grid.Column="1"
                             materialDesign:HintAssist.Hint="عدد القواعد"
                             materialDesign:HintAssist.IsFloating="True"
                             Text="{Binding BaseCount, UpdateSourceTrigger=PropertyChanged}"
                             Margin="10,0,0,0"/>
                </Grid>

                <!-- نظام الصناديق -->
                <Grid Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <ComboBox Grid.Column="0"
                              materialDesign:HintAssist.Hint="نوع الصناديق"
                              materialDesign:HintAssist.IsFloating="True"
                              ItemsSource="{Binding BoxTypes}"
                              Text="{Binding BoxType, UpdateSourceTrigger=PropertyChanged}"
                              IsEditable="True"
                              IsTextSearchEnabled="True"
                              StaysOpenOnEdit="True"
                              Margin="0,0,10,0">
                        <ComboBox.ToolTip>
                            <ToolTip Content="يمكنك كتابة نوع صندوق جديد أو اختيار من المخزون المتاح"/>
                        </ComboBox.ToolTip>
                    </ComboBox>

                    <TextBox Grid.Column="1"
                             materialDesign:HintAssist.Hint="عدد الصناديق"
                             materialDesign:HintAssist.IsFloating="True"
                             Text="{Binding BoxCount, UpdateSourceTrigger=PropertyChanged}"
                             Margin="10,0,0,0"/>
                </Grid>

                <!-- نظام أسلاك الشبكة -->
                <Grid Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <ComboBox Grid.Column="0"
                              materialDesign:HintAssist.Hint="نوع سلك الشبكة"
                              materialDesign:HintAssist.IsFloating="True"
                              ItemsSource="{Binding NetworkCableTypes}"
                              Text="{Binding NetworkCableType, UpdateSourceTrigger=PropertyChanged}"
                              IsEditable="True"
                              IsTextSearchEnabled="True"
                              StaysOpenOnEdit="True"
                              Margin="0,0,10,0">
                        <ComboBox.ToolTip>
                            <ToolTip Content="يمكنك كتابة نوع سلك جديد أو اختيار من المخزون المتاح"/>
                        </ComboBox.ToolTip>
                    </ComboBox>

                    <TextBox Grid.Column="1"
                             materialDesign:HintAssist.Hint="طول سلك الشبكة (متر)"
                             materialDesign:HintAssist.IsFloating="True"
                             Text="{Binding NetworkCableLength, UpdateSourceTrigger=PropertyChanged}"
                             Margin="10,0,0,0"/>
                </Grid>

                <!-- نوع وطول سلك الكهرباء -->
                <Grid Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <ComboBox Grid.Column="0"
                              materialDesign:HintAssist.Hint="نوع سلك الكهرباء"
                              materialDesign:HintAssist.IsFloating="True"
                              ItemsSource="{Binding PowerCableTypes}"
                              Text="{Binding PowerCableType, UpdateSourceTrigger=PropertyChanged}"
                              IsEditable="True"
                              IsTextSearchEnabled="True"
                              StaysOpenOnEdit="True"
                              Margin="0,0,10,0">
                        <ComboBox.ToolTip>
                            <ToolTip Content="يمكنك كتابة نوع سلك جديد أو اختيار من المخزون المتاح"/>
                        </ComboBox.ToolTip>
                    </ComboBox>

                    <TextBox Grid.Column="1"
                             materialDesign:HintAssist.Hint="طول سلك الكهرباء (متر)"
                             materialDesign:HintAssist.IsFloating="True"
                             Text="{Binding PowerCableLength, UpdateSourceTrigger=PropertyChanged}"
                             Margin="10,0,0,0"/>
                </Grid>

                <!-- أنواع الأجهزة الموجودة في الموقع -->
                <TextBox materialDesign:HintAssist.Hint="أنواع الأجهزة الموجودة في الموقع"
                         materialDesign:HintAssist.IsFloating="True"
                         Text="{Binding DeviceTypesText, UpdateSourceTrigger=PropertyChanged}"
                         Margin="0,0,0,20">
                    <TextBox.ToolTip>
                        <ToolTip Content="اكتب أنواع الأجهزة مفصولة بفاصلة (مثال: Router, Switch, Access Point)"/>
                    </TextBox.ToolTip>
                </TextBox>

                <!-- عناصر إضافية من المخزون -->
                <Expander Header="عناصر إضافية من المخزون"
                          Margin="0,0,0,20"
                          IsExpanded="False">
                    <StackPanel Margin="10">
                        <Button Content="إضافة عنصر من المخزون"
                                Command="{Binding AddInventoryItemCommand}"
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                Margin="0,0,0,10"/>

                        <ItemsControl ItemsSource="{Binding AdditionalInventoryItems}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <materialDesign:Card Margin="0,5" Padding="10">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="3*"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <TextBlock Grid.Column="0"
                                                       Text="{Binding Name}"
                                                       VerticalAlignment="Center"
                                                       FontWeight="Medium"
                                                       TextWrapping="Wrap"/>

                                            <TextBox Grid.Column="1"
                                                     Text="{Binding Quantity, UpdateSourceTrigger=PropertyChanged}"
                                                     materialDesign:HintAssist.Hint="الكمية"
                                                     Margin="10,0"
                                                     VerticalAlignment="Center"
                                                     MinWidth="80"/>

                                            <Button Grid.Column="2"
                                                    Content="🗑️"
                                                    Command="{Binding DataContext.RemoveInventoryItemCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                                                    CommandParameter="{Binding}"
                                                    Style="{StaticResource MaterialDesignIconButton}"
                                                    ToolTip="حذف العنصر"/>
                                        </Grid>
                                    </materialDesign:Card>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>

                        <!-- رسالة عدم وجود عناصر -->
                        <TextBlock Text="لا توجد عناصر إضافية مضافة"
                                   HorizontalAlignment="Center"
                                   Margin="0,20"
                                   Foreground="{DynamicResource MaterialDesignBodyLight}"
                                   Visibility="{Binding AdditionalInventoryItems.Count, Converter={StaticResource ZeroToVisibilityConverter}}"/>
                    </StackPanel>
                </Expander>

                <!-- Network -->
                <ComboBox materialDesign:HintAssist.Hint="الشبكة (اختياري)"
                         materialDesign:HintAssist.IsFloating="True"
                         ItemsSource="{Binding AvailableNetworks}"
                         SelectedValuePath="Id"
                         DisplayMemberPath="Name"
                         SelectedValue="{Binding SelectedNetworkId}"
                         Margin="0,0,0,20"/>

                <!-- Error Message -->
                <TextBlock Text="{Binding ErrorMessage}"
                           Foreground="{DynamicResource ValidationErrorBrush}"
                           FontWeight="Medium"
                           Margin="0,0,0,10"
                           Visibility="{Binding ErrorMessage, Converter={StaticResource StringToVisibilityConverter}}"/>

                <!-- Required Fields Note -->
                <TextBlock Text="* الحقول المطلوبة"
                           FontSize="12"
                           Foreground="{DynamicResource MaterialDesignBodyLight}"
                           Margin="0,10,0,0"/>
            </StackPanel>
        </ScrollViewer>

        <!-- Action Buttons -->
        <Border Grid.Row="1"
                Background="{DynamicResource MaterialDesignPaper}"
                BorderBrush="{DynamicResource MaterialDesignDivider}"
                BorderThickness="0,1,0,0"
                Padding="20">

            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Loading Indicator -->
                <ProgressBar Grid.Column="0"
                             IsIndeterminate="True"
                             Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                             VerticalAlignment="Center"
                             Height="4"
                             Margin="0,0,20,0"/>

                <!-- Cancel Button -->
                <Button Grid.Column="1"
                        Content="إلغاء"
                        Command="{Binding CancelCommand}"
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        Margin="0,0,10,0"
                        MinWidth="80"/>

                <!-- Save Button -->
                <Button Grid.Column="2"
                        Content="{Binding IsEditMode, Converter={StaticResource BooleanToStringConverter}, ConverterParameter='تحديث|حفظ'}"
                        Command="{Binding SaveCommand}"
                        Style="{StaticResource MaterialDesignRaisedButton}"
                        IsEnabled="{Binding IsLoading, Converter={StaticResource InverseBooleanConverter}}"
                        MinWidth="80"/>
            </Grid>
        </Border>
    </Grid>
</Window>
