using System;
using System.Collections.Generic;
using System.Linq;
using NetworkManagement.Models;
using NetworkManagement.Services;

namespace NetworkManagement.Helpers
{
    /// <summary>
    /// فئة الثوابت الخاصة بالمهام
    /// </summary>
    public static class TaskConstants
    {
        // حالات المهام
        public static class Status
        {
            public const string Pending = "pending";
            public const string InProgress = "in-progress";
            public const string Completed = "completed";
            public const string Cancelled = "cancelled";
        }

        // أولويات المهام
        public static class Priority
        {
            public const string Low = "low";
            public const string Medium = "medium";
            public const string High = "high";
            public const string Urgent = "urgent";
        }

        // عرض الحالات بالعربية
        public static class StatusDisplay
        {
            public const string Pending = "معلق";
            public const string InProgress = "قيد التنفيذ";
            public const string Completed = "مكتمل";
            public const string Cancelled = "ملغي";
            public const string All = "الكل";
        }

        // عرض الأولويات بالعربية
        public static class PriorityDisplay
        {
            public const string Low = "منخفضة";
            public const string Medium = "متوسطة";
            public const string High = "عالية";
            public const string Urgent = "عاجلة";
            public const string All = "الكل";
        }

        // ألوان موحدة للحالات والأولويات
        public static class Colors
        {
            // ألوان الحالات
            public const string Pending = "#FF9800";
            public const string InProgress = "#2196F3";
            public const string Completed = "#4CAF50";
            public const string Cancelled = "#F44336";

            // ألوان الأولويات
            public const string Low = "#4CAF50";
            public const string Medium = "#2196F3";
            public const string High = "#FF9800";
            public const string Urgent = "#F44336";

            public const string Default = "#9E9E9E";
        }

        // خيارات الفلترة
        public static readonly string[] StatusFilterOptions = 
        {
            StatusDisplay.All,
            StatusDisplay.Pending,
            StatusDisplay.InProgress,
            StatusDisplay.Completed,
            StatusDisplay.Cancelled
        };

        public static readonly string[] PriorityFilterOptions = 
        {
            PriorityDisplay.All,
            PriorityDisplay.Low,
            PriorityDisplay.Medium,
            PriorityDisplay.High,
            PriorityDisplay.Urgent
        };

        // خيارات الحالة للحوار
        public static readonly string[] StatusOptions = 
        {
            StatusDisplay.Pending,
            StatusDisplay.InProgress,
            StatusDisplay.Completed,
            StatusDisplay.Cancelled
        };

        // خيارات الأولوية للحوار
        public static readonly string[] PriorityOptions = 
        {
            PriorityDisplay.Low,
            PriorityDisplay.Medium,
            PriorityDisplay.High,
            PriorityDisplay.Urgent
        };
    }

    /// <summary>
    /// فئة المساعدات الخاصة بالمهام
    /// </summary>
    public static class TaskHelper
    {
        /// <summary>
        /// تحويل عرض الحالة إلى مفتاح الحالة
        /// </summary>
        /// <param name="statusDisplay">عرض الحالة بالعربية</param>
        /// <returns>مفتاح الحالة بالإنجليزية</returns>
        public static string GetStatusKey(string? statusDisplay)
        {
            return statusDisplay switch
            {
                TaskConstants.StatusDisplay.Pending => TaskConstants.Status.Pending,
                TaskConstants.StatusDisplay.InProgress => TaskConstants.Status.InProgress,
                TaskConstants.StatusDisplay.Completed => TaskConstants.Status.Completed,
                TaskConstants.StatusDisplay.Cancelled => TaskConstants.Status.Cancelled,
                _ => TaskConstants.Status.Pending
            };
        }

        /// <summary>
        /// تحويل مفتاح الحالة إلى عرض الحالة
        /// </summary>
        /// <param name="statusKey">مفتاح الحالة بالإنجليزية</param>
        /// <returns>عرض الحالة بالعربية</returns>
        public static string GetStatusDisplay(string? statusKey)
        {
            return statusKey switch
            {
                TaskConstants.Status.Pending => TaskConstants.StatusDisplay.Pending,
                TaskConstants.Status.InProgress => TaskConstants.StatusDisplay.InProgress,
                TaskConstants.Status.Completed => TaskConstants.StatusDisplay.Completed,
                TaskConstants.Status.Cancelled => TaskConstants.StatusDisplay.Cancelled,
                _ => TaskConstants.StatusDisplay.Pending
            };
        }

        /// <summary>
        /// تحويل عرض الأولوية إلى مفتاح الأولوية
        /// </summary>
        /// <param name="priorityDisplay">عرض الأولوية بالعربية</param>
        /// <returns>مفتاح الأولوية بالإنجليزية</returns>
        public static string GetPriorityKey(string? priorityDisplay)
        {
            return priorityDisplay switch
            {
                TaskConstants.PriorityDisplay.Low => TaskConstants.Priority.Low,
                TaskConstants.PriorityDisplay.Medium => TaskConstants.Priority.Medium,
                TaskConstants.PriorityDisplay.High => TaskConstants.Priority.High,
                TaskConstants.PriorityDisplay.Urgent => TaskConstants.Priority.Urgent,
                _ => TaskConstants.Priority.Medium
            };
        }

        /// <summary>
        /// تحويل مفتاح الأولوية إلى عرض الأولوية
        /// </summary>
        /// <param name="priorityKey">مفتاح الأولوية بالإنجليزية</param>
        /// <returns>عرض الأولوية بالعربية</returns>
        public static string GetPriorityDisplay(string? priorityKey)
        {
            return priorityKey switch
            {
                TaskConstants.Priority.Low => TaskConstants.PriorityDisplay.Low,
                TaskConstants.Priority.Medium => TaskConstants.PriorityDisplay.Medium,
                TaskConstants.Priority.High => TaskConstants.Priority.High,
                TaskConstants.Priority.Urgent => TaskConstants.PriorityDisplay.Urgent,
                _ => TaskConstants.PriorityDisplay.Medium
            };
        }

        /// <summary>
        /// الحصول على لون الحالة
        /// </summary>
        /// <param name="statusKey">مفتاح الحالة</param>
        /// <returns>لون الحالة</returns>
        public static string GetStatusColor(string? statusKey)
        {
            return statusKey switch
            {
                TaskConstants.Status.Pending => TaskConstants.Colors.Pending,
                TaskConstants.Status.InProgress => TaskConstants.Colors.InProgress,
                TaskConstants.Status.Completed => TaskConstants.Colors.Completed,
                TaskConstants.Status.Cancelled => TaskConstants.Colors.Cancelled,
                _ => TaskConstants.Colors.Default
            };
        }

        /// <summary>
        /// الحصول على لون الأولوية
        /// </summary>
        /// <param name="priorityKey">مفتاح الأولوية</param>
        /// <returns>لون الأولوية</returns>
        public static string GetPriorityColor(string? priorityKey)
        {
            return priorityKey switch
            {
                TaskConstants.Priority.Low => TaskConstants.Colors.Low,
                TaskConstants.Priority.Medium => TaskConstants.Colors.Medium,
                TaskConstants.Priority.High => TaskConstants.Colors.High,
                TaskConstants.Priority.Urgent => TaskConstants.Colors.Urgent,
                _ => TaskConstants.Colors.Default
            };
        }

        /// <summary>
        /// التحقق من صحة حالة المهمة
        /// </summary>
        /// <param name="status">حالة المهمة</param>
        /// <returns>true إذا كانت الحالة صحيحة</returns>
        public static bool IsValidStatus(string? status)
        {
            return status switch
            {
                TaskConstants.Status.Pending or
                TaskConstants.Status.InProgress or
                TaskConstants.Status.Completed or
                TaskConstants.Status.Cancelled => true,
                _ => false
            };
        }

        /// <summary>
        /// التحقق من صحة أولوية المهمة
        /// </summary>
        /// <param name="priority">أولوية المهمة</param>
        /// <returns>true إذا كانت الأولوية صحيحة</returns>
        public static bool IsValidPriority(string? priority)
        {
            return priority switch
            {
                TaskConstants.Priority.Low or
                TaskConstants.Priority.Medium or
                TaskConstants.Priority.High or
                TaskConstants.Priority.Urgent => true,
                _ => false
            };
        }

        /// <summary>
        /// التحقق من إمكانية إكمال المهمة
        /// </summary>
        /// <param name="task">المهمة</param>
        /// <returns>true إذا كان يمكن إكمال المهمة</returns>
        public static bool CanCompleteTask(Models.Task? task)
        {
            return task != null &&
                   task.Status != TaskConstants.Status.Completed &&
                   task.Status != TaskConstants.Status.Cancelled;
        }

        /// <summary>
        /// التحقق من تأخر المهمة
        /// </summary>
        /// <param name="task">المهمة</param>
        /// <returns>true إذا كانت المهمة متأخرة</returns>
        public static bool IsTaskOverdue(Models.Task? task)
        {
            // المهام المتأخرة: المهام التي مر على طلبها أكثر من أسبوع ولم تكتمل بعد
            return task != null &&
                   task.RequestDate < DateTime.Now.AddDays(-7) &&
                   task.Status != TaskConstants.Status.Completed;
        }

        /// <summary>
        /// التحقق من اكتمال المهمة
        /// </summary>
        /// <param name="task">المهمة</param>
        /// <returns>true إذا كانت المهمة مكتملة</returns>
        public static bool IsTaskCompleted(Models.Task? task)
        {
            return task?.Status == TaskConstants.Status.Completed;
        }

        /// <summary>
        /// إنشاء مهمة جديدة بالقيم الافتراضية
        /// </summary>
        /// <param name="description">وصف المهمة</param>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="networkId">معرف الشبكة (اختياري)</param>
        /// <returns>مهمة جديدة</returns>
        public static Models.Task CreateNewTask(string description, string userId, string? networkId = null)
        {
            return new Models.Task
            {
                Id = Guid.NewGuid().ToString(),
                Description = description?.Trim() ?? string.Empty,
                UserId = userId,
                NetworkId = networkId,
                Status = TaskConstants.Status.Pending,
                Priority = TaskConstants.Priority.Medium,
                RequestDate = DateTime.Now
            };
        }

        /// <summary>
        /// تطبيق فلتر الحالة على المهام
        /// </summary>
        /// <param name="tasks">قائمة المهام</param>
        /// <param name="statusFilter">فلتر الحالة</param>
        /// <returns>المهام المفلترة</returns>
        public static IEnumerable<Models.Task> ApplyStatusFilter(IEnumerable<Models.Task> tasks, string? statusFilter)
        {
            if (string.IsNullOrEmpty(statusFilter) || statusFilter == TaskConstants.StatusDisplay.All)
                return tasks;

            var statusKey = GetStatusKey(statusFilter);
            return tasks.Where(t => t.Status == statusKey);
        }

        /// <summary>
        /// تطبيق فلتر الأولوية على المهام
        /// </summary>
        /// <param name="tasks">قائمة المهام</param>
        /// <param name="priorityFilter">فلتر الأولوية</param>
        /// <returns>المهام المفلترة</returns>
        public static IEnumerable<Models.Task> ApplyPriorityFilter(IEnumerable<Models.Task> tasks, string? priorityFilter)
        {
            if (string.IsNullOrEmpty(priorityFilter) || priorityFilter == TaskConstants.PriorityDisplay.All)
                return tasks;

            var priorityKey = GetPriorityKey(priorityFilter);
            return tasks.Where(t => t.Priority == priorityKey);
        }

        /// <summary>
        /// تطبيق فلتر البحث النصي على المهام
        /// </summary>
        /// <param name="tasks">قائمة المهام</param>
        /// <param name="searchText">نص البحث</param>
        /// <returns>المهام المفلترة</returns>
        public static IEnumerable<Models.Task> ApplySearchFilter(IEnumerable<Models.Task> tasks, string? searchText)
        {
            if (string.IsNullOrWhiteSpace(searchText))
                return tasks;

            var search = searchText.Trim().ToLower();
            return tasks.Where(t =>
                (t.Description?.ToLower().Contains(search) == true) ||
                (t.Notes?.ToLower().Contains(search) == true) ||
                (t.User?.Name?.ToLower().Contains(search) == true));
        }

        /// <summary>
        /// التحقق من صحة بيانات المهمة
        /// </summary>
        /// <param name="task">المهمة المراد التحقق منها</param>
        /// <returns>قائمة بالأخطاء، فارغة إذا كانت البيانات صحيحة</returns>
        public static List<string> ValidateTask(Models.Task? task)
        {
            var errors = new List<string>();

            if (task == null)
            {
                errors.Add("المهمة غير موجودة");
                return errors;
            }

            // التحقق من الوصف
            if (string.IsNullOrWhiteSpace(task.Description))
            {
                errors.Add("وصف المهمة مطلوب");
            }
            else if (task.Description.Length > 500)
            {
                errors.Add("وصف المهمة يجب أن يكون أقل من 500 حرف");
            }

            // التحقق من الحالة
            if (!IsValidStatus(task.Status))
            {
                errors.Add("حالة المهمة غير صحيحة");
            }

            // التحقق من الأولوية
            if (!IsValidPriority(task.Priority))
            {
                errors.Add("أولوية المهمة غير صحيحة");
            }

            // التحقق من الملاحظات
            if (!string.IsNullOrEmpty(task.Notes) && task.Notes.Length > 1000)
            {
                errors.Add("الملاحظات يجب أن تكون أقل من 1000 حرف");
            }

            // التحقق من معرف المستخدم
            if (string.IsNullOrWhiteSpace(task.UserId))
            {
                errors.Add("معرف المستخدم مطلوب");
            }

            // التحقق من تاريخ الطلب
            if (task.RequestDate > DateTime.Now)
            {
                errors.Add("تاريخ طلب المهمة لا يمكن أن يكون في المستقبل");
            }

            // التحقق من تاريخ الإكمال
            if (task.CompletedAt.HasValue && task.CompletedAt.Value < task.RequestDate)
            {
                errors.Add("تاريخ الإكمال لا يمكن أن يكون قبل تاريخ الطلب");
            }

            return errors;
        }

        /// <summary>
        /// تنظيف وتصحيح بيانات المهمة
        /// </summary>
        /// <param name="task">المهمة المراد تنظيفها</param>
        /// <returns>المهمة بعد التنظيف</returns>
        public static Models.Task? CleanupTask(Models.Task? task)
        {
            if (task == null) return null;

            // تنظيف النصوص
            task.Description = task.Description?.Trim() ?? string.Empty;
            task.Notes = task.Notes?.Trim();

            // قص النصوص الطويلة
            if (task.Description.Length > 500)
                task.Description = task.Description[..500];

            if (!string.IsNullOrEmpty(task.Notes) && task.Notes.Length > 1000)
                task.Notes = task.Notes[..1000];

            // تصحيح الحالة والأولوية
            if (!IsValidStatus(task.Status))
                task.Status = TaskConstants.Status.Pending;

            if (!IsValidPriority(task.Priority))
                task.Priority = TaskConstants.Priority.Medium;

            // تحديث تاريخ الطلب إذا لم يكن محدداً
            if (task.RequestDate == default)
                task.RequestDate = DateTime.Now;

            return task;
        }

        /// <summary>
        /// إنشاء ملخص للمهمة
        /// </summary>
        /// <param name="task">المهمة</param>
        /// <returns>ملخص المهمة</returns>
        public static string GetTaskSummary(Models.Task? task)
        {
            if (task == null) return "مهمة غير موجودة";

            var summary = $"المهمة: {task.Description}";
            summary += $" | الحالة: {GetStatusDisplay(task.Status)}";
            summary += $" | الأولوية: {GetPriorityDisplay(task.Priority)}";

            summary += $" | تاريخ الطلب: {task.RequestDate:yyyy-MM-dd}";

            if (task.CompletedAt.HasValue)
                summary += $" | تاريخ الإكمال: {task.CompletedAt.Value:yyyy-MM-dd}";

            if (task.User != null)
                summary += $" | المستخدم: {task.User.Name}";

            return summary;
        }
    }
}
