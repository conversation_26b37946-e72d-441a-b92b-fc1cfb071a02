<Window x:Class="NetworkManagement.Views.PurchaseDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="{Binding Title}"
        Height="600" Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft"
        Background="{DynamicResource MaterialDesignPaper}"
        Foreground="{DynamicResource MaterialDesignBody}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Main Content -->
        <ScrollViewer Grid.Row="0" VerticalScrollBarVisibility="Auto" Margin="20">
            <StackPanel>
                <!-- Item Type -->
                <TextBox materialDesign:HintAssist.Hint="نوع العنصر *"
                         materialDesign:HintAssist.IsFloating="True"
                         Text="{Binding ItemType, UpdateSourceTrigger=PropertyChanged}"
                         Margin="0,0,0,20"/>

                <!-- Price and Quantity -->
                <Grid Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <TextBox Grid.Column="0"
                             materialDesign:HintAssist.Hint="السعر (ر.ي) *"
                             materialDesign:HintAssist.IsFloating="True"
                             Text="{Binding Price, UpdateSourceTrigger=PropertyChanged, StringFormat=N0}"
                             Margin="0,0,10,0"/>

                    <TextBox Grid.Column="1"
                             materialDesign:HintAssist.Hint="الكمية *"
                             materialDesign:HintAssist.IsFloating="True"
                             Text="{Binding Quantity, UpdateSourceTrigger=PropertyChanged}"
                             Margin="10,0,0,0"/>
                </Grid>

                <!-- Unit -->
                <ComboBox materialDesign:HintAssist.Hint="الوحدة"
                          materialDesign:HintAssist.IsFloating="True"
                          ItemsSource="{Binding Units}"
                          SelectedItem="{Binding Unit}"
                          IsEditable="True"
                          Margin="0,0,0,20"/>

                <!-- Supplier -->
                <TextBox materialDesign:HintAssist.Hint="المورد"
                         materialDesign:HintAssist.IsFloating="True"
                         Text="{Binding Supplier, UpdateSourceTrigger=PropertyChanged}"
                         Margin="0,0,0,20"/>

                <!-- Category -->
                <ComboBox materialDesign:HintAssist.Hint="الفئة"
                          materialDesign:HintAssist.IsFloating="True"
                          ItemsSource="{Binding Categories}"
                          SelectedItem="{Binding Category}"
                          Margin="0,0,0,20"/>

                <!-- Date -->
                <DatePicker materialDesign:HintAssist.Hint="تاريخ الشراء"
                           materialDesign:HintAssist.IsFloating="True"
                           SelectedDate="{Binding Date}"
                           Margin="0,0,0,20"/>

                <!-- Invoice Number -->
                <TextBox materialDesign:HintAssist.Hint="رقم الفاتورة"
                         materialDesign:HintAssist.IsFloating="True"
                         Text="{Binding InvoiceNumber, UpdateSourceTrigger=PropertyChanged}"
                         Margin="0,0,0,20"/>

                <!-- Network -->
                <ComboBox materialDesign:HintAssist.Hint="الشبكة (اختياري)"
                         materialDesign:HintAssist.IsFloating="True"
                         ItemsSource="{Binding AvailableNetworks}"
                         SelectedValuePath="Id"
                         DisplayMemberPath="Name"
                         SelectedValue="{Binding SelectedNetworkId}"
                         Margin="0,0,0,20"/>

                <!-- Description -->
                <TextBox materialDesign:HintAssist.Hint="الوصف"
                         materialDesign:HintAssist.IsFloating="True"
                         Text="{Binding Description, UpdateSourceTrigger=PropertyChanged}"
                         AcceptsReturn="True"
                         TextWrapping="Wrap"
                         MinLines="3"
                         MaxLines="5"
                         VerticalScrollBarVisibility="Auto"
                         Margin="0,0,0,20"/>

                <!-- Error Message -->
                <TextBlock Text="{Binding ErrorMessage}"
                           Foreground="{DynamicResource ValidationErrorBrush}"
                           FontWeight="Medium"
                           Margin="0,0,0,10"
                           Visibility="{Binding ErrorMessage, Converter={StaticResource StringToVisibilityConverter}}"/>

                <!-- Required Fields Note -->
                <TextBlock Text="* الحقول المطلوبة"
                           FontSize="12"
                           Foreground="{DynamicResource MaterialDesignBodyLight}"
                           Margin="0,10,0,0"/>
            </StackPanel>
        </ScrollViewer>

        <!-- Action Buttons -->
        <Border Grid.Row="1"
                Background="{DynamicResource MaterialDesignPaper}"
                BorderBrush="{DynamicResource MaterialDesignDivider}"
                BorderThickness="0,1,0,0"
                Padding="20">

            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Loading Indicator -->
                <ProgressBar Grid.Column="0"
                             IsIndeterminate="True"
                             Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                             VerticalAlignment="Center"
                             Height="4"
                             Margin="0,0,20,0"/>

                <!-- Cancel Button -->
                <Button Grid.Column="1"
                        Content="إلغاء"
                        Command="{Binding CancelCommand}"
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        Margin="0,0,10,0"
                        MinWidth="80"/>

                <!-- Save Button -->
                <Button Grid.Column="2"
                        Content="{Binding IsEditMode, Converter={StaticResource BooleanToStringConverter}, ConverterParameter='تحديث|حفظ'}"
                        Command="{Binding SaveCommand}"
                        Style="{StaticResource MaterialDesignRaisedButton}"
                        IsEnabled="{Binding IsLoading, Converter={StaticResource InverseBooleanConverter}}"
                        MinWidth="80"/>
            </Grid>
        </Border>
    </Grid>
</Window>
