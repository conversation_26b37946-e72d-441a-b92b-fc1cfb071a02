using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using NetworkManagement.Data;
using NetworkManagement.Models;

namespace NetworkManagement.Services
{
    public class SiteService : ISiteService
    {
        private readonly NetworkDbContext _context;
        private readonly ISiteInventoryService _siteInventoryService;

        public SiteService(NetworkDbContext context, ISiteInventoryService siteInventoryService)
        {
            _context = context;
            _siteInventoryService = siteInventoryService;
        }

        public async Task<IEnumerable<Site>> GetAllAsync(string? networkFilter = null)
        {
            var query = _context.Sites?.Include(s => s.Devices).Include(s => s.Network).AsQueryable() ?? 
                throw new InvalidOperationException("DbSet<Site> is null");

            if (!string.IsNullOrEmpty(networkFilter))
            {
                query = query.Where(s => s.NetworkId == networkFilter);
            }

            return await query.OrderBy(s => s.Name).ToListAsync();
        }

        public async Task<Site?> GetByIdAsync(string id)
        {
            if (_context.Sites == null)
                return null;

            return await _context.Sites
                .Include(s => s.Devices)
                .Include(s => s.Network)
                .FirstOrDefaultAsync(s => s.Id == id);
        }

        public async Task<Site> CreateAsync(Site site)
        {
            if (_context.Sites == null)
                throw new InvalidOperationException("DbSet<Site> is null");

            site.Id = Guid.NewGuid().ToString();
            site.CreatedAt = DateTime.Now;
            site.UpdatedAt = DateTime.Now;

            _context.Sites.Add(site);
            await _context.SaveChangesAsync();

            // خصم الكميات من المخزون (دعم القيم السالبة)
            try
            {
                await _siteInventoryService.DeductInventoryForNewSiteAsync(site, site.NetworkId);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"تحذير: فشل في تحديث المخزون: {ex.Message}");
                // لا نمنع إضافة الموقع حتى لو فشل تحديث المخزون
            }

            return site;
        }

        public async Task<Site> UpdateAsync(Site site)
        {
            if (_context.Sites == null)
                throw new InvalidOperationException("DbSet<Site> is null");

            // الحصول على الموقع القديم
            var oldSite = await _context.Sites.AsNoTracking().FirstOrDefaultAsync(s => s.Id == site.Id);
            if (oldSite == null)
                throw new ArgumentException("الموقع غير موجود", nameof(site));

            // تحديث المخزون بناءً على التغييرات (دعم القيم السالبة)
            try
            {
                await _siteInventoryService.UpdateInventoryForSiteModificationAsync(oldSite, site, site.NetworkId);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"تحذير: فشل في تحديث المخزون: {ex.Message}");
                // لا نمنع تحديث الموقع حتى لو فشل تحديث المخزون
            }

            site.UpdatedAt = DateTime.Now;
            _context.Sites.Update(site);
            await _context.SaveChangesAsync();
            return site;
        }

        public async Task<bool> DeleteAsync(string id)
        {
            if (_context.Sites == null)
                throw new InvalidOperationException("DbSet<Site> is null");

            var site = await _context.Sites.FindAsync(id);
            if (site == null) return false;

            var networkId = site.NetworkId;

            // إرجاع الكميات إلى المخزون (دعم القيم السالبة)
            try
            {
                await _siteInventoryService.RestoreInventoryForDeletedSiteAsync(site, networkId);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"تحذير: فشل في إرجاع المخزون: {ex.Message}");
                // لا نمنع حذف الموقع حتى لو فشل تحديث المخزون
            }

            _context.Sites.Remove(site);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<IEnumerable<Site>> SearchAsync(string searchTerm, string? networkFilter = null)
        {
            var query = _context.Sites?.Include(s => s.Devices).Include(s => s.Network).AsQueryable() ?? 
                throw new InvalidOperationException("DbSet<Site> is null");

            if (!string.IsNullOrEmpty(networkFilter))
            {
                query = query.Where(s => s.NetworkId == networkFilter);
            }

            if (!string.IsNullOrEmpty(searchTerm))
            {
                query = query.Where(s =>
                    s.Name.Contains(searchTerm) ||
                    (s.Address ?? "").Contains(searchTerm) ||
                    (s.Phone ?? "").Contains(searchTerm));
            }

            return await query.OrderBy(s => s.Name).ToListAsync();
        }
    }
}

