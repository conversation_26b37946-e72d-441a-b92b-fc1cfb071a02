using System;

namespace NetworkManagement.Services
{
    /// <summary>
    /// خدمة تشفير كلمات المرور
    /// </summary>
    public interface IPasswordHashService
    {
        /// <summary>
        /// تشفير كلمة المرور
        /// </summary>
        /// <param name="password">كلمة المرور الأصلية</param>
        /// <returns>كلمة المرور المشفرة</returns>
        string HashPassword(string password);

        /// <summary>
        /// التحقق من كلمة المرور
        /// </summary>
        /// <param name="password">كلمة المرور الأصلية</param>
        /// <param name="hashedPassword">كلمة المرور المشفرة</param>
        /// <returns>true إذا كانت كلمة المرور صحيحة</returns>
        bool VerifyPassword(string password, string hashedPassword);

        /// <summary>
        /// التحقق من أن كلمة المرور مشفرة بالفعل
        /// </summary>
        /// <param name="password">كلمة المرور للفحص</param>
        /// <returns>true إذا كانت مشفرة</returns>
        bool IsPasswordHashed(string password);
    }
}
