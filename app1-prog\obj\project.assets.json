{"version": 3, "targets": {"net6.0-windows7.0": {"BCrypt.Net-Next/4.0.3": {"type": "package", "compile": {"lib/net6.0/BCrypt.Net-Next.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/BCrypt.Net-Next.dll": {"related": ".xml"}}}, "CommunityToolkit.Mvvm/8.2.2": {"type": "package", "compile": {"lib/net6.0/CommunityToolkit.Mvvm.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/CommunityToolkit.Mvvm.dll": {"related": ".pdb;.xml"}}, "build": {"buildTransitive/netstandard2.1/CommunityToolkit.Mvvm.targets": {}}}, "EPPlus/7.0.0": {"type": "package", "dependencies": {"EPPlus.Interfaces": "6.1.1", "EPPlus.System.Drawing": "6.1.1", "Microsoft.Extensions.Configuration.Json": "6.0.0", "Microsoft.IO.RecyclableMemoryStream": "2.3.2", "System.Security.Cryptography.Pkcs": "6.0.3"}, "compile": {"lib/net6.0/EPPlus.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/EPPlus.dll": {"related": ".xml"}}}, "EPPlus.Interfaces/6.1.1": {"type": "package", "compile": {"lib/net6.0/EPPlus.Interfaces.dll": {}}, "runtime": {"lib/net6.0/EPPlus.Interfaces.dll": {}}}, "EPPlus.System.Drawing/6.1.1": {"type": "package", "dependencies": {"EPPlus.Interfaces": "6.1.1", "System.Drawing.Common": "6.0.0"}, "compile": {"lib/net6.0/EPPlus.System.Drawing.dll": {}}, "runtime": {"lib/net6.0/EPPlus.System.Drawing.dll": {}}}, "HarfBuzzSharp/7.3.0.1": {"type": "package", "dependencies": {"HarfBuzzSharp.NativeAssets.Win32": "7.3.0.1", "HarfBuzzSharp.NativeAssets.macOS": "7.3.0.1"}, "compile": {"lib/net6.0/HarfBuzzSharp.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/HarfBuzzSharp.dll": {"related": ".pdb;.xml"}}}, "HarfBuzzSharp.NativeAssets.macOS/7.3.0.1": {"type": "package", "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/_._": {}}, "runtimeTargets": {"runtimes/osx/native/libHarfBuzzSharp.dylib": {"assetType": "native", "rid": "osx"}}}, "HarfBuzzSharp.NativeAssets.Win32/7.3.0.1": {"type": "package", "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win-arm64/native/libHarfBuzzSharp.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/libHarfBuzzSharp.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/libHarfBuzzSharp.dll": {"assetType": "native", "rid": "win-x86"}}}, "Humanizer.Core/2.8.26": {"type": "package", "compile": {"lib/netstandard2.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Humanizer.dll": {"related": ".xml"}}}, "MaterialDesignColors/2.1.4": {"type": "package", "compile": {"lib/net6.0/MaterialDesignColors.dll": {"related": ".pdb"}}, "runtime": {"lib/net6.0/MaterialDesignColors.dll": {"related": ".pdb"}}}, "MaterialDesignThemes/4.9.0": {"type": "package", "dependencies": {"MaterialDesignColors": "2.1.4", "Microsoft.Xaml.Behaviors.Wpf": "1.1.39"}, "compile": {"lib/net6.0/MaterialDesignThemes.Wpf.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/MaterialDesignThemes.Wpf.dll": {"related": ".pdb;.xml"}}, "build": {"build/MaterialDesignThemes.targets": {}}}, "Microsoft.EntityFrameworkCore/6.0.7": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "6.0.7", "Microsoft.EntityFrameworkCore.Analyzers": "6.0.7", "Microsoft.Extensions.Caching.Memory": "6.0.1", "Microsoft.Extensions.DependencyInjection": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0"}, "compile": {"lib/net6.0/Microsoft.EntityFrameworkCore.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/Microsoft.EntityFrameworkCore.props": {}}}, "Microsoft.EntityFrameworkCore.Abstractions/6.0.7": {"type": "package", "compile": {"lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore.Analyzers/6.0.7": {"type": "package", "compile": {"lib/netstandard2.0/_._": {}}, "runtime": {"lib/netstandard2.0/_._": {}}}, "Microsoft.EntityFrameworkCore.Design/6.0.7": {"type": "package", "dependencies": {"Humanizer.Core": "2.8.26", "Microsoft.EntityFrameworkCore.Relational": "6.0.7"}, "compile": {"lib/net6.0/_._": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Design.dll": {"related": ".xml"}}, "build": {"build/net6.0/Microsoft.EntityFrameworkCore.Design.props": {}}}, "Microsoft.EntityFrameworkCore.Relational/6.0.7": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore": "6.0.7", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0"}, "compile": {"lib/net6.0/Microsoft.EntityFrameworkCore.Relational.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Relational.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore.Tools/6.0.7": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore.Design": "6.0.7"}, "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/_._": {}}}, "Microsoft.Extensions.Caching.Abstractions/7.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Caching.Memory/7.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration/7.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.Abstractions/7.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.Binder/7.0.3": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "7.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.CommandLine/7.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "7.0.0", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/7.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "7.0.0", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.FileExtensions/7.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "7.0.0", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "7.0.0", "Microsoft.Extensions.FileProviders.Physical": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.Json/7.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "7.0.0", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "7.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "7.0.0", "System.Text.Json": "7.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.UserSecrets/7.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.Configuration.Json": "7.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "7.0.0", "Microsoft.Extensions.FileProviders.Physical": "7.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/Microsoft.Extensions.Configuration.UserSecrets.props": {}, "buildTransitive/net6.0/Microsoft.Extensions.Configuration.UserSecrets.targets": {}}}, "Microsoft.Extensions.DependencyInjection/7.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/7.0.0": {"type": "package", "compile": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Abstractions/7.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Physical/7.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "7.0.0", "Microsoft.Extensions.FileSystemGlobbing": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.FileSystemGlobbing/7.0.0": {"type": "package", "compile": {"lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Hosting/7.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "7.0.0", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.Configuration.Binder": "7.0.3", "Microsoft.Extensions.Configuration.CommandLine": "7.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "7.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "7.0.0", "Microsoft.Extensions.Configuration.Json": "7.0.0", "Microsoft.Extensions.Configuration.UserSecrets": "7.0.0", "Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "7.0.0", "Microsoft.Extensions.FileProviders.Physical": "7.0.0", "Microsoft.Extensions.Hosting.Abstractions": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Configuration": "7.0.0", "Microsoft.Extensions.Logging.Console": "7.0.0", "Microsoft.Extensions.Logging.Debug": "7.0.0", "Microsoft.Extensions.Logging.EventLog": "7.0.0", "Microsoft.Extensions.Logging.EventSource": "7.0.0", "Microsoft.Extensions.Options": "7.0.1", "System.Diagnostics.DiagnosticSource": "7.0.1"}, "compile": {"lib/net6.0/Microsoft.Extensions.Hosting.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Hosting.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Hosting.Abstractions/7.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "7.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Logging/7.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Logging.Abstractions/7.0.0": {"type": "package", "compile": {"lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/Microsoft.Extensions.Logging.Abstractions.targets": {}}}, "Microsoft.Extensions.Logging.Configuration/7.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "7.0.0", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.Configuration.Binder": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "7.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Logging.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Configuration.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Logging.Console/7.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Configuration": "7.0.0", "Microsoft.Extensions.Options": "7.0.0", "System.Text.Json": "7.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Logging.Console.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Console.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Logging.Debug/7.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Logging.Debug.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Debug.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Logging.EventLog/7.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.0", "System.Diagnostics.EventLog": "7.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Logging.EventLog.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.EventLog.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Logging.EventSource/7.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0", "System.Text.Json": "7.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Logging.EventSource.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.EventSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Options/7.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/7.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.Configuration.Binder": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Primitives/7.0.0": {"type": "package", "compile": {"lib/net6.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.IO.RecyclableMemoryStream/2.3.2": {"type": "package", "compile": {"lib/net5.0/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}}, "Microsoft.Win32.SystemEvents/7.0.0": {"type": "package", "compile": {"lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"assetType": "runtime", "rid": "win"}}}, "Microsoft.Xaml.Behaviors.Wpf/1.1.39": {"type": "package", "compile": {"lib/net5.0-windows7.0/Microsoft.Xaml.Behaviors.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net5.0-windows7.0/Microsoft.Xaml.Behaviors.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "MySqlConnector/2.1.13": {"type": "package", "compile": {"lib/net6.0/MySqlConnector.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/MySqlConnector.dll": {"related": ".xml"}}}, "Pomelo.EntityFrameworkCore.MySql/6.0.2": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore.Relational": "[6.0.7, 7.0.0)", "Microsoft.Extensions.DependencyInjection": "6.0.0", "MySqlConnector": "2.1.2"}, "compile": {"lib/net6.0/Pomelo.EntityFrameworkCore.MySql.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Pomelo.EntityFrameworkCore.MySql.dll": {"related": ".xml"}}}, "QuestPDF/2023.12.6": {"type": "package", "dependencies": {"SkiaSharp": "2.88.7", "SkiaSharp.HarfBuzz": "2.88.7"}, "compile": {"lib/net6.0/QuestPDF.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/QuestPDF.dll": {"related": ".xml"}}}, "SkiaSharp/2.88.7": {"type": "package", "dependencies": {"SkiaSharp.NativeAssets.Win32": "2.88.7", "SkiaSharp.NativeAssets.macOS": "2.88.7"}, "compile": {"lib/net6.0/SkiaSharp.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/SkiaSharp.dll": {"related": ".pdb;.xml"}}}, "SkiaSharp.HarfBuzz/2.88.7": {"type": "package", "dependencies": {"HarfBuzzSharp": "7.3.0.1", "SkiaSharp": "2.88.7"}, "compile": {"lib/net6.0/SkiaSharp.HarfBuzz.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/SkiaSharp.HarfBuzz.dll": {"related": ".pdb;.xml"}}}, "SkiaSharp.NativeAssets.macOS/2.88.7": {"type": "package", "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/_._": {}}, "runtimeTargets": {"runtimes/osx/native/libSkiaSharp.dylib": {"assetType": "native", "rid": "osx"}}}, "SkiaSharp.NativeAssets.Win32/2.88.7": {"type": "package", "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win-arm64/native/libSkiaSharp.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/libSkiaSharp.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/libSkiaSharp.dll": {"assetType": "native", "rid": "win-x86"}}}, "System.Diagnostics.DiagnosticSource/7.0.1": {"type": "package", "compile": {"lib/net6.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Diagnostics.EventLog/7.0.0": {"type": "package", "compile": {"lib/net6.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Diagnostics.EventLog.Messages.dll": {"assetType": "runtime", "rid": "win"}, "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Drawing.Common/7.0.0": {"type": "package", "dependencies": {"Microsoft.Win32.SystemEvents": "7.0.0"}, "compile": {"lib/net6.0/System.Drawing.Common.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Drawing.Common.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Drawing.Common.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Pkcs/6.0.3": {"type": "package", "compile": {"lib/net6.0/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Security.Cryptography.Pkcs.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "compile": {"lib/net6.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/browser/lib/net6.0/System.Text.Encodings.Web.dll": {"assetType": "runtime", "rid": "browser"}}}, "System.Text.Json/8.0.5": {"type": "package", "dependencies": {"System.Text.Encodings.Web": "8.0.0"}, "compile": {"lib/net6.0/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/System.Text.Json.targets": {}}}}}, "libraries": {"BCrypt.Net-Next/4.0.3": {"sha512": "W+U9WvmZQgi5cX6FS5GDtDoPzUCV4LkBLkywq/kRZhuDwcbavOzcDAr3LXJFqHUi952Yj3LEYoWW0jbEUQChsA==", "type": "package", "path": "bcrypt.net-next/4.0.3", "files": [".nupkg.metadata", ".signature.p7s", "bcrypt.net-next.4.0.3.nupkg.sha512", "bcrypt.net-next.nuspec", "ico.png", "lib/net20/BCrypt.Net-Next.dll", "lib/net20/BCrypt.Net-Next.xml", "lib/net35/BCrypt.Net-Next.dll", "lib/net35/BCrypt.Net-Next.xml", "lib/net462/BCrypt.Net-Next.dll", "lib/net462/BCrypt.Net-Next.xml", "lib/net472/BCrypt.Net-Next.dll", "lib/net472/BCrypt.Net-Next.xml", "lib/net48/BCrypt.Net-Next.dll", "lib/net48/BCrypt.Net-Next.xml", "lib/net5.0/BCrypt.Net-Next.dll", "lib/net5.0/BCrypt.Net-Next.xml", "lib/net6.0/BCrypt.Net-Next.dll", "lib/net6.0/BCrypt.Net-Next.xml", "lib/netstandard2.0/BCrypt.Net-Next.dll", "lib/netstandard2.0/BCrypt.Net-Next.xml", "lib/netstandard2.1/BCrypt.Net-Next.dll", "lib/netstandard2.1/BCrypt.Net-Next.xml", "readme.md"]}, "CommunityToolkit.Mvvm/8.2.2": {"sha512": "r0g0k9tGYdrnz8R7T3x5UiokDffeevzK/2P/9SBL6fqLgN8B157MIi/bVUWI1KAz6ZorZrK9AdABCWUeXZZsvA==", "type": "package", "path": "communitytoolkit.mvvm/8.2.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "License.md", "ThirdPartyNotices.txt", "analyzers/dotnet/roslyn4.0/cs/CommunityToolkit.Mvvm.CodeFixers.dll", "analyzers/dotnet/roslyn4.0/cs/CommunityToolkit.Mvvm.SourceGenerators.dll", "analyzers/dotnet/roslyn4.3/cs/CommunityToolkit.Mvvm.CodeFixers.dll", "analyzers/dotnet/roslyn4.3/cs/CommunityToolkit.Mvvm.SourceGenerators.dll", "build/netstandard2.0/CommunityToolkit.Mvvm.targets", "build/netstandard2.1/CommunityToolkit.Mvvm.targets", "buildTransitive/netstandard2.0/CommunityToolkit.Mvvm.targets", "buildTransitive/netstandard2.1/CommunityToolkit.Mvvm.targets", "communitytoolkit.mvvm.8.2.2.nupkg.sha512", "communitytoolkit.mvvm.nuspec", "lib/net6.0/CommunityToolkit.Mvvm.dll", "lib/net6.0/CommunityToolkit.Mvvm.pdb", "lib/net6.0/CommunityToolkit.Mvvm.xml", "lib/netstandard2.0/CommunityToolkit.Mvvm.dll", "lib/netstandard2.0/CommunityToolkit.Mvvm.pdb", "lib/netstandard2.0/CommunityToolkit.Mvvm.xml", "lib/netstandard2.1/CommunityToolkit.Mvvm.dll", "lib/netstandard2.1/CommunityToolkit.Mvvm.pdb", "lib/netstandard2.1/CommunityToolkit.Mvvm.xml"]}, "EPPlus/7.0.0": {"sha512": "fqDPDAB0v+sU1m0VxDy4jBKhH77BoiaGjglnzflUVoHDL3Bnf8Xf/V8/dm3uYhMwChExnqFXxYscslu46seZQQ==", "type": "package", "path": "epplus/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "EPPlusLogo.png", "epplus.7.0.0.nupkg.sha512", "epplus.nuspec", "lib/net35/EPPlus.dll", "lib/net35/EPPlus.xml", "lib/net462/EPPlus.dll", "lib/net462/EPPlus.xml", "lib/net6.0/EPPlus.dll", "lib/net6.0/EPPlus.xml", "lib/net7.0/EPPlus.dll", "lib/net7.0/EPPlus.xml", "lib/netstandard2.0/EPPlus.dll", "lib/netstandard2.0/EPPlus.xml", "lib/netstandard2.1/EPPlus.dll", "lib/netstandard2.1/EPPlus.xml", "license.md", "readme.md", "readme.txt"]}, "EPPlus.Interfaces/6.1.1": {"sha512": "y7dkrOoE1ZR9Vgy1Jf2rEIaTf3SHlUjYt01NklP+F5Qh7S2ruPbzTcpYLRWMeXiG8XL8h2jqX4CyIkFt3NQGZw==", "type": "package", "path": "epplus.interfaces/6.1.1", "files": [".nupkg.metadata", ".signature.p7s", "EPPlusLogo.png", "epplus.interfaces.6.1.1.nupkg.sha512", "epplus.interfaces.nuspec", "lib/net35/EPPlus.Interfaces.dll", "lib/net462/EPPlus.Interfaces.dll", "lib/net6.0/EPPlus.Interfaces.dll", "lib/net7.0/EPPlus.Interfaces.dll", "lib/netstandard2.0/EPPlus.Interfaces.dll", "lib/netstandard2.1/EPPlus.Interfaces.dll", "license.md", "readme.md"]}, "EPPlus.System.Drawing/6.1.1": {"sha512": "lRF5gHYrmkHOOiLMI0t6q8zNYjUrzRgAM5BCXumv5xiqXko8fx3AWI+HCNZfhEqVFGOop+42KfR5GiUcCoyoMw==", "type": "package", "path": "epplus.system.drawing/6.1.1", "files": [".nupkg.metadata", ".signature.p7s", "EPPlusLogo.png", "epplus.system.drawing.6.1.1.nupkg.sha512", "epplus.system.drawing.nuspec", "lib/net35/EPPlus.System.Drawing.dll", "lib/net462/EPPlus.System.Drawing.dll", "lib/net6.0/EPPlus.System.Drawing.dll", "lib/net7.0/EPPlus.System.Drawing.dll", "lib/netstandard2.0/EPPlus.System.Drawing.dll", "lib/netstandard2.1/EPPlus.System.Drawing.dll", "license.md", "readme.md"]}, "HarfBuzzSharp/7.3.0.1": {"sha512": "/6z6TQBqWWbVpLUw7E/82HZKLT3OAzg/w6z8I6JFNFcYa3nM9RnZ8XGos9nD4buAzThLHpqPw/rb8FdZXEORAQ==", "type": "package", "path": "harfbuzzsharp/7.3.0.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "harfbuzzsharp.7.3.0.1.nupkg.sha512", "harfbuzzsharp.nuspec", "lib/monoandroid1.0/HarfBuzzSharp.dll", "lib/monoandroid1.0/HarfBuzzSharp.pdb", "lib/monoandroid1.0/HarfBuzzSharp.xml", "lib/net462/HarfBuzzSharp.dll", "lib/net462/HarfBuzzSharp.pdb", "lib/net462/HarfBuzzSharp.xml", "lib/net6.0-android30.0/HarfBuzzSharp.dll", "lib/net6.0-android30.0/HarfBuzzSharp.pdb", "lib/net6.0-android30.0/HarfBuzzSharp.xml", "lib/net6.0-ios13.6/HarfBuzzSharp.dll", "lib/net6.0-ios13.6/HarfBuzzSharp.pdb", "lib/net6.0-ios13.6/HarfBuzzSharp.xml", "lib/net6.0-maccatalyst13.5/HarfBuzzSharp.dll", "lib/net6.0-maccatalyst13.5/HarfBuzzSharp.pdb", "lib/net6.0-maccatalyst13.5/HarfBuzzSharp.xml", "lib/net6.0-macos10.15/HarfBuzzSharp.dll", "lib/net6.0-macos10.15/HarfBuzzSharp.pdb", "lib/net6.0-macos10.15/HarfBuzzSharp.xml", "lib/net6.0-tvos13.4/HarfBuzzSharp.dll", "lib/net6.0-tvos13.4/HarfBuzzSharp.pdb", "lib/net6.0-tvos13.4/HarfBuzzSharp.xml", "lib/net6.0/HarfBuzzSharp.dll", "lib/net6.0/HarfBuzzSharp.pdb", "lib/net6.0/HarfBuzzSharp.xml", "lib/netcoreapp3.1/HarfBuzzSharp.dll", "lib/netcoreapp3.1/HarfBuzzSharp.pdb", "lib/netcoreapp3.1/HarfBuzzSharp.xml", "lib/netstandard1.3/HarfBuzzSharp.dll", "lib/netstandard1.3/HarfBuzzSharp.pdb", "lib/netstandard1.3/HarfBuzzSharp.xml", "lib/netstandard2.0/HarfBuzzSharp.dll", "lib/netstandard2.0/HarfBuzzSharp.pdb", "lib/netstandard2.0/HarfBuzzSharp.xml", "lib/netstandard2.1/HarfBuzzSharp.dll", "lib/netstandard2.1/HarfBuzzSharp.pdb", "lib/netstandard2.1/HarfBuzzSharp.xml", "lib/tizen40/HarfBuzzSharp.dll", "lib/tizen40/HarfBuzzSharp.pdb", "lib/tizen40/HarfBuzzSharp.xml", "lib/uap10.0.10240/HarfBuzzSharp.dll", "lib/uap10.0.10240/HarfBuzzSharp.pdb", "lib/uap10.0.10240/HarfBuzzSharp.xml", "lib/uap10.0.16299/HarfBuzzSharp.dll", "lib/uap10.0.16299/HarfBuzzSharp.pdb", "lib/uap10.0.16299/HarfBuzzSharp.xml", "lib/xamarinios1.0/HarfBuzzSharp.dll", "lib/xamarinios1.0/HarfBuzzSharp.pdb", "lib/xamarinios1.0/HarfBuzzSharp.xml", "lib/xamarinmac2.0/HarfBuzzSharp.dll", "lib/xamarinmac2.0/HarfBuzzSharp.pdb", "lib/xamarinmac2.0/HarfBuzzSharp.xml", "lib/xamarintvos1.0/HarfBuzzSharp.dll", "lib/xamarintvos1.0/HarfBuzzSharp.pdb", "lib/xamarintvos1.0/HarfBuzzSharp.xml", "lib/xamarinwatchos1.0/HarfBuzzSharp.dll", "lib/xamarinwatchos1.0/HarfBuzzSharp.pdb", "lib/xamarinwatchos1.0/HarfBuzzSharp.xml"]}, "HarfBuzzSharp.NativeAssets.macOS/7.3.0.1": {"sha512": "4i6e8WhXF04At1rZ904xjt5g10W86u6B2SpzWAw+cfY4mzfyUV0hgZHIWsE6PsW8a9bP/bSdsZx/Rt4UIroi4w==", "type": "package", "path": "harfbuzzsharp.nativeassets.macos/7.3.0.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/net462/HarfBuzzSharp.NativeAssets.macOS.targets", "build/net6.0-macos10.15/HarfBuzzSharp.NativeAssets.macOS.targets", "build/xamarinmac2.0/HarfBuzzSharp.NativeAssets.macOS.targets", "buildTransitive/net462/HarfBuzzSharp.NativeAssets.macOS.targets", "buildTransitive/net6.0-macos10.15/HarfBuzzSharp.NativeAssets.macOS.targets", "buildTransitive/xamarinmac2.0/HarfBuzzSharp.NativeAssets.macOS.targets", "harfbuzzsharp.nativeassets.macos.7.3.0.1.nupkg.sha512", "harfbuzzsharp.nativeassets.macos.nuspec", "lib/net462/_._", "lib/net6.0-macos10.15/_._", "lib/net6.0/_._", "lib/netcoreapp3.1/_._", "lib/netstandard1.3/_._", "lib/xamarinmac2.0/_._", "runtimes/osx/native/libHarfBuzzSharp.dylib"]}, "HarfBuzzSharp.NativeAssets.Win32/7.3.0.1": {"sha512": "NxzjhGcY7fYCu51BsanQurXXyqGogZiMzFBpDrZg49jNhT/S0QWYe/uvmP9YbwagprKQ+2tbGT0cw1BvxJciXQ==", "type": "package", "path": "harfbuzzsharp.nativeassets.win32/7.3.0.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/net462/HarfBuzzSharp.NativeAssets.Win32.targets", "buildTransitive/net462/HarfBuzzSharp.NativeAssets.Win32.targets", "harfbuzzsharp.nativeassets.win32.7.3.0.1.nupkg.sha512", "harfbuzzsharp.nativeassets.win32.nuspec", "lib/net462/_._", "lib/net6.0/_._", "lib/netcoreapp3.1/_._", "lib/netstandard1.3/_._", "runtimes/win-arm64/native/libHarfBuzzSharp.dll", "runtimes/win-x64/native/libHarfBuzzSharp.dll", "runtimes/win-x86/native/libHarfBuzzSharp.dll"]}, "Humanizer.Core/2.8.26": {"sha512": "OiKusGL20vby4uDEswj2IgkdchC1yQ6rwbIkZDVBPIR6al2b7n3pC91elBul9q33KaBgRKhbZH3+2Ur4fnWx2A==", "type": "package", "path": "humanizer.core/2.8.26", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.2.8.26.nupkg.sha512", "humanizer.core.nuspec", "lib/netstandard1.0/Humanizer.dll", "lib/netstandard1.0/Humanizer.xml", "lib/netstandard2.0/Humanizer.dll", "lib/netstandard2.0/Humanizer.xml", "logo.png"]}, "MaterialDesignColors/2.1.4": {"sha512": "C4Oy+qkjMoMPoZKyqYdCnIYtK8c0OSIHmNP73Vgc69NjiUG093xTkE7W/Ks54cTDS7fmWOtUHfwISTVTtb/YKg==", "type": "package", "path": "materialdesigncolors/2.1.4", "files": [".nupkg.metadata", ".signature.p7s", "images/MaterialDesignColors.Icon.png", "lib/net462/MaterialDesignColors.dll", "lib/net462/MaterialDesignColors.pdb", "lib/net6.0/MaterialDesignColors.dll", "lib/net6.0/MaterialDesignColors.pdb", "lib/net7.0/MaterialDesignColors.dll", "lib/net7.0/MaterialDesignColors.pdb", "lib/netcoreapp3.1/MaterialDesignColors.dll", "lib/netcoreapp3.1/MaterialDesignColors.pdb", "materialdesigncolors.2.1.4.nupkg.sha512", "materialdesigncolors.nuspec"]}, "MaterialDesignThemes/4.9.0": {"sha512": "Bp9Auw70j+9V7WsUMT4pc8ulVzfL0Eav/tyGgICDirxxhKJwhqtC/6PRkTUm+R1t9611xiDuk5pSUNdDV6vfOQ==", "type": "package", "path": "materialdesignthemes/4.9.0", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "build/MaterialDesignThemes.targets", "build/Resources/Roboto/Roboto-Black.ttf", "build/Resources/Roboto/Roboto-BlackItalic.ttf", "build/Resources/Roboto/Roboto-Bold.ttf", "build/Resources/Roboto/Roboto-BoldItalic.ttf", "build/Resources/Roboto/Roboto-Italic.ttf", "build/Resources/Roboto/Roboto-Light.ttf", "build/Resources/Roboto/Roboto-LightItalic.ttf", "build/Resources/Roboto/Roboto-Medium.ttf", "build/Resources/Roboto/Roboto-MediumItalic.ttf", "build/Resources/Roboto/Roboto-Regular.ttf", "build/Resources/Roboto/Roboto-Thin.ttf", "build/Resources/Roboto/Roboto-ThinItalic.ttf", "build/Resources/Roboto/RobotoCondensed-Bold.ttf", "build/Resources/Roboto/RobotoCondensed-BoldItalic.ttf", "build/Resources/Roboto/RobotoCondensed-Italic.ttf", "build/Resources/Roboto/RobotoCondensed-Light.ttf", "build/Resources/Roboto/RobotoCondensed-LightItalic.ttf", "build/Resources/Roboto/RobotoCondensed-Regular.ttf", "images/MaterialDesignThemes.Icon.png", "lib/net462/MaterialDesignThemes.Wpf.dll", "lib/net462/MaterialDesignThemes.Wpf.pdb", "lib/net462/MaterialDesignThemes.Wpf.xml", "lib/net6.0/MaterialDesignThemes.Wpf.dll", "lib/net6.0/MaterialDesignThemes.Wpf.pdb", "lib/net6.0/MaterialDesignThemes.Wpf.xml", "lib/net7.0/MaterialDesignThemes.Wpf.dll", "lib/net7.0/MaterialDesignThemes.Wpf.pdb", "lib/net7.0/MaterialDesignThemes.Wpf.xml", "lib/netcoreapp3.1/MaterialDesignThemes.Wpf.dll", "lib/netcoreapp3.1/MaterialDesignThemes.Wpf.pdb", "lib/netcoreapp3.1/MaterialDesignThemes.Wpf.xml", "materialdesignthemes.4.9.0.nupkg.sha512", "materialdesignthemes.nuspec", "tools/VisualStudioToolsManifest.xml"]}, "Microsoft.EntityFrameworkCore/6.0.7": {"sha512": "9BsvGSpTzxvqnxH19wLBFivK5TzWmsHZQc/1cQ4b2e+k85aIG9R4FYewQLHZdPrAxNQImXjTyW5nRI3s1rpt6A==", "type": "package", "path": "microsoft.entityframeworkcore/6.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "buildTransitive/net6.0/Microsoft.EntityFrameworkCore.props", "lib/net6.0/Microsoft.EntityFrameworkCore.dll", "lib/net6.0/Microsoft.EntityFrameworkCore.xml", "microsoft.entityframeworkcore.6.0.7.nupkg.sha512", "microsoft.entityframeworkcore.nuspec"]}, "Microsoft.EntityFrameworkCore.Abstractions/6.0.7": {"sha512": "bjU0CkTqldgpVPTSj9M+R/3EaTz+u0jMeQMIC91YdGYDbpX/tAN5UYx+Ihzk4AtP8gmhburQUgMTdnmCE9c5sA==", "type": "package", "path": "microsoft.entityframeworkcore.abstractions/6.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.dll", "lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.xml", "microsoft.entityframeworkcore.abstractions.6.0.7.nupkg.sha512", "microsoft.entityframeworkcore.abstractions.nuspec"]}, "Microsoft.EntityFrameworkCore.Analyzers/6.0.7": {"sha512": "VAOrGma8mRspUb/9quwIr21UZVqfWOcRQqhcYNkHBUD7woenwFTBvntiC9h2Ebtvj/BrRfezqjaHpWVvPSg4dw==", "type": "package", "path": "microsoft.entityframeworkcore.analyzers/6.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "analyzers/dotnet/cs/Microsoft.EntityFrameworkCore.Analyzers.dll", "lib/netstandard2.0/_._", "microsoft.entityframeworkcore.analyzers.6.0.7.nupkg.sha512", "microsoft.entityframeworkcore.analyzers.nuspec"]}, "Microsoft.EntityFrameworkCore.Design/6.0.7": {"sha512": "chshSATT8ugtqCiYryx5qDefK99o2ojyMCOIlQgetDhe83czL9Sp7s1a5MkvUyN5D4F0Vv8gXU77Wv+cplUJuw==", "type": "package", "path": "microsoft.entityframeworkcore.design/6.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "build/net6.0/Microsoft.EntityFrameworkCore.Design.props", "lib/net6.0/Microsoft.EntityFrameworkCore.Design.dll", "lib/net6.0/Microsoft.EntityFrameworkCore.Design.xml", "microsoft.entityframeworkcore.design.6.0.7.nupkg.sha512", "microsoft.entityframeworkcore.design.nuspec"]}, "Microsoft.EntityFrameworkCore.Relational/6.0.7": {"sha512": "0uo4fPDHutMbd9AJJEKl2q/2fYuFGA8tEBE2fQeQFaNDd+F/aUjaXc1FUD84J7Wcax8WP40rZo1E0u9A0yPPZw==", "type": "package", "path": "microsoft.entityframeworkcore.relational/6.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/net6.0/Microsoft.EntityFrameworkCore.Relational.dll", "lib/net6.0/Microsoft.EntityFrameworkCore.Relational.xml", "microsoft.entityframeworkcore.relational.6.0.7.nupkg.sha512", "microsoft.entityframeworkcore.relational.nuspec"]}, "Microsoft.EntityFrameworkCore.Tools/6.0.7": {"sha512": "Gdv70a73pXNYwvlo278uPFU4zo5Dnl5sB1eiAHyc7AUd3xeWueqx9ke9F4n1CHnEaB9h21x/c2waQAdMJqcYCQ==", "type": "package", "path": "microsoft.entityframeworkcore.tools/6.0.7", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/net6.0/_._", "microsoft.entityframeworkcore.tools.6.0.7.nupkg.sha512", "microsoft.entityframeworkcore.tools.nuspec", "tools/EntityFrameworkCore.PS2.psd1", "tools/EntityFrameworkCore.PS2.psm1", "tools/EntityFrameworkCore.psd1", "tools/EntityFrameworkCore.psm1", "tools/about_EntityFrameworkCore.help.txt", "tools/init.ps1", "tools/net461/any/ef.exe", "tools/net461/win-x86/ef.exe", "tools/netcoreapp2.0/any/ef.dll", "tools/netcoreapp2.0/any/ef.runtimeconfig.json"]}, "Microsoft.Extensions.Caching.Abstractions/7.0.0": {"sha512": "IeimUd0TNbhB4ded3AbgBLQv2SnsiVugDyGV1MvspQFVlA07nDC7Zul7kcwH5jWN3JiTcp/ySE83AIJo8yfKjg==", "type": "package", "path": "microsoft.extensions.caching.abstractions/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Caching.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Caching.Abstractions.targets", "lib/net462/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net462/Microsoft.Extensions.Caching.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Caching.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Caching.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.xml", "microsoft.extensions.caching.abstractions.7.0.0.nupkg.sha512", "microsoft.extensions.caching.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Caching.Memory/7.0.0": {"sha512": "xpidBs2KCE2gw1JrD0quHE72kvCaI3xFql5/Peb2GRtUuZX+dYPoK/NTdVMiM67Svym0M0Df9A3xyU0FbMQhHw==", "type": "package", "path": "microsoft.extensions.caching.memory/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Caching.Memory.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Caching.Memory.targets", "lib/net462/Microsoft.Extensions.Caching.Memory.dll", "lib/net462/Microsoft.Extensions.Caching.Memory.xml", "lib/net6.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net6.0/Microsoft.Extensions.Caching.Memory.xml", "lib/net7.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net7.0/Microsoft.Extensions.Caching.Memory.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.xml", "microsoft.extensions.caching.memory.7.0.0.nupkg.sha512", "microsoft.extensions.caching.memory.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration/7.0.0": {"sha512": "tldQUBWt/xeH2K7/hMPPo5g8zuLc3Ro9I5d4o/XrxvxOCA2EZBtW7bCHHTc49fcBtvB8tLAb/Qsmfrq+2SJ4vA==", "type": "package", "path": "microsoft.extensions.configuration/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.targets", "lib/net462/Microsoft.Extensions.Configuration.dll", "lib/net462/Microsoft.Extensions.Configuration.xml", "lib/net6.0/Microsoft.Extensions.Configuration.dll", "lib/net6.0/Microsoft.Extensions.Configuration.xml", "lib/net7.0/Microsoft.Extensions.Configuration.dll", "lib/net7.0/Microsoft.Extensions.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.xml", "microsoft.extensions.configuration.7.0.0.nupkg.sha512", "microsoft.extensions.configuration.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Abstractions/7.0.0": {"sha512": "f34u2eaqIjNO9YLHBz8rozVZ+TcFiFs0F3r7nUJd7FRkVSxk8u4OpoK226mi49MwexHOR2ibP9MFvRUaLilcQQ==", "type": "package", "path": "microsoft.extensions.configuration.abstractions/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Abstractions.targets", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml", "microsoft.extensions.configuration.abstractions.7.0.0.nupkg.sha512", "microsoft.extensions.configuration.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Binder/7.0.3": {"sha512": "1eRFwJBrkkncTpvh6mivB8zg4uBVm6+Y6stEJERrVEqZZc8Hvf+N1iIgj2ySYDUQko4J1Gw1rLf1M8bG83F0eA==", "type": "package", "path": "microsoft.extensions.configuration.binder/7.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Binder.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Binder.targets", "lib/net462/Microsoft.Extensions.Configuration.Binder.dll", "lib/net462/Microsoft.Extensions.Configuration.Binder.xml", "lib/net6.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net6.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/net7.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net7.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.xml", "microsoft.extensions.configuration.binder.7.0.3.nupkg.sha512", "microsoft.extensions.configuration.binder.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.CommandLine/7.0.0": {"sha512": "a8Iq8SCw5m8W5pZJcPCgBpBO4E89+NaObPng+ApIhrGSv9X4JPrcFAaGM4sDgR0X83uhLgsNJq8VnGP/wqhr8A==", "type": "package", "path": "microsoft.extensions.configuration.commandline/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.CommandLine.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.CommandLine.targets", "lib/net462/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/net462/Microsoft.Extensions.Configuration.CommandLine.xml", "lib/net6.0/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/net6.0/Microsoft.Extensions.Configuration.CommandLine.xml", "lib/net7.0/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/net7.0/Microsoft.Extensions.Configuration.CommandLine.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.CommandLine.xml", "microsoft.extensions.configuration.commandline.7.0.0.nupkg.sha512", "microsoft.extensions.configuration.commandline.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.EnvironmentVariables/7.0.0": {"sha512": "RIkfqCkvrAogirjsqSrG1E1FxgrLsOZU2nhRbl07lrajnxzSU2isj2lwQah0CtCbLWo/pOIukQzM1GfneBUnxA==", "type": "package", "path": "microsoft.extensions.configuration.environmentvariables/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.EnvironmentVariables.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.targets", "lib/net462/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net462/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/net6.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net6.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/net7.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net7.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "microsoft.extensions.configuration.environmentvariables.7.0.0.nupkg.sha512", "microsoft.extensions.configuration.environmentvariables.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.FileExtensions/7.0.0": {"sha512": "xk2lRJ1RDuqe57BmgvRPyCt6zyePKUmvT6iuXqiHR+/OIIgWVR8Ff5k2p6DwmqY8a17hx/OnrekEhziEIeQP6Q==", "type": "package", "path": "microsoft.extensions.configuration.fileextensions/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.FileExtensions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.FileExtensions.targets", "lib/net462/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net462/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net6.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net6.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net7.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net7.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "microsoft.extensions.configuration.fileextensions.7.0.0.nupkg.sha512", "microsoft.extensions.configuration.fileextensions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Json/7.0.0": {"sha512": "LDNYe3uw76W35Jci+be4LDf2lkQZe0A7EEYQVChFbc509CpZ4Iupod8li4PUXPBhEUOFI/rlQNf5xkzJRQGvtA==", "type": "package", "path": "microsoft.extensions.configuration.json/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Json.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Json.targets", "lib/net462/Microsoft.Extensions.Configuration.Json.dll", "lib/net462/Microsoft.Extensions.Configuration.Json.xml", "lib/net6.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net6.0/Microsoft.Extensions.Configuration.Json.xml", "lib/net7.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net7.0/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.xml", "microsoft.extensions.configuration.json.7.0.0.nupkg.sha512", "microsoft.extensions.configuration.json.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.UserSecrets/7.0.0": {"sha512": "33HPW1PmB2RS0ietBQyvOxjp4O3wlt+4tIs8KPyMn1kqp04goiZGa7+3mc69NRLv6bphkLDy0YR7Uw3aZyf8Zw==", "type": "package", "path": "microsoft.extensions.configuration.usersecrets/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/net462/Microsoft.Extensions.Configuration.UserSecrets.props", "buildTransitive/net462/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/net6.0/Microsoft.Extensions.Configuration.UserSecrets.props", "buildTransitive/net6.0/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.props", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.targets", "lib/net462/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/net462/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/net6.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/net6.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/net7.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/net7.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "microsoft.extensions.configuration.usersecrets.7.0.0.nupkg.sha512", "microsoft.extensions.configuration.usersecrets.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection/7.0.0": {"sha512": "elNeOmkeX3eDVG6pYVeV82p29hr+UKDaBhrZyWvWLw/EVZSYEkZlQdkp0V39k/Xehs2Qa0mvoCvkVj3eQxNQ1Q==", "type": "package", "path": "microsoft.extensions.dependencyinjection/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.xml", "lib/net6.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net6.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net7.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net7.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.7.0.0.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/7.0.0": {"sha512": "h3j/QfmFN4S0w4C2A6X7arXij/M/OVw3uQHSOFxnND4DyAzO1F9eMX7Eti7lU/OkSthEE0WzRsfT/Dmx86jzCw==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.7.0.0.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Abstractions/7.0.0": {"sha512": "NyawiW9ZT/liQb34k9YqBSNPLuuPkrjMgQZ24Y/xXX1RoiBkLUdPMaQTmxhZ5TYu8ZKZ9qayzil75JX95vGQUg==", "type": "package", "path": "microsoft.extensions.fileproviders.abstractions/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Abstractions.targets", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "microsoft.extensions.fileproviders.abstractions.7.0.0.nupkg.sha512", "microsoft.extensions.fileproviders.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Physical/7.0.0": {"sha512": "K8D2MTR+EtzkbZ8z80LrG7Ur64R7ZZdRLt1J5cgpc/pUWl0C6IkAUapPuK28oionHueCPELUqq0oYEvZfalNdg==", "type": "package", "path": "microsoft.extensions.fileproviders.physical/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Physical.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Physical.targets", "lib/net462/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net462/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net6.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net6.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net7.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net7.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.xml", "microsoft.extensions.fileproviders.physical.7.0.0.nupkg.sha512", "microsoft.extensions.fileproviders.physical.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileSystemGlobbing/7.0.0": {"sha512": "2jONjKHiF+E92ynz2ZFcr9OvxIw+rTGMPEH+UZGeHTEComVav93jQUWGkso8yWwVBcEJGcNcZAaqY01FFJcj7w==", "type": "package", "path": "microsoft.extensions.filesystemglobbing/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileSystemGlobbing.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileSystemGlobbing.targets", "lib/net462/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net462/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net7.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net7.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.xml", "microsoft.extensions.filesystemglobbing.7.0.0.nupkg.sha512", "microsoft.extensions.filesystemglobbing.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Hosting/7.0.1": {"sha512": "aoeMou6XSW84wiqd895OdaGyO9PfH6nohQJ0XBcshRDafbdIU6PQIVl8TpOCssPYq3ciRseP5064hbFyCR9J9w==", "type": "package", "path": "microsoft.extensions.hosting/7.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Hosting.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Hosting.targets", "lib/net462/Microsoft.Extensions.Hosting.dll", "lib/net462/Microsoft.Extensions.Hosting.xml", "lib/net6.0/Microsoft.Extensions.Hosting.dll", "lib/net6.0/Microsoft.Extensions.Hosting.xml", "lib/net7.0/Microsoft.Extensions.Hosting.dll", "lib/net7.0/Microsoft.Extensions.Hosting.xml", "lib/netstandard2.0/Microsoft.Extensions.Hosting.dll", "lib/netstandard2.0/Microsoft.Extensions.Hosting.xml", "lib/netstandard2.1/Microsoft.Extensions.Hosting.dll", "lib/netstandard2.1/Microsoft.Extensions.Hosting.xml", "microsoft.extensions.hosting.7.0.1.nupkg.sha512", "microsoft.extensions.hosting.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Hosting.Abstractions/7.0.0": {"sha512": "43n9Je09z0p/7ViPxfRqs5BUItRLNVh5b6JH40F2Agkh2NBsY/jpNYTtbCcxrHCsA3oRmbR6RJBzUutB4VZvNQ==", "type": "package", "path": "microsoft.extensions.hosting.abstractions/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Hosting.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Hosting.Abstractions.targets", "lib/net462/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net462/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.xml", "microsoft.extensions.hosting.abstractions.7.0.0.nupkg.sha512", "microsoft.extensions.hosting.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging/7.0.0": {"sha512": "Nw2muoNrOG5U5qa2ZekXwudUn2BJcD41e65zwmDHb1fQegTX66UokLWZkJRpqSSHXDOWZ5V0iqhbxOEky91atA==", "type": "package", "path": "microsoft.extensions.logging/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.targets", "lib/net462/Microsoft.Extensions.Logging.dll", "lib/net462/Microsoft.Extensions.Logging.xml", "lib/net6.0/Microsoft.Extensions.Logging.dll", "lib/net6.0/Microsoft.Extensions.Logging.xml", "lib/net7.0/Microsoft.Extensions.Logging.dll", "lib/net7.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.1/Microsoft.Extensions.Logging.dll", "lib/netstandard2.1/Microsoft.Extensions.Logging.xml", "microsoft.extensions.logging.7.0.0.nupkg.sha512", "microsoft.extensions.logging.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Abstractions/7.0.0": {"sha512": "kmn78+LPVMOWeITUjIlfxUPDsI0R6G0RkeAMBmQxAJ7vBJn4q2dTva7pWi65ceN5vPGjJ9q/Uae2WKgvfktJAw==", "type": "package", "path": "microsoft.extensions.logging.abstractions/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn3.11/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.0/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net462/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net6.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.targets", "lib/net462/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net462/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.7.0.0.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Configuration/7.0.0": {"sha512": "FLDA0HcffKA8ycoDQLJuCNGIE42cLWPxgdQGRBaSzZrYTkMBjnf9zrr8pGT06psLq9Q+RKWmmZczQ9bCrXEBcA==", "type": "package", "path": "microsoft.extensions.logging.configuration/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.Configuration.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Configuration.targets", "lib/net462/Microsoft.Extensions.Logging.Configuration.dll", "lib/net462/Microsoft.Extensions.Logging.Configuration.xml", "lib/net6.0/Microsoft.Extensions.Logging.Configuration.dll", "lib/net6.0/Microsoft.Extensions.Logging.Configuration.xml", "lib/net7.0/Microsoft.Extensions.Logging.Configuration.dll", "lib/net7.0/Microsoft.Extensions.Logging.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Configuration.xml", "microsoft.extensions.logging.configuration.7.0.0.nupkg.sha512", "microsoft.extensions.logging.configuration.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Console/7.0.0": {"sha512": "qt5n8bHLZPUfuRnFxJKW5q9ZwOTncdh96rtWzWpX3Y/064MlxzCSw2ELF5Jlwdo+Y4wK3I47NmUTFsV7Sg8rqg==", "type": "package", "path": "microsoft.extensions.logging.console/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.Console.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Console.targets", "lib/net462/Microsoft.Extensions.Logging.Console.dll", "lib/net462/Microsoft.Extensions.Logging.Console.xml", "lib/net6.0/Microsoft.Extensions.Logging.Console.dll", "lib/net6.0/Microsoft.Extensions.Logging.Console.xml", "lib/net7.0/Microsoft.Extensions.Logging.Console.dll", "lib/net7.0/Microsoft.Extensions.Logging.Console.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Console.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Console.xml", "microsoft.extensions.logging.console.7.0.0.nupkg.sha512", "microsoft.extensions.logging.console.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Debug/7.0.0": {"sha512": "tFGGyPDpJ8ZdQdeckCArP7nZuoY3am9zJWuvp4OD1bHq65S0epW9BNHzAWeaIO4eYwWnGm1jRNt3vRciH8H6MA==", "type": "package", "path": "microsoft.extensions.logging.debug/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.Debug.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Debug.targets", "lib/net462/Microsoft.Extensions.Logging.Debug.dll", "lib/net462/Microsoft.Extensions.Logging.Debug.xml", "lib/net6.0/Microsoft.Extensions.Logging.Debug.dll", "lib/net6.0/Microsoft.Extensions.Logging.Debug.xml", "lib/net7.0/Microsoft.Extensions.Logging.Debug.dll", "lib/net7.0/Microsoft.Extensions.Logging.Debug.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Debug.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Debug.xml", "microsoft.extensions.logging.debug.7.0.0.nupkg.sha512", "microsoft.extensions.logging.debug.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.EventLog/7.0.0": {"sha512": "Rp7cYL9xQRVTgjMl77H5YDxszAaO+mlA+KT0BnLSVhuCoKQQOOs1sSK2/x8BK2dZ/lKeAC/CVF+20Ef2dpKXwg==", "type": "package", "path": "microsoft.extensions.logging.eventlog/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.EventLog.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.EventLog.targets", "lib/net462/Microsoft.Extensions.Logging.EventLog.dll", "lib/net462/Microsoft.Extensions.Logging.EventLog.xml", "lib/net6.0/Microsoft.Extensions.Logging.EventLog.dll", "lib/net6.0/Microsoft.Extensions.Logging.EventLog.xml", "lib/net7.0/Microsoft.Extensions.Logging.EventLog.dll", "lib/net7.0/Microsoft.Extensions.Logging.EventLog.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.EventLog.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.EventLog.xml", "microsoft.extensions.logging.eventlog.7.0.0.nupkg.sha512", "microsoft.extensions.logging.eventlog.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.EventSource/7.0.0": {"sha512": "MxQXndQFviIyOPqyMeLNshXnmqcfzEHE2wWcr7BF1unSisJgouZ3tItnq+aJLGPojrW8OZSC/ZdRoR6wAq+c7w==", "type": "package", "path": "microsoft.extensions.logging.eventsource/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.EventSource.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.EventSource.targets", "lib/net462/Microsoft.Extensions.Logging.EventSource.dll", "lib/net462/Microsoft.Extensions.Logging.EventSource.xml", "lib/net6.0/Microsoft.Extensions.Logging.EventSource.dll", "lib/net6.0/Microsoft.Extensions.Logging.EventSource.xml", "lib/net7.0/Microsoft.Extensions.Logging.EventSource.dll", "lib/net7.0/Microsoft.Extensions.Logging.EventSource.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.EventSource.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.EventSource.xml", "microsoft.extensions.logging.eventsource.7.0.0.nupkg.sha512", "microsoft.extensions.logging.eventsource.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Options/7.0.1": {"sha512": "pZRDYdN1FpepOIfHU62QoBQ6zdAoTvnjxFfqAzEd9Jhb2dfhA5i6jeTdgGgcgTWFRC7oT0+3XrbQu4LjvgX1Nw==", "type": "package", "path": "microsoft.extensions.options/7.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Options.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.targets", "lib/net462/Microsoft.Extensions.Options.dll", "lib/net462/Microsoft.Extensions.Options.xml", "lib/net6.0/Microsoft.Extensions.Options.dll", "lib/net6.0/Microsoft.Extensions.Options.xml", "lib/net7.0/Microsoft.Extensions.Options.dll", "lib/net7.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.1/Microsoft.Extensions.Options.dll", "lib/netstandard2.1/Microsoft.Extensions.Options.xml", "microsoft.extensions.options.7.0.1.nupkg.sha512", "microsoft.extensions.options.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Options.ConfigurationExtensions/7.0.0": {"sha512": "95UnxZkkFdXxF6vSrtJsMHCzkDeSMuUWGs2hDT54cX+U5eVajrCJ3qLyQRW+CtpTt5OJ8bmTvpQVHu1DLhH+cA==", "type": "package", "path": "microsoft.extensions.options.configurationextensions/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Options.ConfigurationExtensions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.ConfigurationExtensions.targets", "lib/net462/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net462/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/net6.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net6.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/net7.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net7.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "microsoft.extensions.options.configurationextensions.7.0.0.nupkg.sha512", "microsoft.extensions.options.configurationextensions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Primitives/7.0.0": {"sha512": "um1KU5kxcRp3CNuI8o/GrZtD4AIOXDk+RLsytjZ9QPok3ttLUelLKpilVPuaFT3TFjOhSibUAso0odbOaCDj3Q==", "type": "package", "path": "microsoft.extensions.primitives/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Primitives.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Primitives.targets", "lib/net462/Microsoft.Extensions.Primitives.dll", "lib/net462/Microsoft.Extensions.Primitives.xml", "lib/net6.0/Microsoft.Extensions.Primitives.dll", "lib/net6.0/Microsoft.Extensions.Primitives.xml", "lib/net7.0/Microsoft.Extensions.Primitives.dll", "lib/net7.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.7.0.0.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.IO.RecyclableMemoryStream/2.3.2": {"sha512": "Oh1qXXFdJFcHozvb4H6XYLf2W0meZFuG0A+TfapFPj9z5fd4vxiARGEhAaLj/6XWQaMYIv4SH/9Q6H78Hw0E2Q==", "type": "package", "path": "microsoft.io.recyclablememorystream/2.3.2", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Microsoft.IO.RecyclableMemoryStream.dll", "lib/net462/Microsoft.IO.RecyclableMemoryStream.xml", "lib/net5.0/Microsoft.IO.RecyclableMemoryStream.dll", "lib/net5.0/Microsoft.IO.RecyclableMemoryStream.xml", "lib/netcoreapp2.1/Microsoft.IO.RecyclableMemoryStream.dll", "lib/netcoreapp2.1/Microsoft.IO.RecyclableMemoryStream.xml", "lib/netstandard2.0/Microsoft.IO.RecyclableMemoryStream.dll", "lib/netstandard2.0/Microsoft.IO.RecyclableMemoryStream.xml", "lib/netstandard2.1/Microsoft.IO.RecyclableMemoryStream.dll", "lib/netstandard2.1/Microsoft.IO.RecyclableMemoryStream.xml", "microsoft.io.recyclablememorystream.2.3.2.nupkg.sha512", "microsoft.io.recyclablememorystream.nuspec"]}, "Microsoft.Win32.SystemEvents/7.0.0": {"sha512": "2nXPrhdAyAzir0gLl8Yy8S5Mnm/uBSQQA7jEsILOS1MTyS7DbmV1NgViMtvV1sfCD1ebITpNwb1NIinKeJgUVQ==", "type": "package", "path": "microsoft.win32.systemevents/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Win32.SystemEvents.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Win32.SystemEvents.targets", "lib/net462/Microsoft.Win32.SystemEvents.dll", "lib/net462/Microsoft.Win32.SystemEvents.xml", "lib/net6.0/Microsoft.Win32.SystemEvents.dll", "lib/net6.0/Microsoft.Win32.SystemEvents.xml", "lib/net7.0/Microsoft.Win32.SystemEvents.dll", "lib/net7.0/Microsoft.Win32.SystemEvents.xml", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.dll", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.xml", "microsoft.win32.systemevents.7.0.0.nupkg.sha512", "microsoft.win32.systemevents.nuspec", "runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.xml", "runtimes/win/lib/net7.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/net7.0/Microsoft.Win32.SystemEvents.xml", "useSharedDesignerContext.txt"]}, "Microsoft.Xaml.Behaviors.Wpf/1.1.39": {"sha512": "8PZKqw9QOcu42xk8puY4P1+EXHL9YGOR9b7qhaYx5cILHul456H073tj99vyPcCt0W0781T9RwHqkx507ZyUpQ==", "type": "package", "path": "microsoft.xaml.behaviors.wpf/1.1.39", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Design/Microsoft.Xaml.Behaviors.Design.dll", "lib/net45/Microsoft.Xaml.Behaviors.dll", "lib/net45/Microsoft.Xaml.Behaviors.pdb", "lib/net45/Microsoft.Xaml.Behaviors.xml", "lib/net5.0-windows7.0/Design/Microsoft.Xaml.Behaviors.DesignTools.dll", "lib/net5.0-windows7.0/Microsoft.Xaml.Behaviors.dll", "lib/net5.0-windows7.0/Microsoft.Xaml.Behaviors.pdb", "lib/net5.0-windows7.0/Microsoft.Xaml.Behaviors.xml", "lib/netcoreapp3.1/Design/Microsoft.Xaml.Behaviors.DesignTools.dll", "lib/netcoreapp3.1/Microsoft.Xaml.Behaviors.dll", "lib/netcoreapp3.1/Microsoft.Xaml.Behaviors.pdb", "lib/netcoreapp3.1/Microsoft.Xaml.Behaviors.xml", "microsoft.xaml.behaviors.wpf.1.1.39.nupkg.sha512", "microsoft.xaml.behaviors.wpf.nuspec", "tools/Install.ps1"]}, "MySqlConnector/2.1.13": {"sha512": "Y3qts7mnuZ4EGEQOI4xJ1n+9ZJOmrcwD2CdqyAXuQONBkrc2ENDywE6ZBysFaRojXrnWzLXpc595+GYlN/PI9Q==", "type": "package", "path": "mysqlconnector/2.1.13", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net45/MySqlConnector.dll", "lib/net45/MySqlConnector.xml", "lib/net461/MySqlConnector.dll", "lib/net461/MySqlConnector.xml", "lib/net471/MySqlConnector.dll", "lib/net471/MySqlConnector.xml", "lib/net5.0/MySqlConnector.dll", "lib/net5.0/MySqlConnector.xml", "lib/net6.0/MySqlConnector.dll", "lib/net6.0/MySqlConnector.xml", "lib/netcoreapp3.1/MySqlConnector.dll", "lib/netcoreapp3.1/MySqlConnector.xml", "lib/netstandard2.0/MySqlConnector.dll", "lib/netstandard2.0/MySqlConnector.xml", "lib/netstandard2.1/MySqlConnector.dll", "lib/netstandard2.1/MySqlConnector.xml", "logo.png", "mysqlconnector.2.1.13.nupkg.sha512", "mysqlconnector.nuspec"]}, "Pomelo.EntityFrameworkCore.MySql/6.0.2": {"sha512": "KvlZ800CnEuEGnxj5OT1fCKGjQXxW5kpPlCP91JqBYG+2Z3927eqXmlX6LLKUt4swqE8ZsEQ+Zkpab8bqstf4g==", "type": "package", "path": "pomelo.entityframeworkcore.mysql/6.0.2", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net6.0/Pomelo.EntityFrameworkCore.MySql.dll", "lib/net6.0/Pomelo.EntityFrameworkCore.MySql.xml", "pomelo.entityframeworkcore.mysql.6.0.2.nupkg.sha512", "pomelo.entityframeworkcore.mysql.nuspec"]}, "QuestPDF/2023.12.6": {"sha512": "6WYu+g7Q+q8ZKnoqpu7BhVUdoS8n2TD7tqpbm3ZCczDhk+SuatI3rwIuJKTrADI1kSMrrkOCVvr9nrWd3jyZXw==", "type": "package", "path": "questpdf/2023.12.6", "files": [".nupkg.metadata", ".signature.p7s", "Logo.png", "PackageLicense.md", "PackageReadme.md", "lib/net6.0/QuestPDF.dll", "lib/net6.0/QuestPDF.xml", "lib/net8.0/QuestPDF.dll", "lib/net8.0/QuestPDF.xml", "lib/netstandard2.0/QuestPDF.dll", "lib/netstandard2.0/QuestPDF.xml", "questpdf.2023.12.6.nupkg.sha512", "questpdf.nuspec"]}, "SkiaSharp/2.88.7": {"sha512": "LJHAMrbWO00J7jXWLWehyjqFo29T4VzABimfJb4nICqpEe3c/KvQGWL4ItON8ymzhxYOeFgyxeRzuNzO4GHSug==", "type": "package", "path": "skiasharp/2.88.7", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "interactive-extensions/dotnet/SkiaSharp.DotNet.Interactive.dll", "lib/monoandroid1.0/SkiaSharp.dll", "lib/monoandroid1.0/SkiaSharp.pdb", "lib/monoandroid1.0/SkiaSharp.xml", "lib/net462/SkiaSharp.dll", "lib/net462/SkiaSharp.pdb", "lib/net462/SkiaSharp.xml", "lib/net6.0-android30.0/SkiaSharp.dll", "lib/net6.0-android30.0/SkiaSharp.pdb", "lib/net6.0-android30.0/SkiaSharp.xml", "lib/net6.0-ios13.6/SkiaSharp.dll", "lib/net6.0-ios13.6/SkiaSharp.pdb", "lib/net6.0-ios13.6/SkiaSharp.xml", "lib/net6.0-maccatalyst13.5/SkiaSharp.dll", "lib/net6.0-maccatalyst13.5/SkiaSharp.pdb", "lib/net6.0-maccatalyst13.5/SkiaSharp.xml", "lib/net6.0-macos10.15/SkiaSharp.dll", "lib/net6.0-macos10.15/SkiaSharp.pdb", "lib/net6.0-macos10.15/SkiaSharp.xml", "lib/net6.0-tizen7.0/SkiaSharp.dll", "lib/net6.0-tizen7.0/SkiaSharp.pdb", "lib/net6.0-tizen7.0/SkiaSharp.xml", "lib/net6.0-tvos13.4/SkiaSharp.dll", "lib/net6.0-tvos13.4/SkiaSharp.pdb", "lib/net6.0-tvos13.4/SkiaSharp.xml", "lib/net6.0/SkiaSharp.dll", "lib/net6.0/SkiaSharp.pdb", "lib/net6.0/SkiaSharp.xml", "lib/netcoreapp3.1/SkiaSharp.dll", "lib/netcoreapp3.1/SkiaSharp.pdb", "lib/netcoreapp3.1/SkiaSharp.xml", "lib/netstandard1.3/SkiaSharp.dll", "lib/netstandard1.3/SkiaSharp.pdb", "lib/netstandard1.3/SkiaSharp.xml", "lib/netstandard2.0/SkiaSharp.dll", "lib/netstandard2.0/SkiaSharp.pdb", "lib/netstandard2.0/SkiaSharp.xml", "lib/netstandard2.1/SkiaSharp.dll", "lib/netstandard2.1/SkiaSharp.pdb", "lib/netstandard2.1/SkiaSharp.xml", "lib/tizen40/SkiaSharp.dll", "lib/tizen40/SkiaSharp.pdb", "lib/tizen40/SkiaSharp.xml", "lib/uap10.0.10240/SkiaSharp.dll", "lib/uap10.0.10240/SkiaSharp.pdb", "lib/uap10.0.10240/SkiaSharp.xml", "lib/uap10.0.16299/SkiaSharp.dll", "lib/uap10.0.16299/SkiaSharp.pdb", "lib/uap10.0.16299/SkiaSharp.xml", "lib/xamarinios1.0/SkiaSharp.dll", "lib/xamarinios1.0/SkiaSharp.pdb", "lib/xamarinios1.0/SkiaSharp.xml", "lib/xamarinmac2.0/SkiaSharp.dll", "lib/xamarinmac2.0/SkiaSharp.pdb", "lib/xamarinmac2.0/SkiaSharp.xml", "lib/xamarintvos1.0/SkiaSharp.dll", "lib/xamarintvos1.0/SkiaSharp.pdb", "lib/xamarintvos1.0/SkiaSharp.xml", "lib/xamarinwatchos1.0/SkiaSharp.dll", "lib/xamarinwatchos1.0/SkiaSharp.pdb", "lib/xamarinwatchos1.0/SkiaSharp.xml", "skiasharp.2.88.7.nupkg.sha512", "skiasharp.nuspec"]}, "SkiaSharp.HarfBuzz/2.88.7": {"sha512": "4Vd+GpokorZNpDvUp09zjK6CMXN35zyJtNvzkGtS1TsXlG/5yA3lWTsQIU0nSQjfgpYql0tSEU5+WTsjxljugg==", "type": "package", "path": "skiasharp.harfbuzz/2.88.7", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "lib/net462/SkiaSharp.HarfBuzz.dll", "lib/net462/SkiaSharp.HarfBuzz.pdb", "lib/net462/SkiaSharp.HarfBuzz.xml", "lib/net6.0/SkiaSharp.HarfBuzz.dll", "lib/net6.0/SkiaSharp.HarfBuzz.pdb", "lib/net6.0/SkiaSharp.HarfBuzz.xml", "lib/netcoreapp3.1/SkiaSharp.HarfBuzz.dll", "lib/netcoreapp3.1/SkiaSharp.HarfBuzz.pdb", "lib/netcoreapp3.1/SkiaSharp.HarfBuzz.xml", "lib/netstandard1.3/SkiaSharp.HarfBuzz.dll", "lib/netstandard1.3/SkiaSharp.HarfBuzz.pdb", "lib/netstandard1.3/SkiaSharp.HarfBuzz.xml", "lib/netstandard2.0/SkiaSharp.HarfBuzz.dll", "lib/netstandard2.0/SkiaSharp.HarfBuzz.pdb", "lib/netstandard2.0/SkiaSharp.HarfBuzz.xml", "lib/netstandard2.1/SkiaSharp.HarfBuzz.dll", "lib/netstandard2.1/SkiaSharp.HarfBuzz.pdb", "lib/netstandard2.1/SkiaSharp.HarfBuzz.xml", "skiasharp.harfbuzz.2.88.7.nupkg.sha512", "skiasharp.harfbuzz.nuspec"]}, "SkiaSharp.NativeAssets.macOS/2.88.7": {"sha512": "3jNzco4VjcYPFNxR9aNWcgweFXbTSdM1VpNRzCS4X0i1A1OuNqcaulrAvmntNpujeWxHo9e6WGh6FN8Jf5+XhA==", "type": "package", "path": "skiasharp.nativeassets.macos/2.88.7", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/net462/SkiaSharp.NativeAssets.macOS.targets", "build/net6.0-macos10.15/SkiaSharp.NativeAssets.macOS.targets", "build/xamarinmac2.0/SkiaSharp.NativeAssets.macOS.targets", "buildTransitive/net462/SkiaSharp.NativeAssets.macOS.targets", "buildTransitive/net6.0-macos10.15/SkiaSharp.NativeAssets.macOS.targets", "buildTransitive/xamarinmac2.0/SkiaSharp.NativeAssets.macOS.targets", "lib/net462/_._", "lib/net6.0-macos10.15/_._", "lib/net6.0/_._", "lib/netcoreapp3.1/_._", "lib/netstandard1.3/_._", "lib/xamarinmac2.0/_._", "runtimes/osx/native/libSkiaSharp.dylib", "skiasharp.nativeassets.macos.2.88.7.nupkg.sha512", "skiasharp.nativeassets.macos.nuspec"]}, "SkiaSharp.NativeAssets.Win32/2.88.7": {"sha512": "BCXmWdQ0oVck9vRwC8U3ocSaTHEx28VB+6qw9OxGIMQ86iO5bp4Flqk3IXH0l9Pbr7vWAUOpI212iaL9mTaUZQ==", "type": "package", "path": "skiasharp.nativeassets.win32/2.88.7", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/net462/SkiaSharp.NativeAssets.Win32.targets", "buildTransitive/net462/SkiaSharp.NativeAssets.Win32.targets", "lib/net462/_._", "lib/net6.0/_._", "lib/netcoreapp3.1/_._", "lib/netstandard1.3/_._", "runtimes/win-arm64/native/libSkiaSharp.dll", "runtimes/win-x64/native/libSkiaSharp.dll", "runtimes/win-x86/native/libSkiaSharp.dll", "skiasharp.nativeassets.win32.2.88.7.nupkg.sha512", "skiasharp.nativeassets.win32.nuspec"]}, "System.Diagnostics.DiagnosticSource/7.0.1": {"sha512": "T9SLFxzDp0SreCffRDXSAS5G+lq6E8qP4knHS2IBjwCdx2KEvGnGZsq7gFpselYOda7l6gXsJMD93TQsFj/URA==", "type": "package", "path": "system.diagnostics.diagnosticsource/7.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.DiagnosticSource.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.DiagnosticSource.targets", "lib/net462/System.Diagnostics.DiagnosticSource.dll", "lib/net462/System.Diagnostics.DiagnosticSource.xml", "lib/net6.0/System.Diagnostics.DiagnosticSource.dll", "lib/net6.0/System.Diagnostics.DiagnosticSource.xml", "lib/net7.0/System.Diagnostics.DiagnosticSource.dll", "lib/net7.0/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.xml", "system.diagnostics.diagnosticsource.7.0.1.nupkg.sha512", "system.diagnostics.diagnosticsource.nuspec", "useSharedDesignerContext.txt"]}, "System.Diagnostics.EventLog/7.0.0": {"sha512": "eUDP47obqQm3SFJfP6z+Fx2nJ4KKTQbXB4Q9Uesnzw9SbYdhjyoGXuvDn/gEmFY6N5Z3bFFbpAQGA7m6hrYJCw==", "type": "package", "path": "system.diagnostics.eventlog/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.EventLog.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.EventLog.targets", "lib/net462/System.Diagnostics.EventLog.dll", "lib/net462/System.Diagnostics.EventLog.xml", "lib/net6.0/System.Diagnostics.EventLog.dll", "lib/net6.0/System.Diagnostics.EventLog.xml", "lib/net7.0/System.Diagnostics.EventLog.dll", "lib/net7.0/System.Diagnostics.EventLog.xml", "lib/netstandard2.0/System.Diagnostics.EventLog.dll", "lib/netstandard2.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net7.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net7.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net7.0/System.Diagnostics.EventLog.xml", "system.diagnostics.eventlog.7.0.0.nupkg.sha512", "system.diagnostics.eventlog.nuspec", "useSharedDesignerContext.txt"]}, "System.Drawing.Common/7.0.0": {"sha512": "KIX+oBU38pxkKPxvLcLfIkOV5Ien8ReN78wro7OF5/erwcmortzeFx+iBswlh2Vz6gVne0khocQudGwaO1Ey6A==", "type": "package", "path": "system.drawing.common/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Drawing.Common.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Drawing.Common.targets", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/System.Drawing.Common.dll", "lib/net462/System.Drawing.Common.xml", "lib/net6.0/System.Drawing.Common.dll", "lib/net6.0/System.Drawing.Common.xml", "lib/net7.0/System.Drawing.Common.dll", "lib/net7.0/System.Drawing.Common.xml", "lib/netstandard2.0/System.Drawing.Common.dll", "lib/netstandard2.0/System.Drawing.Common.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "runtimes/win/lib/net6.0/System.Drawing.Common.dll", "runtimes/win/lib/net6.0/System.Drawing.Common.xml", "runtimes/win/lib/net7.0/System.Drawing.Common.dll", "runtimes/win/lib/net7.0/System.Drawing.Common.xml", "system.drawing.common.7.0.0.nupkg.sha512", "system.drawing.common.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.Pkcs/6.0.3": {"sha512": "18UT1BdZ4TYFBHk/wuq7JzCdE3X75z81X+C2rXqIlmEnC21Pm60spGV/dKQSzbyomstqYE33EuN5hfnC86VFxA==", "type": "package", "path": "system.security.cryptography.pkcs/6.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.Pkcs.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Security.Cryptography.Pkcs.dll", "lib/net461/System.Security.Cryptography.Pkcs.xml", "lib/net6.0/System.Security.Cryptography.Pkcs.dll", "lib/net6.0/System.Security.Cryptography.Pkcs.xml", "lib/netcoreapp3.1/System.Security.Cryptography.Pkcs.dll", "lib/netcoreapp3.1/System.Security.Cryptography.Pkcs.xml", "lib/netstandard2.0/System.Security.Cryptography.Pkcs.dll", "lib/netstandard2.0/System.Security.Cryptography.Pkcs.xml", "lib/netstandard2.1/System.Security.Cryptography.Pkcs.dll", "lib/netstandard2.1/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net461/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net461/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net6.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net6.0/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/netcoreapp3.1/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/netcoreapp3.1/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/netstandard2.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/netstandard2.0/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/netstandard2.1/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/netstandard2.1/System.Security.Cryptography.Pkcs.xml", "system.security.cryptography.pkcs.6.0.3.nupkg.sha512", "system.security.cryptography.pkcs.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Encodings.Web/8.0.0": {"sha512": "yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ==", "type": "package", "path": "system.text.encodings.web/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Text.Encodings.Web.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Text.Encodings.Web.targets", "lib/net462/System.Text.Encodings.Web.dll", "lib/net462/System.Text.Encodings.Web.xml", "lib/net6.0/System.Text.Encodings.Web.dll", "lib/net6.0/System.Text.Encodings.Web.xml", "lib/net7.0/System.Text.Encodings.Web.dll", "lib/net7.0/System.Text.Encodings.Web.xml", "lib/net8.0/System.Text.Encodings.Web.dll", "lib/net8.0/System.Text.Encodings.Web.xml", "lib/netstandard2.0/System.Text.Encodings.Web.dll", "lib/netstandard2.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net6.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net6.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net7.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net7.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net8.0/System.Text.Encodings.Web.xml", "system.text.encodings.web.8.0.0.nupkg.sha512", "system.text.encodings.web.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Json/8.0.5": {"sha512": "0f1B50Ss7rqxXiaBJyzUu9bWFOO2/zSlifZ/UNMdiIpDYe4cY4LQQicP4nirK1OS31I43rn062UIJ1Q9bpmHpg==", "type": "package", "path": "system.text.json/8.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn3.11/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.0/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "buildTransitive/net461/System.Text.Json.targets", "buildTransitive/net462/System.Text.Json.targets", "buildTransitive/net6.0/System.Text.Json.targets", "buildTransitive/netcoreapp2.0/System.Text.Json.targets", "buildTransitive/netstandard2.0/System.Text.Json.targets", "lib/net462/System.Text.Json.dll", "lib/net462/System.Text.Json.xml", "lib/net6.0/System.Text.Json.dll", "lib/net6.0/System.Text.Json.xml", "lib/net7.0/System.Text.Json.dll", "lib/net7.0/System.Text.Json.xml", "lib/net8.0/System.Text.Json.dll", "lib/net8.0/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.8.0.5.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt"]}}, "projectFileDependencyGroups": {"net6.0-windows7.0": ["BCrypt.Net-Next >= 4.0.3", "CommunityToolkit.Mvvm >= 8.2.2", "EPPlus >= 7.0.0", "MaterialDesignThemes >= 4.9.0", "Microsoft.EntityFrameworkCore >= 6.0.7", "Microsoft.EntityFrameworkCore.Relational >= 6.0.7", "Microsoft.EntityFrameworkCore.Tools >= 6.0.7", "Microsoft.Extensions.Caching.Memory >= 7.0.0", "Microsoft.Extensions.DependencyInjection >= 7.0.0", "Microsoft.Extensions.Hosting >= 7.0.1", "Microsoft.Xaml.Behaviors.Wpf >= 1.1.39", "MySqlConnector >= 2.1.13", "Pomelo.EntityFrameworkCore.MySql >= 6.0.2", "QuestPDF >= 2023.12.6", "System.Drawing.Common >= 7.0.0", "System.Text.Json >= 8.0.5"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\APP\\app1-prog\\NetworkManagement.csproj", "projectName": "NetworkManagement", "projectPath": "D:\\APP\\app1-prog\\NetworkManagement.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\APP\\app1-prog\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {}}}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "10.0.100"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.2.2, )"}, "EPPlus": {"target": "Package", "version": "[7.0.0, )"}, "MaterialDesignThemes": {"target": "Package", "version": "[4.9.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[6.0.7, )"}, "Microsoft.EntityFrameworkCore.Relational": {"target": "Package", "version": "[6.0.7, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[6.0.7, )"}, "Microsoft.Extensions.Caching.Memory": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[7.0.1, )"}, "Microsoft.Xaml.Behaviors.Wpf": {"target": "Package", "version": "[1.1.39, )"}, "MySqlConnector": {"target": "Package", "version": "[2.1.13, )"}, "Pomelo.EntityFrameworkCore.MySql": {"target": "Package", "version": "[6.0.2, )"}, "QuestPDF": {"target": "Package", "version": "[2023.12.6, )"}, "System.Drawing.Common": {"target": "Package", "version": "[7.0.0, )"}, "System.Text.Json": {"target": "Package", "version": "[8.0.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.3.25201.16\\RuntimeIdentifierGraph.json", "packagesToPrune": {"Microsoft.CSharp": "(, 4.7.0]", "Microsoft.NETCore.App": "(, 2.1.0]", "Microsoft.VisualBasic": "(, 10.3.0]", "Microsoft.Win32.Primitives": "(, 4.3.0]", "Microsoft.Win32.Registry": "(, 5.0.0]", "runtime.any.System.Collections": "(, 4.3.0]", "runtime.any.System.Diagnostics.Tools": "(, 4.3.0]", "runtime.any.System.Diagnostics.Tracing": "(, 4.3.0]", "runtime.any.System.Globalization": "(, 4.3.0]", "runtime.any.System.Globalization.Calendars": "(, 4.3.0]", "runtime.any.System.IO": "(, 4.3.0]", "runtime.any.System.Reflection": "(, 4.3.0]", "runtime.any.System.Reflection.Extensions": "(, 4.3.0]", "runtime.any.System.Reflection.Primitives": "(, 4.3.0]", "runtime.any.System.Resources.ResourceManager": "(, 4.3.0]", "runtime.any.System.Runtime": "(, 4.3.1]", "runtime.any.System.Runtime.Handles": "(, 4.3.0]", "runtime.any.System.Runtime.InteropServices": "(, 4.3.0]", "runtime.any.System.Text.Encoding": "(, 4.3.0]", "runtime.any.System.Text.Encoding.Extensions": "(, 4.3.0]", "runtime.any.System.Threading.Tasks": "(, 4.3.0]", "runtime.any.System.Threading.Timer": "(, 4.3.0]", "runtime.aot.System.Collections": "(, 4.3.0]", "runtime.aot.System.Diagnostics.Tools": "(, 4.3.0]", "runtime.aot.System.Diagnostics.Tracing": "(, 4.3.0]", "runtime.aot.System.Globalization": "(, 4.3.0]", "runtime.aot.System.Globalization.Calendars": "(, 4.3.0]", "runtime.aot.System.IO": "(, 4.3.0]", "runtime.aot.System.Reflection": "(, 4.3.0]", "runtime.aot.System.Reflection.Extensions": "(, 4.3.0]", "runtime.aot.System.Reflection.Primitives": "(, 4.3.0]", "runtime.aot.System.Resources.ResourceManager": "(, 4.3.0]", "runtime.aot.System.Runtime": "(, 4.3.1]", "runtime.aot.System.Runtime.Handles": "(, 4.3.0]", "runtime.aot.System.Runtime.InteropServices": "(, 4.3.0]", "runtime.aot.System.Text.Encoding": "(, 4.3.0]", "runtime.aot.System.Text.Encoding.Extensions": "(, 4.3.0]", "runtime.aot.System.Threading.Tasks": "(, 4.3.0]", "runtime.aot.System.Threading.Timer": "(, 4.3.0]", "runtime.debian.8-x64.runtime.native.System": "(, 4.3.1]", "runtime.debian.8-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.debian.8-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.debian.8-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.debian.9-x64.runtime.native.System": "(, 4.3.1]", "runtime.debian.9-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.debian.9-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.debian.9-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.debian.9-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.debian.9-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.fedora.23-x64.runtime.native.System": "(, 4.3.1]", "runtime.fedora.23-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.fedora.23-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.fedora.23-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.fedora.24-x64.runtime.native.System": "(, 4.3.1]", "runtime.fedora.24-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.fedora.24-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.fedora.24-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.fedora.27-x64.runtime.native.System": "(, 4.3.1]", "runtime.fedora.27-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.fedora.27-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.fedora.27-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.fedora.27-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.fedora.27-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.fedora.28-x64.runtime.native.System": "(, 4.3.1]", "runtime.fedora.28-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.fedora.28-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.fedora.28-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.fedora.28-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.fedora.28-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.native.System.Security.Cryptography.Apple": "(, 4.3.1]", "runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.opensuse.13.2-x64.runtime.native.System": "(, 4.3.1]", "runtime.opensuse.13.2-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.opensuse.42.1-x64.runtime.native.System": "(, 4.3.1]", "runtime.opensuse.42.1-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.opensuse.42.3-x64.runtime.native.System": "(, 4.3.1]", "runtime.opensuse.42.3-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.opensuse.42.3-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.opensuse.42.3-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.osx.10.10-x64.runtime.native.System": "(, 4.3.1]", "runtime.osx.10.10-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.osx.10.10-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.osx.10.10-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "(, 4.3.1]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.rhel.7-x64.runtime.native.System": "(, 4.3.1]", "runtime.rhel.7-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.rhel.7-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.rhel.7-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.ubuntu.14.04-x64.runtime.native.System": "(, 4.3.1]", "runtime.ubuntu.14.04-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.ubuntu.16.04-x64.runtime.native.System": "(, 4.3.1]", "runtime.ubuntu.16.04-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.ubuntu.16.10-x64.runtime.native.System": "(, 4.3.1]", "runtime.ubuntu.16.10-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.ubuntu.18.04-x64.runtime.native.System": "(, 4.3.1]", "runtime.ubuntu.18.04-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.ubuntu.18.04-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.ubuntu.18.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.unix.Microsoft.Win32.Primitives": "(, 4.3.0]", "runtime.unix.System.Console": "(, 4.3.1]", "runtime.unix.System.Diagnostics.Debug": "(, 4.3.0]", "runtime.unix.System.IO.FileSystem": "(, 4.3.0]", "runtime.unix.System.Net.Primitives": "(, 4.3.0]", "runtime.unix.System.Net.Sockets": "(, 4.3.0]", "runtime.unix.System.Private.Uri": "(, 4.3.1]", "runtime.unix.System.Runtime.Extensions": "(, 4.3.1]", "runtime.win.Microsoft.Win32.Primitives": "(, 4.3.0]", "runtime.win.System.Console": "(, 4.3.1]", "runtime.win.System.Diagnostics.Debug": "(, 4.3.0]", "runtime.win.System.IO.FileSystem": "(, 4.3.0]", "runtime.win.System.Net.Primitives": "(, 4.3.0]", "runtime.win.System.Net.Sockets": "(, 4.3.0]", "runtime.win.System.Runtime.Extensions": "(, 4.3.1]", "runtime.win10-arm-aot.runtime.native.System.IO.Compression": "(, 4.0.1]", "runtime.win10-arm64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.win10-x64-aot.runtime.native.System.IO.Compression": "(, 4.0.1]", "runtime.win10-x86-aot.runtime.native.System.IO.Compression": "(, 4.0.1]", "runtime.win7-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.win7-x86.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.win7.System.Private.Uri": "(, 4.3.1]", "runtime.win8-arm.runtime.native.System.IO.Compression": "(, 4.3.2]", "System.AppContext": "(, 4.3.0]", "System.Buffers": "(, 4.5.1]", "System.Collections": "(, 4.3.0]", "System.Collections.Concurrent": "(, 4.3.0]", "System.Collections.Immutable": "(, 6.0.0]", "System.Collections.NonGeneric": "(, 4.3.0]", "System.Collections.Specialized": "(, 4.3.0]", "System.ComponentModel": "(, 4.3.0]", "System.ComponentModel.Annotations": "(, 5.0.0]", "System.ComponentModel.EventBasedAsync": "(, 4.3.0]", "System.ComponentModel.Primitives": "(, 4.3.0]", "System.ComponentModel.TypeConverter": "(, 4.3.0]", "System.Console": "(, 4.3.1]", "System.Data.Common": "(, 4.3.0]", "System.Data.DataSetExtensions": "(, 4.5.0]", "System.Diagnostics.Contracts": "(, 4.3.0]", "System.Diagnostics.Debug": "(, 4.3.0]", "System.Diagnostics.DiagnosticSource": "(, 6.0.0]", "System.Diagnostics.FileVersionInfo": "(, 4.3.0]", "System.Diagnostics.Process": "(, 4.3.0]", "System.Diagnostics.StackTrace": "(, 4.3.0]", "System.Diagnostics.TextWriterTraceListener": "(, 4.3.0]", "System.Diagnostics.Tools": "(, 4.3.0]", "System.Diagnostics.TraceSource": "(, 4.3.0]", "System.Diagnostics.Tracing": "(, 4.3.0]", "System.Drawing.Primitives": "(, 4.3.0]", "System.Dynamic.Runtime": "(, 4.3.0]", "System.Formats.Asn1": "(, 6.0.0]", "System.Globalization": "(, 4.3.0]", "System.Globalization.Calendars": "(, 4.3.0]", "System.Globalization.Extensions": "(, 4.3.0]", "System.IO": "(, 4.3.0]", "System.IO.Compression": "(, 4.3.0]", "System.IO.Compression.ZipFile": "(, 4.3.0]", "System.IO.FileSystem": "(, 4.3.0]", "System.IO.FileSystem.AccessControl": "(, 5.0.0]", "System.IO.FileSystem.DriveInfo": "(, 4.3.1]", "System.IO.FileSystem.Primitives": "(, 4.3.0]", "System.IO.FileSystem.Watcher": "(, 4.3.0]", "System.IO.IsolatedStorage": "(, 4.3.0]", "System.IO.MemoryMappedFiles": "(, 4.3.0]", "System.IO.Pipes": "(, 4.3.0]", "System.IO.Pipes.AccessControl": "(, 4.6.0]", "System.IO.UnmanagedMemoryStream": "(, 4.3.0]", "System.Linq": "(, 4.3.0]", "System.Linq.Expressions": "(, 4.3.0]", "System.Linq.Parallel": "(, 4.3.0]", "System.Linq.Queryable": "(, 4.3.0]", "System.Memory": "(, 4.5.5]", "System.Net.Http": "(, 4.3.4]", "System.Net.Http.Json": "(, 6.0.0]", "System.Net.NameResolution": "(, 4.3.0]", "System.Net.NetworkInformation": "(, 4.3.0]", "System.Net.Ping": "(, 4.3.0]", "System.Net.Primitives": "(, 4.3.1]", "System.Net.Requests": "(, 4.3.0]", "System.Net.Security": "(, 4.3.2]", "System.Net.Sockets": "(, 4.3.0]", "System.Net.WebHeaderCollection": "(, 4.3.0]", "System.Net.WebSockets": "(, 4.3.0]", "System.Net.WebSockets.Client": "(, 4.3.2]", "System.Numerics.Vectors": "(, 4.5.0]", "System.ObjectModel": "(, 4.3.0]", "System.Private.DataContractSerialization": "(, 4.3.0]", "System.Private.Uri": "(, 4.3.0]", "System.Reflection": "(, 4.3.0]", "System.Reflection.DispatchProxy": "(, 4.7.1]", "System.Reflection.Emit": "(, 4.7.0]", "System.Reflection.Emit.ILGeneration": "(, 4.7.0]", "System.Reflection.Emit.Lightweight": "(, 4.7.0]", "System.Reflection.Extensions": "(, 4.3.0]", "System.Reflection.Metadata": "(, 6.0.0]", "System.Reflection.Primitives": "(, 4.3.0]", "System.Reflection.TypeExtensions": "(, 4.7.0]", "System.Resources.Reader": "(, 4.3.0]", "System.Resources.ResourceManager": "(, 4.3.0]", "System.Resources.Writer": "(, 4.3.0]", "System.Runtime": "(, 4.3.1]", "System.Runtime.CompilerServices.Unsafe": "(, 6.0.0]", "System.Runtime.CompilerServices.VisualC": "(, 4.3.0]", "System.Runtime.Extensions": "(, 4.3.1]", "System.Runtime.Handles": "(, 4.3.0]", "System.Runtime.InteropServices": "(, 4.3.0]", "System.Runtime.InteropServices.RuntimeInformation": "(, 4.3.0]", "System.Runtime.InteropServices.WindowsRuntime": "(, 4.3.0]", "System.Runtime.Loader": "(, 4.3.0]", "System.Runtime.Numerics": "(, 4.3.0]", "System.Runtime.Serialization.Formatters": "(, 4.3.0]", "System.Runtime.Serialization.Json": "(, 4.3.0]", "System.Runtime.Serialization.Primitives": "(, 4.3.0]", "System.Runtime.Serialization.Xml": "(, 4.3.0]", "System.Runtime.WindowsRuntime": "(, 4.7.0]", "System.Runtime.WindowsRuntime.UI.Xaml": "(, 4.7.0]", "System.Security.AccessControl": "(, 6.0.0]", "System.Security.Claims": "(, 4.3.0]", "System.Security.Cryptography.Algorithms": "(, 4.3.1]", "System.Security.Cryptography.Cng": "(, 4.6.0]", "System.Security.Cryptography.Csp": "(, 4.3.0]", "System.Security.Cryptography.Encoding": "(, 4.3.0]", "System.Security.Cryptography.OpenSsl": "(, 5.0.0]", "System.Security.Cryptography.Primitives": "(, 4.3.0]", "System.Security.Cryptography.X509Certificates": "(, 4.3.2]", "System.Security.Cryptography.Xml": "(, 4.4.0]", "System.Security.Principal": "(, 4.3.0]", "System.Security.Principal.Windows": "(, 5.0.0]", "System.Security.SecureString": "(, 4.3.0]", "System.Text.Encoding": "(, 4.3.0]", "System.Text.Encoding.CodePages": "(, 6.0.0]", "System.Text.Encoding.Extensions": "(, 4.3.0]", "System.Text.Encodings.Web": "(, 6.0.0]", "System.Text.Json": "(, 6.0.0]", "System.Text.RegularExpressions": "(, 4.3.1]", "System.Threading": "(, 4.3.0]", "System.Threading.Channels": "(, 6.0.0]", "System.Threading.Overlapped": "(, 4.3.0]", "System.Threading.Tasks": "(, 4.3.0]", "System.Threading.Tasks.Dataflow": "(, 6.0.0]", "System.Threading.Tasks.Extensions": "(, 4.5.4]", "System.Threading.Tasks.Parallel": "(, 4.3.0]", "System.Threading.Thread": "(, 4.3.0]", "System.Threading.ThreadPool": "(, 4.3.0]", "System.Threading.Timer": "(, 4.3.0]", "System.ValueTuple": "(, 4.5.0]", "System.Xml.ReaderWriter": "(, 4.3.1]", "System.Xml.XDocument": "(, 4.3.0]", "System.Xml.XmlDocument": "(, 4.3.0]", "System.Xml.XmlSerializer": "(, 4.3.0]", "System.Xml.XPath": "(, 4.3.0]", "System.Xml.XPath.XDocument": "(, 4.3.0]"}}}}}