using System.Threading.Tasks;

namespace NetworkManagement.Services
{
    /// <summary>
    /// خدمة بدء التشغيل السريع
    /// </summary>
    public interface IStartupService
    {
        /// <summary>
        /// تهيئة التطبيق بشكل غير متزامن
        /// </summary>
        Task InitializeAsync();

        /// <summary>
        /// التحقق من جاهزية قاعدة البيانات
        /// </summary>
        Task<bool> IsDatabaseReadyAsync();

        /// <summary>
        /// تحميل البيانات الأساسية
        /// </summary>
        Task PreloadEssentialDataAsync();

        /// <summary>
        /// تحسين الأداء عند البدء
        /// </summary>
        Task OptimizeStartupAsync();
    }
}
