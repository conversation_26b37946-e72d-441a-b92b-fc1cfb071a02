using System;
using System.Windows.Controls;
using NetworkManagement.ViewModels;

namespace NetworkManagement.Views
{
    public partial class DashboardView : UserControl
    {
        public DashboardView()
        {
            InitializeComponent();
            Loaded += DashboardView_Loaded;
        }

        private async void DashboardView_Loaded(object sender, System.Windows.RoutedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("DashboardView_Loaded started");

                var viewModel = App.GetService<DashboardViewModel>();
                System.Diagnostics.Debug.WriteLine($"ViewModel retrieved: {viewModel != null}");

                if (viewModel != null)
                {
                    DataContext = viewModel;
                    System.Diagnostics.Debug.WriteLine("DataContext set");

                    await viewModel.InitializeAsync();
                    System.Diagnostics.Debug.WriteLine("InitializeAsync completed");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("ViewModel is null!");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading dashboard: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }
    }
}
