using System;
using System.Collections.Generic;
using System.Globalization;
using System.Threading.Tasks;
using System.Windows;

namespace NetworkManagement.Services
{
    public class LocalizationService : ILocalizationService
    {
        private readonly ISettingsService _settingsService;
        private string _currentLanguage = "العربية";

        // قاموس الترجمات
        private readonly Dictionary<string, Dictionary<string, string>> _translations = new()
        {
            ["العربية"] = new Dictionary<string, string>
            {
                ["Dashboard"] = "لوحة التحكم",
                ["Devices"] = "الأجهزة",
                ["Sites"] = "المواقع",
                ["Users"] = "المستخدمين",
                ["Tasks"] = "المهام",
                ["Purchases"] = "المشتريات",
                ["Inventory"] = "المخزون",
                ["Reports"] = "التقارير",
                ["Settings"] = "الإعدادات",
                ["NetworkManagement"] = "إدارة الشبكات",
                ["Login"] = "تسجيل الدخول",
                ["Logout"] = "تسجيل الخروج",
                ["Save"] = "حفظ",
                ["Cancel"] = "إلغاء",
                ["Delete"] = "حذف",
                ["Edit"] = "تعديل",
                ["Add"] = "إضافة",
                ["Search"] = "بحث",
                ["Name"] = "الاسم",
                ["Description"] = "الوصف",
                ["Status"] = "الحالة",
                ["Active"] = "نشط",
                ["Inactive"] = "غير نشط"
            },
            ["English"] = new Dictionary<string, string>
            {
                ["Dashboard"] = "Dashboard",
                ["Devices"] = "Devices",
                ["Sites"] = "Sites",
                ["Users"] = "Users",
                ["Tasks"] = "Tasks",
                ["Purchases"] = "Purchases",
                ["Inventory"] = "Inventory",
                ["Reports"] = "Reports",
                ["Settings"] = "Settings",
                ["NetworkManagement"] = "Network Management",
                ["Login"] = "Login",
                ["Logout"] = "Logout",
                ["Save"] = "Save",
                ["Cancel"] = "Cancel",
                ["Delete"] = "Delete",
                ["Edit"] = "Edit",
                ["Add"] = "Add",
                ["Search"] = "Search",
                ["Name"] = "Name",
                ["Description"] = "Description",
                ["Status"] = "Status",
                ["Active"] = "Active",
                ["Inactive"] = "Inactive"
            }
        };

        public LocalizationService(ISettingsService settingsService)
        {
            _settingsService = settingsService;
        }

        public async Task ApplyLanguageAsync(string languageCode)
        {
            try
            {
                _currentLanguage = languageCode;

                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    // تطبيق اتجاه النص
                    if (languageCode == "العربية")
                    {
                        if (Application.Current.MainWindow != null)
                        {
                            Application.Current.MainWindow.FlowDirection = FlowDirection.RightToLeft;
                        }
                        CultureInfo.CurrentUICulture = new CultureInfo("ar-SA");
                    }
                    else
                    {
                        if (Application.Current.MainWindow != null)
                        {
                            Application.Current.MainWindow.FlowDirection = FlowDirection.LeftToRight;
                        }
                        CultureInfo.CurrentUICulture = new CultureInfo("en-US");
                    }
                });

                // حفظ اللغة في الإعدادات
                try
                {
                    var settings = await _settingsService.GetSettingsAsync();
                    var updatedSettings = new
                    {
                        RememberLogin = settings.RememberLogin,
                        DefaultNetwork = settings.DefaultNetwork ?? "",
                        PingTimeout = settings.PingTimeout,
                        ShowNotifications = settings.ShowNotifications,
                        Theme = settings.Theme ?? "Light",
                        Language = _currentLanguage,
                        BackupLocation = settings.BackupLocation ?? "",
                        AutoBackupEnabled = settings.AutoBackupEnabled,
                        AutoBackupDays = settings.AutoBackupDays
                    };

                    await _settingsService.SaveSettingsAsync(updatedSettings);
                }
                catch (Exception settingsEx)
                {
                    System.Diagnostics.Debug.WriteLine($"Error saving language settings: {settingsEx.Message}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error applying language: {ex.Message}");
                throw;
            }
        }

        public string GetCurrentLanguage()
        {
            return _currentLanguage;
        }

        public string[] GetAvailableLanguages()
        {
            return new[] { "العربية", "English" };
        }

        public async Task ApplyDefaultLanguageAsync()
        {
            await ApplyLanguageAsync("العربية");
        }

        public async Task InitializeLanguageAsync()
        {
            try
            {
                var settings = await _settingsService.GetSettingsAsync();
                var savedLanguage = settings.Language ?? "العربية";
                await ApplyLanguageAsync(savedLanguage);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error initializing language: {ex.Message}");
                await ApplyDefaultLanguageAsync();
            }
        }

        public string GetLocalizedString(string key)
        {
            if (_translations.ContainsKey(_currentLanguage) &&
                _translations[_currentLanguage].ContainsKey(key))
            {
                return _translations[_currentLanguage][key];
            }

            // إرجاع المفتاح إذا لم توجد الترجمة
            return key;
        }
    }
}
