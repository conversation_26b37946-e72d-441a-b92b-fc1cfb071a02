using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using NetworkManagement.Models;
using NetworkManagement.Services;

namespace NetworkManagement.Data
{
    public static class DatabaseInitializer
    {
        public static async System.Threading.Tasks.Task InitializeAsync(NetworkDbContext context, IPasswordHashService? passwordHashService = null)
        {
            try
            {
                // لا نستخدم EnsureCreatedAsync لتجنب التضارب مع ملف SQL
                // قاعدة البيانات يتم إنشاؤها عبر DatabaseService

                // إضافة البيانات الأساسية فقط إذا كانت قاعدة البيانات فارغة
                await SeedDataAsync(context, passwordHashService);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Database initialization error: {ex.Message}");
                // لا نرمي الخطأ هنا لتجنب تعطيل التطبيق
            }
        }

        private static async System.Threading.Tasks.Task SeedDataAsync(NetworkDbContext context, IPasswordHashService? passwordHashService)
        {
            // إضافة المستخدم المدير فقط
            if (context.Users != null && !await context.Users.AnyAsync())
            {
                var defaultPassword = "password";
                var hashedPassword = passwordHashService?.HashPassword(defaultPassword) ?? defaultPassword;

                var adminUser = new User
                {
                    Id = "admin",
                    Username = "admin",
                    Password = hashedPassword,
                    Name = "المدير العام",
                    Role = "Admin",
                    NetworkId = null, // Super Admin - can access all networks
                    IsActive = true,
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                };

                context.Users.Add(adminUser);
                await context.SaveChangesAsync();
            }

            // تحديث الأدوار الموجودة لتوحيد التسمية
            if (context.Users != null)
            {
                var usersToUpdate = await context.Users.Where(u =>
                    u.Role == "admin" || u.Role == "manager" || u.Role == "technician" || u.Role == "user" ||
                    u.Role == "ADMIN" || u.Role == "MANAGER" || u.Role == "TECHNICIAN" || u.Role == "USER")
                    .ToListAsync();

                foreach (var user in usersToUpdate)
                {
                    user.Role = user.Role.ToLower() switch
                    {
                        "admin" => "Admin",
                        "manager" => "Manager",
                        "technician" => "Technician",
                        "user" => "User",
                        _ => user.Role
                    };
                    user.UpdatedAt = DateTime.Now;
                }

                if (usersToUpdate.Any())
                {
                    await context.SaveChangesAsync();
                }
            }
        }
    }
}
