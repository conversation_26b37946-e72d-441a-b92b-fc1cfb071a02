using System;
using System.Windows;
using NetworkManagement.ViewModels;

namespace NetworkManagement.Views
{
    public partial class MapSelectionDialog : Window
    {
        public MapSelectionDialogViewModel ViewModel { get; }

        public MapSelectionDialog(double? initialLat = null, double? initialLng = null)
        {
            InitializeComponent();
            ViewModel = new MapSelectionDialogViewModel(initialLat, initialLng);
            DataContext = ViewModel;
            
            ViewModel.LocationSelected += OnLocationSelected;
            ViewModel.DialogClosed += OnDialogClosed;
        }

        private void OnLocationSelected(object? sender, (double Latitude, double Longitude) location)
        {
            DialogResult = true;
            Close();
        }

        private void OnDialogClosed(object? sender, EventArgs e)
        {
            DialogResult = false;
            Close();
        }



        public (double Latitude, double Longitude)? GetSelectedLocation()
        {
            if (ViewModel.SelectedLatitude.HasValue && ViewModel.SelectedLongitude.HasValue)
            {
                return (ViewModel.SelectedLatitude.Value, ViewModel.SelectedLongitude.Value);
            }
            return null;
        }

        protected override void OnClosed(EventArgs e)
        {
            ViewModel.LocationSelected -= OnLocationSelected;
            ViewModel.DialogClosed -= OnDialogClosed;
            base.OnClosed(e);
        }
    }
}
