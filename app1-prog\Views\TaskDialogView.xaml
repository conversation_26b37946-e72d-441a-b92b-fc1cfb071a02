<Window x:Class="NetworkManagement.Views.TaskDialogView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="{Binding Title}"
        Height="600" Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="{DynamicResource PrimaryHueMidBrush}" Padding="20">
            <StackPanel Orientation="Horizontal">
                <materialDesign:PackIcon Kind="ClipboardText" Width="24" Height="24"
                                       Foreground="White" VerticalAlignment="Center"/>
                <TextBlock Text="{Binding Title}"
                          FontSize="18" FontWeight="Medium"
                          Foreground="White" VerticalAlignment="Center" Margin="10,0,0,0"/>
            </StackPanel>
        </Border>

        <!-- Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="20">
                <!-- Description -->
                <TextBox materialDesign:HintAssist.Hint="وصف المهمة *"
                        materialDesign:HintAssist.IsFloating="True"
                        Text="{Binding Description, UpdateSourceTrigger=PropertyChanged}"
                        TextWrapping="Wrap"
                        AcceptsReturn="True"
                        MinLines="2"
                        MaxLines="4"
                        Margin="0,0,0,20"/>

                <!-- Notes -->
                <TextBox materialDesign:HintAssist.Hint="ملاحظات إضافية"
                        materialDesign:HintAssist.IsFloating="True"
                        Text="{Binding Notes, UpdateSourceTrigger=PropertyChanged}"
                        TextWrapping="Wrap"
                        AcceptsReturn="True"
                        MinLines="3"
                        MaxLines="6"
                        Margin="0,0,0,20"/>

                <!-- Status and Priority Row -->
                <Grid Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- Status -->
                    <ComboBox Grid.Column="0"
                             materialDesign:HintAssist.Hint="الحالة"
                             materialDesign:HintAssist.IsFloating="True"
                             ItemsSource="{Binding StatusOptions}"
                             SelectedItem="{Binding SelectedStatus}"/>

                    <!-- Priority -->
                    <ComboBox Grid.Column="2"
                             materialDesign:HintAssist.Hint="الأولوية"
                             materialDesign:HintAssist.IsFloating="True"
                             ItemsSource="{Binding PriorityOptions}"
                             SelectedItem="{Binding SelectedPriority}"/>
                </Grid>

                <!-- Request Date -->
                <DatePicker materialDesign:HintAssist.Hint="تاريخ طلب المهمة *"
                           materialDesign:HintAssist.IsFloating="True"
                           SelectedDate="{Binding RequestDate}"
                           Margin="0,0,0,20"/>

                <!-- Completion Date (only for completed tasks) -->
                <DatePicker materialDesign:HintAssist.Hint="تاريخ إكمال المهمة"
                           materialDesign:HintAssist.IsFloating="True"
                           SelectedDate="{Binding CompletedAt}"
                           Visibility="{Binding IsCompletedTask, Converter={StaticResource BooleanToVisibilityConverter}}"
                           Margin="0,0,0,20"/>

                <!-- Network -->
                <ComboBox materialDesign:HintAssist.Hint="الشبكة (اختياري)"
                         materialDesign:HintAssist.IsFloating="True"
                         ItemsSource="{Binding Networks}"
                         SelectedValuePath="Id"
                         DisplayMemberPath="Name"
                         SelectedValue="{Binding SelectedNetworkId}"
                         Margin="0,0,0,20"/>

                <!-- Required Fields Note -->
                <TextBlock Text="* الحقول المطلوبة"
                          FontSize="12"
                          Foreground="{DynamicResource MaterialDesignBodyLight}"
                          Margin="0,10,0,0"/>
            </StackPanel>
        </ScrollViewer>

        <!-- Buttons -->
        <Border Grid.Row="2" Background="{DynamicResource MaterialDesignCardBackground}"
               BorderBrush="{DynamicResource MaterialDesignDivider}" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Left" Margin="20,15">
                <Button Command="{Binding SaveCommand}"
                       Style="{StaticResource MaterialDesignRaisedButton}"
                       IsEnabled="{Binding IsSaving, Converter={StaticResource InverseBooleanConverter}}"
                       Margin="0,0,10,0">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="ContentSave" Width="16" Height="16" Margin="0,0,5,0"/>
                        <TextBlock Text="حفظ"/>
                    </StackPanel>
                </Button>

                <Button Command="{Binding CancelCommand}"
                       Style="{StaticResource MaterialDesignOutlinedButton}"
                       IsEnabled="{Binding IsSaving, Converter={StaticResource InverseBooleanConverter}}">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Cancel" Width="16" Height="16" Margin="0,0,5,0"/>
                        <TextBlock Text="إلغاء"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </Border>

        <!-- Loading Overlay -->
        <Border Grid.RowSpan="3"
               Background="#80000000"
               Visibility="{Binding IsSaving, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                <ProgressBar Width="50" Height="50"
                           Style="{StaticResource MaterialDesignCircularProgressBar}"
                           IsIndeterminate="True"
                           Foreground="White"/>
                <TextBlock Text="جاري الحفظ..."
                          Foreground="White"
                          FontSize="14"
                          HorizontalAlignment="Center"
                          Margin="0,10,0,0"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
