namespace NetworkManagement.Models
{
    /// <summary>
    /// إحصائيات المهام
    /// </summary>
    public class TaskStatistics
    {
        /// <summary>
        /// إجمالي المهام
        /// </summary>
        public int TotalTasks { get; set; }

        /// <summary>
        /// المهام المعلقة
        /// </summary>
        public int PendingTasks { get; set; }

        /// <summary>
        /// المهام قيد التنفيذ
        /// </summary>
        public int InProgressTasks { get; set; }

        /// <summary>
        /// المهام المكتملة
        /// </summary>
        public int CompletedTasks { get; set; }

        /// <summary>
        /// المهام الملغية
        /// </summary>
        public int CancelledTasks { get; set; }

        /// <summary>
        /// المهام المتأخرة
        /// </summary>
        public int OverdueTasks { get; set; }

        /// <summary>
        /// المهام منخفضة الأولوية
        /// </summary>
        public int LowPriorityTasks { get; set; }

        /// <summary>
        /// المهام متوسطة الأولوية
        /// </summary>
        public int MediumPriorityTasks { get; set; }

        /// <summary>
        /// المهام عالية الأولوية
        /// </summary>
        public int HighPriorityTasks { get; set; }

        /// <summary>
        /// المهام العاجلة
        /// </summary>
        public int UrgentPriorityTasks { get; set; }

        /// <summary>
        /// نسبة الإكمال
        /// </summary>
        public double CompletionRate => TotalTasks > 0 ? (double)CompletedTasks / TotalTasks * 100 : 0;

        /// <summary>
        /// نسبة المهام المتأخرة
        /// </summary>
        public double OverdueRate => TotalTasks > 0 ? (double)OverdueTasks / TotalTasks * 100 : 0;

        /// <summary>
        /// المهام النشطة (معلقة + قيد التنفيذ)
        /// </summary>
        public int ActiveTasks => PendingTasks + InProgressTasks;

        /// <summary>
        /// المهام عالية الأولوية والعاجلة
        /// </summary>
        public int HighUrgentTasks => HighPriorityTasks + UrgentPriorityTasks;
    }
}
