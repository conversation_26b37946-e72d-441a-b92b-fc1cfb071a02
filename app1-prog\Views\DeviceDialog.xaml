<Window x:Class="NetworkManagement.Views.DeviceDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="{Binding Title}"
        Height="600" Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft"
        Background="{DynamicResource MaterialDesignPaper}"
        Foreground="{DynamicResource MaterialDesignBody}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Loading Overlay -->
        <Grid Grid.RowSpan="2"
              Background="{DynamicResource MaterialDesignPaper}"
              Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar IsIndeterminate="True" Width="100" Height="4" Margin="0,0,0,20"/>
                <TextBlock Text="جاري تحميل البيانات..."
                          HorizontalAlignment="Center"
                          Foreground="{DynamicResource MaterialDesignBody}"/>
            </StackPanel>
        </Grid>

        <!-- Main Content -->
        <ScrollViewer Grid.Row="0" VerticalScrollBarVisibility="Auto" Margin="{StaticResource CardPadding}">
            <StackPanel>
                <!-- Responsible -->
                <TextBox materialDesign:HintAssist.Hint="المسؤول *"
                         materialDesign:HintAssist.IsFloating="True"
                         Text="{Binding Responsible, UpdateSourceTrigger=PropertyChanged}"
                         Margin="{StaticResource ElementMargin}"/>

                <!-- Device Type -->
                <ComboBox materialDesign:HintAssist.Hint="نوع الجهاز"
                          materialDesign:HintAssist.IsFloating="True"
                          ItemsSource="{Binding DeviceTypes}"
                          Text="{Binding Type, UpdateSourceTrigger=PropertyChanged}"
                          IsEditable="True"
                          IsTextSearchEnabled="True"
                          StaysOpenOnEdit="True"
                          Margin="{StaticResource ElementMargin}">
                    <ComboBox.ToolTip>
                        <ToolTip Content="يمكنك كتابة نوع جهاز جديد أو اختيار من القائمة المتاحة"/>
                    </ComboBox.ToolTip>
                </ComboBox>

                <!-- IP Address -->
                <TextBox materialDesign:HintAssist.Hint="عنوان IP"
                         materialDesign:HintAssist.IsFloating="True"
                         Text="{Binding IpAddress, UpdateSourceTrigger=PropertyChanged}"
                         Margin="{StaticResource ElementMargin}"/>

                <!-- Location -->
                <TextBox materialDesign:HintAssist.Hint="الموقع"
                         materialDesign:HintAssist.IsFloating="True"
                         Text="{Binding Location, UpdateSourceTrigger=PropertyChanged}"
                         Margin="{StaticResource ElementMargin}"/>

                <!-- Phone -->
                <TextBox materialDesign:HintAssist.Hint="الهاتف"
                         materialDesign:HintAssist.IsFloating="True"
                         Text="{Binding Phone, UpdateSourceTrigger=PropertyChanged}"
                         Margin="{StaticResource ElementMargin}"/>

                <!-- Status -->
                <ComboBox materialDesign:HintAssist.Hint="الحالة"
                          materialDesign:HintAssist.IsFloating="True"
                          ItemsSource="{Binding StatusOptions}"
                          SelectedItem="{Binding Status}"
                          Margin="{StaticResource ElementMargin}"/>

                <!-- Network -->
                <ComboBox materialDesign:HintAssist.Hint="الشبكة (اختياري)"
                         materialDesign:HintAssist.IsFloating="True"
                         ItemsSource="{Binding AvailableNetworks}"
                         SelectedValuePath="Id"
                         DisplayMemberPath="Name"
                         SelectedValue="{Binding SelectedNetworkId}"
                         Margin="{StaticResource ElementMargin}"/>

                <!-- Site -->
                <ComboBox materialDesign:HintAssist.Hint="الموقع المرتبط"
                          materialDesign:HintAssist.IsFloating="True"
                          ItemsSource="{Binding Sites}"
                          SelectedItem="{Binding SelectedSite}"
                          DisplayMemberPath="Name"
                          Margin="{StaticResource ElementMargin}"/>

                <!-- Install Date -->
                <DatePicker materialDesign:HintAssist.Hint="تاريخ التركيب"
                           materialDesign:HintAssist.IsFloating="True"
                           SelectedDate="{Binding InstallDate}"
                           Margin="{StaticResource ElementMargin}"/>

                <!-- Connection Method -->
                <TextBox materialDesign:HintAssist.Hint="طريقة الربط"
                         materialDesign:HintAssist.IsFloating="True"
                         Text="{Binding ConnectionMethod, UpdateSourceTrigger=PropertyChanged}"
                         Margin="{StaticResource ElementMargin}"/>

                <!-- Linked Network -->
                <TextBox materialDesign:HintAssist.Hint="الشبكة المرتبطة"
                         materialDesign:HintAssist.IsFloating="True"
                         Text="{Binding LinkedNetwork, UpdateSourceTrigger=PropertyChanged}"
                         Margin="{StaticResource ElementMargin}"/>

                <!-- Channel -->
                <TextBox materialDesign:HintAssist.Hint="القناة (أرقام فقط)"
                         materialDesign:HintAssist.IsFloating="True"
                         Text="{Binding Channel, UpdateSourceTrigger=PropertyChanged}"
                         PreviewTextInput="NumericTextBox_PreviewTextInput"
                         PreviewKeyDown="NumericTextBox_PreviewKeyDown"
                         Margin="{StaticResource ElementMargin}"/>

                <!-- Connected Devices -->
                <TextBox materialDesign:HintAssist.Hint="عدد الأجهزة المتصلة (أرقام فقط)"
                         materialDesign:HintAssist.IsFloating="True"
                         Text="{Binding ConnectedDevices, UpdateSourceTrigger=PropertyChanged}"
                         PreviewTextInput="NumericTextBox_PreviewTextInput"
                         PreviewKeyDown="NumericTextBox_PreviewKeyDown"
                         Margin="{StaticResource ElementMargin}"/>

                <!-- Installed Adapter Type -->
                <ComboBox materialDesign:HintAssist.Hint="نوع المحول المركب"
                          materialDesign:HintAssist.IsFloating="True"
                          ItemsSource="{Binding AdapterTypes}"
                          Text="{Binding InstalledAdapterType, UpdateSourceTrigger=PropertyChanged}"
                          IsEditable="True"
                          IsTextSearchEnabled="True"
                          StaysOpenOnEdit="True"
                          Margin="{StaticResource ElementMargin}">
                    <ComboBox.ToolTip>
                        <ToolTip Content="يمكنك كتابة نوع محول جديد أو اختيار من المخزون المتاح"/>
                    </ComboBox.ToolTip>
                </ComboBox>

                <!-- Network Cable Type -->
                <ComboBox materialDesign:HintAssist.Hint="نوع سلك الشبكة المستخدم"
                          materialDesign:HintAssist.IsFloating="True"
                          ItemsSource="{Binding NetworkCableTypes}"
                          Text="{Binding NetworkCableType, UpdateSourceTrigger=PropertyChanged}"
                          IsEditable="True"
                          IsTextSearchEnabled="True"
                          StaysOpenOnEdit="True"
                          Margin="{StaticResource ElementMargin}">
                    <ComboBox.ToolTip>
                        <ToolTip Content="يمكنك كتابة نوع سلك جديد أو اختيار من المخزون المتاح"/>
                    </ComboBox.ToolTip>
                </ComboBox>

                <!-- Network Cable Length -->
                <TextBox materialDesign:HintAssist.Hint="طول سلك الشبكة (متر - أرقام فقط)"
                         materialDesign:HintAssist.IsFloating="True"
                         Text="{Binding NetworkCableLength, UpdateSourceTrigger=PropertyChanged}"
                         PreviewTextInput="NumericTextBox_PreviewTextInput"
                         PreviewKeyDown="NumericTextBox_PreviewKeyDown"
                         Margin="{StaticResource ElementMargin}"/>

                <!-- Power Cable Type -->
                <ComboBox materialDesign:HintAssist.Hint="نوع سلك الكهرباء المستخدم"
                          materialDesign:HintAssist.IsFloating="True"
                          ItemsSource="{Binding PowerCableTypes}"
                          Text="{Binding PowerCableType, UpdateSourceTrigger=PropertyChanged}"
                          IsEditable="True"
                          IsTextSearchEnabled="True"
                          StaysOpenOnEdit="True"
                          Margin="{StaticResource ElementMargin}">
                    <ComboBox.ToolTip>
                        <ToolTip Content="يمكنك كتابة نوع سلك جديد أو اختيار من المخزون المتاح"/>
                    </ComboBox.ToolTip>
                </ComboBox>

                <!-- Power Cable Length -->
                <TextBox materialDesign:HintAssist.Hint="طول سلك الكهرباء (متر - أرقام فقط)"
                         materialDesign:HintAssist.IsFloating="True"
                         Text="{Binding PowerCableLength, UpdateSourceTrigger=PropertyChanged}"
                         PreviewTextInput="NumericTextBox_PreviewTextInput"
                         PreviewKeyDown="NumericTextBox_PreviewKeyDown"
                         Margin="{StaticResource ElementMargin}"/>

                <!-- Inventory Status Message -->
                <Border Background="{DynamicResource MaterialDesignCardBackground}"
                        BorderBrush="{DynamicResource MaterialDesignDivider}"
                        BorderThickness="1"
                        CornerRadius="4"
                        Padding="10"
                        Margin="{StaticResource SpacingS}"
                        Visibility="{Binding InventoryStatusMessage, Converter={StaticResource StringToVisibilityConverter}}">
                    <TextBlock Text="{Binding InventoryStatusMessage}"
                               Foreground="{DynamicResource MaterialDesignBody}"
                               FontSize="12"
                               TextWrapping="Wrap"/>
                </Border>

                <!-- Error Message -->
                <TextBlock Text="{Binding ErrorMessage}"
                           Foreground="{StaticResource StatusDisconnectedBrush}"
                           FontWeight="Medium"
                           Margin="{StaticResource SpacingS}"
                           Visibility="{Binding ErrorMessage, Converter={StaticResource StringToVisibilityConverter}}"/>

                <!-- Required Fields Note -->
                <TextBlock Text="* الحقول المطلوبة"
                           Style="{StaticResource CaptionTextStyle}"
                           Foreground="{DynamicResource MaterialDesignBodyLight}"
                           Margin="{StaticResource SpacingS}"/>
            </StackPanel>
        </ScrollViewer>

        <!-- Action Buttons -->
        <Border Grid.Row="1"
                Background="{DynamicResource MaterialDesignPaper}"
                BorderBrush="{DynamicResource MaterialDesignDivider}"
                BorderThickness="0,1,0,0"
                Padding="{StaticResource CardPadding}">

            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Loading Indicator -->
                <ProgressBar Grid.Column="0"
                             IsIndeterminate="True"
                             Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                             VerticalAlignment="Center"
                             Height="4"
                             Margin="{StaticResource CardPadding}"/>

                <!-- Cancel Button -->
                <Button Grid.Column="1"
                        Content="إلغاء"
                        Command="{Binding CancelCommand}"
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        Margin="{StaticResource SpacingS}"
                        MinWidth="80"/>

                <!-- Save Button -->
                <Button Grid.Column="2"
                        Content="{Binding IsEditMode, Converter={StaticResource BooleanToStringConverter}, ConverterParameter='تحديث|حفظ'}"
                        Command="{Binding SaveCommand}"
                        Style="{StaticResource MaterialDesignRaisedButton}"
                        IsEnabled="{Binding IsLoading, Converter={StaticResource InverseBooleanConverter}}"
                        MinWidth="80"/>
            </Grid>
        </Border>
    </Grid>
</Window>
