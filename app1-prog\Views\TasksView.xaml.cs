using System;
using System.Windows.Controls;
using Microsoft.Extensions.DependencyInjection;
using NetworkManagement.ViewModels;

namespace NetworkManagement.Views
{
    public partial class TasksView : UserControl, IDisposable
    {
        private IServiceScope? _scope;
        private TasksViewModel? _viewModel;
        private bool _isDisposed = false;
        private bool _isInitialized = false;
        private readonly object _lockObject = new object();

        public TasksView()
        {
            InitializeComponent();
            Loaded += TasksView_Loaded;
            Unloaded += TasksView_Unloaded;
        }

        private async void TasksView_Loaded(object sender, System.Windows.RoutedEventArgs e)
        {
            // تجنب التحميل المتعدد والتحقق من التخلص
            lock (_lockObject)
            {
                if (_isDisposed) return;
            }

            try
            {
                System.Diagnostics.Debug.WriteLine("TasksView_Loaded: بدء تحميل صفحة المهام");

                lock (_lockObject)
                {
                    // تنظيف الموارد السابقة إذا كانت موجودة
                    if (_scope != null || _viewModel != null)
                    {
                        System.Diagnostics.Debug.WriteLine("TasksView_Loaded: تنظيف الموارد السابقة");
                        CleanupResourcesInternal();
                    }

                    // إنشاء موارد جديدة
                    _scope = App.CreateScope();
                    _viewModel = _scope.ServiceProvider.GetRequiredService<TasksViewModel>();
                    DataContext = _viewModel;
                }

                System.Diagnostics.Debug.WriteLine("TasksView_Loaded: تم إنشاء TasksViewModel");

                // التهيئة خارج القفل لتجنب deadlock
                if (!_isInitialized)
                {
                    await _viewModel.InitializeAsync();
                    lock (_lockObject)
                    {
                        _isInitialized = true;
                    }
                    System.Diagnostics.Debug.WriteLine("TasksView_Loaded: تم تحميل الصفحة بنجاح");
                }
                else
                {
                    // إذا كان مُهيأ من قبل، قم بتحديث البيانات فقط
                    System.Diagnostics.Debug.WriteLine("TasksView_Loaded: تحديث البيانات الموجودة");
                    await _viewModel.InitializeAsync();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"TasksView_Loaded: خطأ - {ex.Message}");
                System.Windows.MessageBox.Show(
                    $"حدث خطأ أثناء تحميل المهام:\n{ex.Message}",
                    "خطأ في تحميل المهام",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Error);
            }
        }

        private void TasksView_Unloaded(object sender, System.Windows.RoutedEventArgs e)
        {
            Dispose();
        }

        /// <summary>
        /// تنظيف الموارد الداخلي (يجب استدعاؤه داخل lock)
        /// </summary>
        private void CleanupResourcesInternal()
        {
            try
            {
                if (_viewModel != null)
                {
                    _viewModel.Dispose();
                    _viewModel = null;
                }

                if (_scope != null)
                {
                    _scope.Dispose();
                    _scope = null;
                }

                // تنظيف DataContext على UI thread
                if (System.Windows.Application.Current?.Dispatcher?.CheckAccess() == true)
                {
                    DataContext = null;
                }
                else
                {
                    System.Windows.Application.Current?.Dispatcher?.Invoke(() => DataContext = null);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"TasksView: خطأ في تنظيف الموارد الداخلي - {ex.Message}");
            }
        }

        public void Dispose()
        {
            lock (_lockObject)
            {
                if (_isDisposed) return;
                _isDisposed = true;
            }

            try
            {
                System.Diagnostics.Debug.WriteLine("TasksView: بدء تنظيف الموارد");

                // إلغاء الاشتراك في الأحداث
                Loaded -= TasksView_Loaded;
                Unloaded -= TasksView_Unloaded;

                CleanupResourcesInternal();

                System.Diagnostics.Debug.WriteLine("TasksView: تم تنظيف الموارد بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"TasksView: خطأ في تنظيف الموارد - {ex.Message}");
            }

            GC.SuppressFinalize(this);
        }

        private void ExportMenuButton_Click(object sender, System.Windows.RoutedEventArgs e)
        {
            if (sender is System.Windows.Controls.Button button && button.ContextMenu != null)
            {
                button.ContextMenu.PlacementTarget = button;
                button.ContextMenu.IsOpen = true;
            }
        }

        private void MoreActionsButton_Click(object sender, System.Windows.RoutedEventArgs e)
        {
            if (sender is System.Windows.Controls.Button button && button.ContextMenu != null)
            {
                button.ContextMenu.PlacementTarget = button;
                button.ContextMenu.IsOpen = true;
            }
        }


    }
}
