# برومت مفصل للنافذة الرئيسية - MainWindow

## وصف النافذة العام
النافذة الرئيسية هي قلب نظام Shabaka Pro، تحتوي على قائمة التنقل الجانبية، منطقة المحتوى الرئيسي، شريط الحالة، ونظام الإشعارات. يجب أن تكون سريعة الاستجابة ومتوافقة مع جميع أحجام الشاشات.

## المتطلبات التقنية
- **النوع**: Window (النافذة الرئيسية للتطبيق)
- **الحجم الافتراضي**: 1400x900 بكسل
- **الحد الأدنى للحجم**: 1200x700 بكسل
- **حالة النافذة**: Maximized عند الفتح الأول
- **الثيم**: دعم التبديل بين الفاتح والداكن
- **اللغة**: عربية كاملة مع تخطيط RTL

## التصميم المرئي

### الألوان الأساسية:
- **الخلفية الرئيسية**: أبيض (#FAFAFA) للثيم الفاتح، رمادي داكن (#303030) للثيم الداكن
- **لون القائمة الجانبية**: أبيض (#FFFFFF) للثيم الفاتح، رمادي داكن (#212121) للثيم الداكن
- **اللون الأساسي**: أزرق Material Design (#2196F3)
- **لون التحديد**: أزرق فاتح (#E3F2FD)
- **لون الحدود**: رمادي فاتح (#E0E0E0)

### الخطوط:
- **خط القوائم**: Segoe UI، 14px، وزن Medium
- **خط المحتوى**: Tahoma، 13px، وزن Normal
- **خط العناوين**: Segoe UI، 18px، وزن Bold

## هيكل النافذة

### 1. شريط العنوان المخصص (Custom Title Bar):
#### أ. منطقة الشعار والعنوان:
- **شعار التطبيق**: 32x32 بكسل، Resources/shabaka-pro.ico
- **عنوان التطبيق**: "Shabaka Pro - نظام إدارة الشبكات"
- **اسم المستخدم الحالي**: عرض اسم ودور المستخدم
- **اسم الشبكة الحالية**: عرض الشبكة النشطة

#### ب. أزرار التحكم:
- **زر الإشعارات**: 
  - أيقونة: Bell icon
  - عداد الإشعارات غير المقروءة
  - لون أحمر للعداد إذا كان أكبر من 0
- **زر إعدادات قاعدة البيانات**:
  - أيقونة: Database icon
  - فتح نافذة إعدادات قاعدة البيانات
- **زر تبديل الثيم**:
  - أيقونة: LightMode/DarkMode icon
  - تبديل فوري بين الثيم الفاتح والداكن
- **زر تسجيل الخروج**:
  - أيقونة: Logout icon
  - رسالة تأكيد قبل الخروج
- **أزرار النافذة**: تصغير، تكبير، إغلاق

### 2. القائمة الجانبية (Navigation Sidebar):
#### أ. المواصفات العامة:
- **العرض**: 280 بكسل (قابل للطي إلى 60 بكسل)
- **الموضع**: يمين النافذة (RTL)
- **الخلفية**: لون منفصل عن المحتوى الرئيسي
- **الحدود**: حد رفيع على الجانب الأيسر

#### ب. عناصر القائمة:
1. **لوحة التحكم**:
   - أيقونة: Dashboard icon
   - النص: "لوحة التحكم"
   - الصفحة: DashboardView

2. **إدارة الشبكات** (للـ Super Admin فقط):
   - أيقونة: Network icon
   - النص: "إدارة الشبكات"
   - الصفحة: NetworkManagementView

3. **إدارة المستخدمين**:
   - أيقونة: People icon
   - النص: "إدارة المستخدمين"
   - الصفحة: UsersView
   - الصلاحية: Admin, Manager

4. **إدارة الأجهزة**:
   - أيقونة: Computer icon
   - النص: "إدارة الأجهزة"
   - الصفحة: DevicesView

5. **إدارة المواقع**:
   - أيقونة: LocationOn icon
   - النص: "إدارة المواقع"
   - الصفحة: SitesView

6. **إدارة المخزون**:
   - أيقونة: Inventory icon
   - النص: "إدارة المخزون"
   - الصفحة: InventoryView

7. **إدارة المشتريات**:
   - أيقونة: ShoppingCart icon
   - النص: "إدارة المشتريات"
   - الصفحة: PurchasesView

8. **إدارة المهام**:
   - أيقونة: Task icon
   - النص: "مهامي"
   - الصفحة: TasksView

9. **تقييم المستخدمين** (للمديرين):
   - أيقونة: Star icon
   - النص: "تقييم المستخدمين"
   - الصفحة: UserEvaluationView
   - الصلاحية: Admin, Manager

10. **تقييماتي** (للفنيين):
    - أيقونة: StarBorder icon
    - النص: "تقييماتي"
    - الصفحة: MyEvaluationView
    - الصلاحية: Technician

11. **التقارير**:
    - أيقونة: Assessment icon
    - النص: "التقارير"
    - الصفحة: ReportsView

12. **الإعدادات**:
    - أيقونة: Settings icon
    - النص: "الإعدادات"
    - الصفحة: SettingsView

#### ج. تفاعلات القائمة:
- **التحديد**: تمييز العنصر المحدد بلون مختلف
- **التمرير**: تأثير hover خفيف
- **الطي**: زر لطي/فتح القائمة
- **الأيقونات فقط**: عند الطي، عرض الأيقونات فقط مع tooltips

### 3. منطقة المحتوى الرئيسي (Main Content Area):
#### أ. المواصفات:
- **الموضع**: يسار القائمة الجانبية
- **الخلفية**: خلفية التطبيق الرئيسية
- **الحشو**: 20 بكسل من جميع الجهات
- **المحتوى**: Frame لعرض الصفحات المختلفة

#### ب. شريط التنقل العلوي (Breadcrumb):
- **الموضع**: أعلى منطقة المحتوى
- **المحتوى**: مسار التنقل الحالي
- **مثال**: "الرئيسية > إدارة الأجهزة > إضافة جهاز جديد"
- **التفاعل**: روابط قابلة للنقر للعودة للصفحات السابقة

### 4. شريط الحالة (Status Bar):
#### أ. المعلومات المعروضة:
- **حالة الاتصال بقاعدة البيانات**:
  - أيقونة: Database icon
  - النص: "متصل" (أخضر) أو "غير متصل" (أحمر)
- **عدد الأجهزة النشطة**:
  - أيقونة: Computer icon
  - النص: "الأجهزة النشطة: 25/30"
- **آخر تحديث للبيانات**:
  - أيقونة: Refresh icon
  - النص: "آخر تحديث: 14:30:25"
- **المستخدم الحالي**:
  - أيقونة: Person icon
  - النص: "أحمد محمد - مدير الشبكة"

#### ب. أزرار الإجراءات السريعة:
- **تحديث البيانات**: زر تحديث فوري
- **نسخ احتياطي سريع**: زر للنسخ الاحتياطي
- **حالة المزامنة**: مؤشر حالة المزامنة

### 5. نظام الإشعارات (Notification System):
#### أ. منطقة الإشعارات:
- **الموضع**: أعلى يمين النافذة (منطقة منبثقة)
- **العرض الأقصى**: 350 بكسل
- **عدد الإشعارات المعروضة**: آخر 5 إشعارات
- **التمرير**: إمكانية التمرير لرؤية المزيد

#### ب. أنواع الإشعارات:
1. **إشعار النجاح**:
   - اللون: أخضر (#4CAF50)
   - الأيقونة: CheckCircle icon
   - مثال: "تم إضافة الجهاز بنجاح"

2. **إشعار التحذير**:
   - اللون: برتقالي (#FF9800)
   - الأيقونة: Warning icon
   - مثال: "المخزون منخفض للمادة X"

3. **إشعار الخطأ**:
   - اللون: أحمر (#F44336)
   - الأيقونة: Error icon
   - مثال: "فشل في الاتصال بالجهاز"

4. **إشعار المعلومات**:
   - اللون: أزرق (#2196F3)
   - الأيقونة: Info icon
   - مثال: "تم تحديث البيانات"

#### ج. تفاعلات الإشعارات:
- **الإغلاق التلقائي**: بعد 5 ثوانٍ للنجاح والمعلومات، 10 ثوانٍ للتحذير والخطأ
- **الإغلاق اليدوي**: زر X لكل إشعار
- **النقر**: إجراء مرتبط بالإشعار (اختياري)
- **الأصوات**: صوت مختلف لكل نوع إشعار

## الوظائف المطلوبة

### 1. إدارة التنقل:
```csharp
- تحميل الصفحة المطلوبة في منطقة المحتوى
- تحديث شريط التنقل (Breadcrumb)
- حفظ آخر صفحة مفتوحة
- التحقق من الصلاحيات قبل فتح الصفحة
```

### 2. إدارة الصلاحيات:
```csharp
- إخفاء/إظهار عناصر القائمة حسب دور المستخدم
- فحص الصلاحيات عند كل تنقل
- عرض رسالة خطأ عند محاولة الوصول لصفحة غير مسموحة
```

### 3. إدارة الثيم:
```csharp
- تبديل فوري بين الثيم الفاتح والداكن
- حفظ تفضيل المستخدم
- تطبيق الثيم على جميع العناصر
```

### 4. إدارة الإشعارات:
```csharp
- استقبال الإشعارات من الخدمات المختلفة
- عرض الإشعارات بالتنسيق المناسب
- إدارة قائمة انتظار الإشعارات
- حفظ سجل الإشعارات
```

### 5. مراقبة حالة النظام:
```csharp
- فحص دوري لحالة قاعدة البيانات
- تحديث إحصائيات شريط الحالة
- مراقبة أداء التطبيق
```

## التفاعلات والأحداث

### 1. أحداث لوحة المفاتيح:
- **Ctrl+1 إلى Ctrl+9**: التنقل السريع للصفحات
- **F5**: تحديث البيانات
- **F11**: تبديل وضع ملء الشاشة
- **Alt+F4**: إغلاق التطبيق مع تأكيد

### 2. أحداث الماوس:
- **النقر على عناصر القائمة**: التنقل للصفحة
- **النقر المزدوج على شريط العنوان**: تكبير/تصغير النافذة
- **السحب**: تحريك النافذة

### 3. أحداث النافذة:
- **تحميل النافذة**: استعادة آخر حالة محفوظة
- **إغلاق النافذة**: حفظ الحالة الحالية
- **تغيير الحجم**: إعادة ترتيب العناصر

## الأمان والصلاحيات

### 1. فحص الصلاحيات:
```csharp
- فحص دور المستخدم عند تحميل النافذة
- إخفاء العناصر غير المسموحة
- منع الوصول المباشر للصفحات المحظورة
```

### 2. إدارة الجلسة:
```csharp
- مراقبة انتهاء صلاحية الجلسة
- تسجيل خروج تلقائي عند انتهاء الجلسة
- حفظ العمل الحالي قبل الخروج
```

## الأداء والتحسين

### 1. تحميل الصفحات:
- تحميل كسول للصفحات (Lazy Loading)
- إعادة استخدام الصفحات المحملة
- تنظيف الذاكرة للصفحات غير المستخدمة

### 2. تحديث البيانات:
- تحديث دوري للإحصائيات
- تحديث فوري عند تغيير البيانات
- تخزين مؤقت للبيانات المتكررة

### 3. استجابة الواجهة:
- عمليات غير متزامنة للمهام الطويلة
- مؤشرات تحميل للعمليات البطيئة
- منع تجميد الواجهة

هذا البرومت المفصل يغطي جميع جوانب النافذة الرئيسية لضمان إنشاء واجهة احترافية ومتكاملة.
