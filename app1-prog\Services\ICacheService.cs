using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace NetworkManagement.Services
{
    /// <summary>
    /// خدمة التخزين المؤقت لتحسين الأداء
    /// </summary>
    public interface ICacheService
    {
        /// <summary>
        /// الحصول على قيمة من الذاكرة المؤقتة
        /// </summary>
        T? Get<T>(string key);

        /// <summary>
        /// حفظ قيمة في الذاكرة المؤقتة
        /// </summary>
        void Set<T>(string key, T value, TimeSpan? expiration = null);

        /// <summary>
        /// الحصول على قيمة أو إنشاؤها إذا لم تكن موجودة
        /// </summary>
        Task<T> GetOrCreateAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiration = null);

        /// <summary>
        /// حذف قيمة من الذاكرة المؤقتة
        /// </summary>
        void Remove(string key);

        /// <summary>
        /// حذف جميع القيم التي تبدأ بمفتاح معين
        /// </summary>
        void RemoveByPattern(string pattern);

        /// <summary>
        /// مسح جميع البيانات المؤقتة
        /// </summary>
        void Clear();

        /// <summary>
        /// التحقق من وجود مفتاح
        /// </summary>
        bool Exists(string key);
    }
}
