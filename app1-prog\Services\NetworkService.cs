using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using NetworkManagement.Data;
using NetworkManagement.Models;
using Task = System.Threading.Tasks.Task;

namespace NetworkManagement.Services
{
    public class NetworkService : INetworkService
    {
        private readonly NetworkDbContext _context;

        public NetworkService(NetworkDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Network>> GetAllAsync()
        {
            if (_context.Networks == null)
                return Array.Empty<Network>();

            return await _context.Networks
                .Include(n => n.Users)
                .Include(n => n.Sites)
                .Include(n => n.Devices)
                .OrderBy(n => n.Name)
                .ToListAsync();
        }

        public async Task<Network?> GetByIdAsync(string id)
        {
            if (_context.Networks == null)
                return null;

            return await _context.Networks
                .Include(n => n.Users)
                .Include(n => n.Sites)
                .Include(n => n.Devices)
                .FirstOrDefaultAsync(n => n.Id == id);
        }

        public async Task<Network> CreateAsync(Network network)
        {
            if (_context.Networks == null)
                throw new InvalidOperationException("DbSet<Network> is null");

            try
            {
                // Ensure database and tables exist
                await _context.Database.EnsureCreatedAsync();

                network.Id = string.IsNullOrEmpty(network.Id) ? Guid.NewGuid().ToString() : network.Id;
                network.CreatedAt = DateTime.Now;
                network.UpdatedAt = DateTime.Now;

                _context.Networks.Add(network);
                await _context.SaveChangesAsync();
                return network;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating network: {ex}");
                throw new InvalidOperationException($"Failed to create network: {ex.Message}", ex);
            }
        }

        public async Task<Network> UpdateAsync(Network network)
        {
            if (_context.Networks == null)
                throw new InvalidOperationException("DbSet<Network> is null");

            network.UpdatedAt = DateTime.Now;
            _context.Networks.Update(network);
            await _context.SaveChangesAsync();
            return network;
        }

        public async Task DeleteAsync(string id)
        {
            if (_context.Networks == null)
                throw new InvalidOperationException("DbSet<Network> is null");

            var network = await _context.Networks.FindAsync(id);
            if (network != null)
            {
                _context.Networks.Remove(network);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<bool> ExistsAsync(string id)
        {
            if (_context.Networks == null)
                return false;

            return await _context.Networks.AnyAsync(n => n.Id == id);
        }

        public async Task<IEnumerable<Network>> GetActiveNetworksAsync()
        {
            if (_context.Networks == null)
                return Array.Empty<Network>();

            return await _context.Networks
                .Include(n => n.Users)
                .Include(n => n.Sites)
                .Include(n => n.Devices)
                .Where(n => n.IsActive)
                .OrderBy(n => n.Name)
                .ToListAsync();
        }
    }
}
